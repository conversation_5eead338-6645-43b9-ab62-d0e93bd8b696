<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <title>Dashboard</title>
    <script src="../config.js"></script>
</head>

<body>

    <script src="https://unpkg.com/@superset-ui/embedded-sdk"></script>
    <div id="dashboard" style="height: 100vh; width: 100%;"></div>
    <script>

        let ACCESS_TOKEN, DASHBOARD_ID;

        // document.getElementById("logout").addEventListener('click', () => {
        //     fetch(`${config.apiUrl}/api/v2/logout`, {
        //         method: "POST",
        //         headers: { "Content-Type": "application/json" },
        //         body: JSON.stringify({ refresh_token: localStorage.getItem("dash_refresh_token") })
        //     })
        //         .then(res => {
        //             if (!res.ok) {
        //                 throw new Error(`Logout failed: ${res.status}`);
        //             }
        //             return res.json(); // Parse the response as JSON
        //         })
        //         .then(data => {
        //             console.log("Logout successful:", data);
        //             // Clear tokens from localStorage
        //             localStorage.clear();

        //             window.location.href = "/dashboard/login.html";
        //         })
        //         .catch(error => {
        //             console.error("Error during logout:", error);
        //             alert("Failed to logout. Please try again.");
        //         });

        // });
        async function embedDashboard() {
            try {
                const guest_token = localStorage.getItem("guest_token");
                if (guest_token == null || guest_token == "") {
                    alert("Can't Find Guest Token");
                    return;
                }
                supersetEmbeddedSdk.embedDashboard({
                    id: DASHBOARD_ID,
                    supersetDomain: config.super_set,
                    mountPoint: document.getElementById("dashboard"),
                    fetchGuestToken: () => guest_token,
                    dashboardUiConfig: {
                        hideTitle: true,
                        filters: {
                            expanded: true
                        },
                        urlParams: {
                            standalone: 1
                        }
                    },
                    iframeSandboxExtras: ['allow-top-navigation', 'allow-popups-to-escape-sandbox']
                });
            } catch (error) {
                alert("An Error Occured");
                console.error("Error embedding dashboard:", error);
            }
            const iframe = document.querySelector("#dashboard iframe");
            iframe.style.width = "100%";
            iframe.style.height = "100%";
        }
        async function fetchGuestTokenFromBackend() {
            try {
                const queryParams = new URLSearchParams(window.location.search);
                ACCESS_TOKEN = queryParams.get("access_token");
                DASHBOARD_ID = queryParams.get("dashboard_id");
                // let dash_access_token = localStorage.getItem("access_token");
                if (ACCESS_TOKEN == null || ACCESS_TOKEN == "") {
                    alert("access token is missing");
                    return;
                }

                const response = await fetch(`${config.apiUrl}/dashboard/token`, {
                    method: 'POST',
                    headers: { "Content-Type": "application/json", "Authorization": `Bearer ${ACCESS_TOKEN}` },
                });

                if (!response.ok) {
                    throw new Error(`Error fetching guest token: ${response.status}`);
                }

                const guestTokenData = await response.json();
                localStorage.setItem("guest_token", guestTokenData.result.guest_token);

                embedDashboard();

            } catch (error) {
                console.error("Error fetching guest token:", error);
                alert("An error occurred. Please try again.");
            }
        }
        fetchGuestTokenFromBackend();
    </script>
</body>

</html>