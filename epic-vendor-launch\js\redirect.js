let fhirData = null;
const vendor_redirect_url = `${window.location.origin}/epic-vendor-launch/redirect.html`;
const fieldList = [
  "patient",
  "documentreference",
  "encounter",
  "procedure",
  "condition",
  "diagnosticreport",
  "explanationbenefit",
  "careplan",
  "allergyintolerance",
  "medicationrequest",
  "device",
  "observation",
  "immunization",
];


getAccessToken();
// Generalized function for API requests
async function apiRequest(
  endpoint,
  method = "GET",
  body = null,
  requiresAuth = true,
  apiUrls = true
) {
  let url;
  if (apiUrls) {
    url = new URL(config.apiUrl + endpoint);
  }

  let headers = {
    "Content-Type": "application/json",
  };

  // Include the access token if required
  if (requiresAuth) {
    const access_token = localStorage.getItem("access_token");
    headers["Authorization"] = `Bearer ${access_token}`;
  }

  try {
    console.log("url", url);
    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : null,
    });
    let text = await response.json();
    console.log("response", response.text());
    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage =
        errorData.detail?.error_description ||
        errorData.errorMessage ||
        "Request failed";
      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    if (error.message.includes("token")) {
      localStorage.clear();
      // window.location.href = "/login.html";
      alert("somethink went wrong");
    } else {
      console.error("API error:", error);
      throw error;
    }
  }
}
async function getAccessToken() {
  const queryParams = new URLSearchParams(window.location.search);
  const code = queryParams.get("code");
  if (code) {
    let access_token_global;
    fetch(`${config.apiUrl}/code?redirect_url=${vendor_redirect_url}&code=${code}`)
      // Converting received data to JSON
      .then(async (response) => {
        if (!response.ok) {
          throw Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
        }
        return await response.json();
      })
      .then((json) => {
        console.log("json", json.result);
        result = json.result;
        const { refresh_token, access_token } = result;
        localStorage.setItem("refresh_token", refresh_token);
        localStorage.setItem("access_token", access_token);
        const decodedToken = jwt_decode(access_token);
        if (
          decodedToken.realm_roles &&
          decodedToken.realm_roles.includes("CLINICAL_COORDINATOR")
        ) {
          access_token_global = access_token;
          patient_data = localStorage.getItem("patientData");
          patient_data = JSON.parse(patient_data);
          for (const name of patient_data["name"]) {
            if (name.use == "official") {
              patientName = `${name.given.join(" ")} ${name.family}`;
              first_name = `${name.given.join(" ")}`;
              last_name = name.family;
            }
          }
          gender = patient_data.gender == "female" ? "F" : "M";
          payload = {
            first_name: first_name,
            last_name: last_name,
            dob: patient_data.birthDate,
            sex: gender,
            mrn: "1234",
            procedure_type_id: "6756d856a403e4d2cde949e0",
            access_token: localStorage.getItem("epic_access_token"),
            patient_id: localStorage.getItem("epic_patient_id"),
            server_base_url: localStorage.getItem("epic_server_url"),
          };

          fetch(`${config.apiUrl}/users/profile`, {
            method: "GET",
            headers: {
              "Content-type": "application/json; charset=UTF-8",
              Authorization: `Bearer ${access_token}`,
            },
          })
            .then(async (response) => {
              if (!response.ok) {
                throw Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
              }
              return await response.json();
            })
            .then((json) => {
              const confirmation = confirm(
                `You belong to ${json.result.org.name}.  Are you confirmed to create a patient?`
              );
              if (!confirmation) {
                alert("Action cancelled by user..");
                document
                  .getElementById("loader-container")
                  .classList.add("hidden");
                document.getElementById("loader-text").innerText =
                  "Action cancelled by the user..";
                return;
              } else {
                site_id = json.result.org.id;
                fetch(`${config.apiUrl}/sites/${site_id}/patient/import`, {
                  method: "POST",
                  body: JSON.stringify(payload),
                  headers: {
                    "Content-type": "application/json; charset=UTF-8",
                    Authorization: `Bearer ${access_token}`,
                  },
                })
                  .then(async (response) => {
                    if (!response.ok) {
                      throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
                    }
                    return await response.json();
                  })
                  .then((json) => {
                    console.log(json);
                    alert("Patient created Successfully");
                    let res_data = json?.result || {};
                    localStorage.setItem("atriai_patient_id", res_data?.patient_id);
                    fieldList.forEach((element) => {
                      let textArea = document.getElementById(element);
                      if (textArea) {
                        textArea.value = JSON.stringify(res_data?.[element], null, 2);
                      }
                    });
                    document
                      .getElementById("loader-container")
                      .classList.add("hidden");
                    document.getElementById("loader-text").innerText =
                      "Success..";
                    // localStorage.clear();
                  })
                  .catch((error) => {
                    alert(error.message);
                    document
                      .getElementById("loader-container")
                      .classList.add("hidden");
                    document.getElementById("loader-text").innerText =
                      "Failed to refer..";
                    console.error("Fetch error:", error.message);
                  });
              }
            })
            .catch((error) => {
              alert(error.message);
              document
                .getElementById("loader-container")
                .classList.add("hidden");
              document.getElementById("loader-text").innerText =
                "Failed to refer..";
              console.error("Fetch error:", error.message);
            });
        } else {
          alert(
            "You are not logged in as CLINICAL COORDINATOR. Please contact admin"
          );
          document.getElementById("loader-container").classList.add("hidden");
          document.getElementById("loader-text").innerText =
            "Failed to refer..";
        }
      })
      .catch((error) => {
        alert(error.message);
        document.getElementById("loader-container").classList.add("hidden");
        document.getElementById("loader-text").innerText = "Failed to refer..";
        console.error("Fetch error:", error.message);
      });
  } else {
    alert("auth code not found..");
    document.getElementById("loader-container").classList.add("hidden");
    let image = document.createElement("img");
    image.setAttribute("src", "./failed.png");
    image.setAttribute("draggable", false);
    let container = document.getElementById("container");
    container.classList.add("flex", "justify-center", "items-center");
    container.replaceChildren(image);
  }
}
document.getElementById("send-data")?.addEventListener("click", (event) => {
  let patient_id = localStorage.getItem("atriai_patient_id");
  let access_token = localStorage.getItem("access_token");
  if (!patient_id) {
    alert("Can't find the Atriai patient ID");
    return;
  }
  if (!access_token) {
    alert("Can't find the Atriai acccess token");
    return;
  }
  document.getElementById("loader-container").classList.remove("hidden");
  let payload = {};
  fieldList.forEach((element) => {
    let textarea = document.getElementById(element);
    if (textarea) {
      try {
        payload[element] = JSON.parse(textarea.value);
      } catch (error) {
        payload[element] = textarea.value;
      }
    }
  });
  fetch(`${config.apiUrl}/sites/patient/${patient_id}/epic`, {
    method: "PUT",
    headers: {
      "Content-type": "application/json; charset=UTF-8",
      Authorization: `Bearer ${access_token}`,
    },
    body: JSON.stringify(payload),
  })
    .then(async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
      }
      return await response.json();
    })
    .then((data) => {
      alert("data updated successfully..");
      document.getElementById("loader-container").classList.add("hidden");
    })
    .catch((error) => {
      alert(error.message);
      document.getElementById("loader-container").classList.add("hidden");
      document.getElementById("loader-text").innerText = "Failed to refer..";
      console.error("Fetch error:", error.message);
    });
});

document.getElementById("generate-data")?.addEventListener("click", function (e) {
    document.getElementById("loader-container").classList.remove("hidden");
    document.getElementById("loader-text").innerText = "Loading..";
    let patient_id = localStorage.getItem("atriai_patient_id");
    let access_token = localStorage.getItem("access_token");
    fetch(`${config.scheduler_apiUrl}/patients/medical-records/fhir-data/${patient_id}/generate`,
      {
        method: "GET",
        headers: {
          "Content-type": "application/json; charset=UTF-8",
          "Authorization": `Bearer ${access_token}`,
        },
      }
    )
    .then(async response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
      }
      return await response.json();
    }).then(data => {
      let parsed_resource = {};
      if (data) {
        fhirData = data;
        let entries = data?.entry || [];
        entries.forEach((element) => {
          let resourcetype = element?.resource?.resourceType;
          if (resourcetype) {
            let key = resourcetype.toLowerCase();
            if (!parsed_resource[key]) {
              parsed_resource[key] = [];
            }
            parsed_resource[key].push(element.resource);
          }
        });
        fieldList.forEach((element) => {
          let textArea = document.getElementById(element);
          if (textArea) {
            if (element != "patient") {
              let data = parsed_resource?.[element] || [];
              textArea.value = JSON.stringify(data, null, 2);
            }
          }
        });
      } else {
        throw new Error("Invalid json format..");
      }
      document.getElementById("loader-container").classList.add("hidden");
    }).catch((error) => {
      alert(error.message);
      document.getElementById("loader-container").classList.add("hidden");
      document.getElementById("loader-text").innerText = "Failed to refer..";
      console.error("Fetch error:", error.message);
    });
  });

document
  .getElementById("upload-trigger")
  ?.addEventListener("click", function () {
    document.getElementById("json-file").click();
  });
document
  .getElementById("json-file")
  ?.addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = function (e) {
      try {
        const jsonData = JSON.parse(e.target.result);
        let parsed_resource = {};
        if (jsonData) {
          fhirData = jsonData;
          let entries = jsonData?.entry || [];
          entries.forEach((element) => {
            let resourcetype = element?.resource?.resourceType;
            if (resourcetype) {
              let key = resourcetype.toLowerCase();
              if (!parsed_resource[key]) {
                parsed_resource[key] = [];
              }
              parsed_resource[key].push(element.resource);
            }
          });
          fieldList.forEach((element) => {
            let textArea = document.getElementById(element);
            if (textArea) {
              if (element != "patient") {
                let data = parsed_resource?.[element] || [];
                textArea.value = JSON.stringify(data, null, 2);
              }
            }
          });
        } else {
          throw new Error("Invalid json format..");
        }
      } catch (error) {
        console.error(error);
      }
    };
    reader.readAsText(file);
  });


function switchTab(tab) {
    document.getElementById('editor').classList.toggle('hidden', tab !== 'editor');
    document.getElementById('parser').classList.toggle('hidden', tab !== 'parser');
    
    document.getElementById('editorTab').classList.toggle('text-white', tab === 'editor');
    document.getElementById('editorTab').classList.toggle('bg-[#8143D9]', tab === 'editor');
    document.getElementById('editorTab').classList.toggle('bg-white', tab !== 'editor');
    
    document.getElementById('parserTab').classList.toggle('text-white', tab === 'parser');
    document.getElementById('parserTab').classList.toggle('bg-[#8143D9]', tab === 'parser');
    document.getElementById('parserTab').classList.toggle('bg-white', tab !== 'parser');

    if(tab == "parser"){
      getFHIRData();
      processAndDisplayData();
    }
}

function getFHIRData(){
  let fhir_data = {
      "resourceType": "Bundle",
      "type": "transaction",
      "entry": []
  }
  fieldList.forEach(field => {
    let element = document.getElementById(field);
    if(element){
      let parsed_data = element.value || [];
      
      if(parsed_data != ""){
        try {
          parsed_data = JSON.parse(parsed_data);
        } catch (error) {
          console.log(error);
        }
      }
      if(typeof(parsed_data) == "string"){
        parsed_data = []
      }
      let resource_data = parsed_data.map(resource => {
        return {
          "resource": resource
        }
      }) || [];

      fhir_data.entry.push(...resource_data);
    }
  });
  fhirData = fhir_data;
}



function processAndDisplayData() {
  if(fhirData == null){
    showError('No json data found');
    // document.getElementById('loader').style.display = 'none'; // Hide the loader
    return;
  }
  if (!fhirData || !fhirData.entry) {
      showError('Invalid FHIR Bundle format. The file must contain an "entry" array.');
      // document.getElementById('loader').style.display = 'none'; // Hide the loader
      return;
  }

  resourcesByType = {};
  fhirData.entry.forEach(entry => {
      const resource = entry.resource;
      if (resource && resource.resourceType) {
          if (!resourcesByType[resource.resourceType]) {
              resourcesByType[resource.resourceType] = [];
          }
          resourcesByType[resource.resourceType].push(resource);
      }
  });

  displayStatistics();
  displayGroupedResources();
  hideError();

  // Hide the loader after processing is done
  // document.getElementById('loader').style.display = 'none';
}


function displayStatistics() {
  const statsSection = document.getElementById('statsSection');
  const statsContent = document.getElementById('statsContent');

  const totalResources = Object.values(resourcesByType)
      .reduce((sum, resources) => sum + resources.length, 0);

  let statsHTML = ` 
<p><strong>Total Resources:</strong> ${totalResources}</p>
<p><strong>Resource Types:</strong> ${Object.keys(resourcesByType).length}</p>
<p><strong>Available Types:</strong></p>
<ul>
  ${Object.entries(resourcesByType)
          .map(([type, resources]) =>
              `<li><a href="#${type}" class="scroll-link">${type}: ${resources.length} resources</a></li>`)
          .join('')}
</ul>
`;

  statsContent.innerHTML = statsHTML;
  statsSection.style.display = 'block';

  // Add smooth scrolling behavior
  document.querySelectorAll('.scroll-link').forEach(link => {
      link?.addEventListener('click', function (event) {
          event.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          document.getElementById(targetId)?.scrollIntoView({ behavior: 'smooth' });
      });
  });
}


function displayGroupedResources() {
  const viewer = document.getElementById('resourceViewer');
  viewer.innerHTML = '';

  Object.entries(resourcesByType).forEach(([type, resources]) => {
      const section = document.createElement('div');
      section.className = 'resource-section';
      section.id = type; // Add ID for scrolling

      const header = document.createElement('div');
      header.className = 'resource-header';
      header.textContent = `${type}s`;
      section.appendChild(header);

      resources.forEach((resource, index) => {
          const resourceHeader = document.createElement('div');
          resourceHeader.className = 'resource-item-header';
          resourceHeader.textContent = `${type} ${index + 1}`;
          section.appendChild(resourceHeader);

          const content = document.createElement('div');
          content.className = 'resource-content';
          processObject(resource, content);
          section.appendChild(content);
      });

      viewer.appendChild(section);
  });
}


function processObject(obj, container, prefix = '') {
  for (let key in obj) {
      if (obj.hasOwnProperty(key) && key !== 'resourceType') {
          const value = obj[key];
          const fullKey = prefix ? `${prefix}.${key}` : key;

          if (Array.isArray(value)) {
              displayArray(fullKey, value, container);
          } else if (typeof value === 'object' && value !== null) {
              displayObject(fullKey, value, container);
          } else {
              displaySimpleKeyValue(fullKey, value, container);
          }
      }
  }
}

function displayArray(key, array, container) {
  const arrayContainer = document.createElement('div');
  arrayContainer.className = 'key-value-pair';

  const keyElem = document.createElement('div');
  keyElem.className = 'key';
  keyElem.textContent = key;

  const valueContainer = document.createElement('div');
  valueContainer.className = 'array-container';

  array.forEach((item, index) => {
      const itemContainer = document.createElement('div');
      itemContainer.className = 'array-item';

      const itemHeader = document.createElement('div');
      itemHeader.className = 'array-header';
      itemHeader.textContent = `Item ${index + 1}`;
      itemContainer.appendChild(itemHeader);

      if (typeof item === 'object' && item !== null) {
          processObject(item, itemContainer);
      } else {
          const textValue = document.createElement('div');
          textValue.textContent = formatValue(item);
          itemContainer.appendChild(textValue);
      }

      valueContainer.appendChild(itemContainer);
  });

  arrayContainer.appendChild(keyElem);
  arrayContainer.appendChild(valueContainer);
  container.appendChild(arrayContainer);
}

function displayObject(key, obj, container) {
  const pair = document.createElement('div');
  pair.className = 'key-value-pair';

  const keyElem = document.createElement('div');
  keyElem.className = 'key';
  keyElem.textContent = key;

  const valueContainer = document.createElement('div');
  valueContainer.className = 'array-container';

  Object.entries(obj).forEach(([subKey, subValue]) => {
      if (Array.isArray(subValue)) {
          displayArray(subKey, subValue, valueContainer);
      } else if (typeof subValue === 'object' && subValue !== null) {
          displayObject(subKey, subValue, valueContainer);
      } else {
          displaySimpleKeyValue(subKey, subValue, valueContainer);
      }
  });

  pair.appendChild(keyElem);
  pair.appendChild(valueContainer);
  container.appendChild(pair);
}

function displaySimpleKeyValue(key, value, container) {
  const pair = document.createElement('div');
  pair.className = 'key-value-pair';

  const keyElem = document.createElement('div');
  keyElem.className = 'key';
  keyElem.textContent = key;

  const valueElem = document.createElement('div');
  valueElem.className = 'value';
  valueElem.textContent = formatValue(value);

  pair.appendChild(keyElem);
  pair.appendChild(valueElem);
  container.appendChild(pair);
}

function formatValue(value) {
  if (value === null) return 'null';
  if (value === undefined) return 'undefined';
  if (typeof value === 'boolean') return value.toString();
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'string') {
      // Check if it's base64 encoded
      if (isBase64(value)) {
          try {
              return decodeBase64(value);
          } catch (e) {
              return '[Invalid base64 data]';
          }
      }
      return value;
  }
  if (Array.isArray(value)) return ''; // No direct text, will process below
  if (typeof value === 'object') return ''; // No direct text, will process below
  return value.toString();
}

function isBase64(str) {
  // Ensure the string length is greater than 100 characters (or another threshold)
  if (str.length < 100) return false;

  // Check for valid base64 characters (alphanumeric + /, +, =)
  const base64Pattern = /^[A-Za-z0-9+/=]+$/;
  if (!base64Pattern.test(str)) return false;

  // Ensure the length of the string is a multiple of 4 (padding rule)
  if (str.length % 4 !== 0) return false;

  // Optional: You can decode and check if the base64 string is valid
  try {
      // Attempt to decode the base64 string
      atob(str);
      return true;  // If it decodes successfully, it is valid base64
  } catch (e) {
      return false;  // If decoding fails, it's not valid base64
  }
}


function decodeBase64(base64Str) {
  // Decode base64 string
  const decodedStr = atob(base64Str);
  return decodedStr;
}



function showError(message) {
  const errorDiv = document.getElementById('errorMessage');
  errorDiv.textContent = message;
  errorDiv.style.display = 'block';
}

function hideError() {
  document.getElementById('errorMessage').style.display = 'none';
}