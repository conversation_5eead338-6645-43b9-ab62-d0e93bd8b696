<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login</title>
    <link rel="stylesheet" href="css/login.css" />
    <link rel="icon" href="images/scheduler_logo.png" type="image/x-icon" />
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->

    <!-- Flatpickr CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="js/jquery.min.js"></script>
    <script src="./config.js"></script>
    <script src="js/main.js"></script>
    <script src="js/api.js"></script>
    <link rel="stylesheet" href="css/output.css" />

    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>
  </head>

  <body class="bg-[#F1F1FB]">
    <div
      class="flex justify-start items-center space-x-3 mb-6 ml-8 md:ml-4 mt-4"
    >
      <img
        src="svg/calendar-icon.svg"
        class="h-[3rem] w-[3rem] md:h-auto md:w-auto"
      />
      <h1 class="text-lg font-semibold text-gray-700">
        CorMetrix <br />Scheduler
      </h1>
    </div>
    <div class="absolute z-10 bottom-4 left-4 hidden md:block">
      <img src="svg/bottom-left.svg" />
    </div>
    <div class="absolute z-10 bottom-0 left-0 md:hidden">
      <img src="svg/bottom-left-mobile.svg" class="h-[90%]" />
    </div>
    <div class="absolute z-1 bottom-[6rem] left-[13rem] hidden md:block">
      <img src="svg/star.svg" />
    </div>

    <div
      class="absolute z-20 grid grid-cols-1 md:grid-cols-2 w-full gap-10 p-5 mt-10 md:mt-0 items-center"
    >
      <div
        class="bg-white shadow-lg rounded-lg max-w-80 md:max-w-lg p-8 mx-auto w-full"
      >
        <div class="flex flex-col items-center">
          <div class="p-3 rounded mb-4 h-[6rem] w-[6rem] md:h-auto md:w-auto">
            <img src="/svg/welcome-icon.svg" alt="Icon" />
          </div>
          <h2 class="text-2xl font-semibold text-gray-700 mb-6">Welcome</h2>
        </div>
        <div class="space-y-8 px-2 md:px-12">
          <!-- <div class="space-y-2">
                    <label>Enter your user name</label>
                    <div class="relative w-full">
                        <img src="/svg/username.svg" alt="Icon"
                            class="absolute top-1/2 left-4 transform -translate-y-1/2 w-6 h-6">
                        <input type="text" id="username"
                            class="w-full px-12 py-4 border rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-600"
                            required>
                    </div>
                </div> -->
          <!-- <div class="space-y-2">
                    <label>Enter your password</label>
                    <div class="relative w-full">
                        <img src="/svg/password.svg" alt="Icon"
                            class="absolute top-1/2 left-4 transform -translate-y-1/2 w-6 h-6">
                        <input type="password" id="password"
                            class="w-full px-12 py-4 border rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-600"
                            required>
                    </div>
                </div> -->
          <div
            id="error-message"
            class="text-red-600 text-lg mt-5 text-center bg-red-100 p-2.5 border border-red-600 rounded-md relative"
            style="display: none"
          >
            <span id="error-text"></span>
            <button id="close-error" class="close-btn">X</button>
          </div>
          <button
            type="submit"
            onclick="login()"
            class="w-full bg-gray-800 text-white py-4 rounded-2xl font-semibold hover:bg-gray-900"
          >
            Login
          </button>
        </div>
      </div>

      <!-- <div class="w-full flex-col justify-center items-center text-purple-600 font-semibold hidden md:flex">
            <h2 class="text-[50px]">Set Your<br> Priorities.</h2>
        </div> -->
    </div>
    <div
      id="main-loader"
      class="w-full h-full flex justify-center items-center hidden absolute top-0 left-0 bg-black bg-opacity-60 z-30"
    >
      <div class="loader"></div>
    </div>
    <div class="absolute top-4 right-4 hidden md:block">
      <img src="svg/top-right.svg" />
    </div>
    <div class="absolute top-0 right-0 md:hidden">
      <img src="svg/top-right-mobile.svg" class="w-40" />
    </div>
  </body>
</html>
