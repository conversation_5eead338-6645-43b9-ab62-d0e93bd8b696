/* Custom CSS for settings tabs */
#settings-tabs {
  position: sticky;
  top: 0;
  z-index: 9999;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#techs {
  height: calc(100% - 48px);
  overflow-y: auto;
}

@media (max-width: 767px) {
  #techs {
    height: calc(100% - 96px); /* Account for two rows of tabs in mobile */
  }
}

.main-techs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
