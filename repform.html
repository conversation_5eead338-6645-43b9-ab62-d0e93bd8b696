<style>
  .container {
    --uib-size: 40px;
    --uib-color: black;
    --uib-speed: 2s;
    --uib-bg-opacity: 0;
    height: var(--uib-size);
    width: var(--uib-size);
    transform-origin: center;
    animation: rotate var(--uib-speed) linear infinite;
    will-change: transform;
    overflow: visible;
  }

  .car {
    fill: none;
    stroke: var(--uib-color);
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: stretch calc(var(--uib-speed) * 0.75) ease-in-out infinite;
    will-change: stroke-dasharray, stroke-dashoffset;
    transition: stroke 0.5s ease;
  }

  .track {
    fill: none;
    stroke: var(--uib-color);
    opacity: var(--uib-bg-opacity);
    transition: stroke 0.5s ease;
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes stretch {
    0% {
      stroke-dasharray: 0, 150;
      stroke-dashoffset: 0;
    }

    50% {
      stroke-dasharray: 75, 150;
      stroke-dashoffset: -25;
    }

    100% {
      stroke-dashoffset: -100;
    }
  }
</style>

<div class="flex w-full h-full mb-9 mt-3 justify-center">
  <div
    id="formLoader"
    class="absolute w-full bg-white bg-opacity-50 h-full flex items-center justify-center"
  >
    <svg class="container" viewBox="0 0 40 40" height="40" width="40">
      <circle
        class="track"
        cx="20"
        cy="20"
        r="17.5"
        pathlength="100"
        stroke-width="5px"
        fill="none"
      />
      <circle
        class="car"
        cx="20"
        cy="20"
        r="17.5"
        pathlength="100"
        stroke-width="5px"
        fill="none"
      />
    </svg>
  </div>

  <div
    id="RepSuccessModal"
    class="absolute w-full bg-white bg-opacity-50 hidden h-full flex items-center justify-center"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-sm w-full z-10">
      <div
        id="header"
        class="flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg"
      >
        <span id="headertitle"></span>
      </div>
      <div class="p-4 text-center">
        <p class="text-gray-600" id="desc"></p>
      </div>
      <div class="p-4 flex justify-center">
        <button
          id="repModalOkBtn"
          class="px-4 py-2 bg-green-500 text-white font-medium rounded"
        >
          OK
        </button>
      </div>
    </div>
  </div>

  <form
    id="repFormElement"
    class="space-y-4 w-[80%] mb-4"
    onsubmit="createNewRep(event)"
  >
    <div>
      <h1 class="font-bold text-2xl mb-2">REP Info</h1>
      <label for="first_name" class="block text-sm font-medium text-gray-700"
        >First Name <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="first_name"
        name="first_name"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      />
    </div>
    <div>
      <label for="last_name" class="block text-sm font-medium text-gray-700"
        >Last Name <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="last_name"
        name="last_name"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      />
    </div>
    <div>
      <label for="username" class="block text-sm font-medium text-gray-700"
        >Username <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="username"
        name="username"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      />
    </div>
    <div>
      <label for="email" class="block text-sm font-medium text-gray-700"
        >Email <span class="text-red-500">*</span></label
      >
      <input
        type="email"
        id="email"
        name="email"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      />
    </div>
    <div>
      <label for="password" class="block text-sm font-medium text-gray-700"
        >Password <span class="text-red-500">*</span></label
      >
      <input
        type="password"
        id="password"
        name="password"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      />
    </div>
    <div>
      <label for="proceduretype" class="block text-sm font-medium text-gray-700"
        >Procedure Type <span class="text-red-500">*</span></label
      >
      <select
        id="proceduretype"
        name="procedure_type_id"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      >
        <option value="">Select a procedure</option>
        <option value="Type A">Type A</option>
        <option value="Type B">Type B</option>
        <option value="Type C">Type C</option>
      </select>
    </div>
    <div>
      <label for="experience" class="block text-sm font-medium text-gray-700"
        >Experience (in years) <span class="text-red-500">*</span></label
      >
      <select
        id="experience"
        name="experience"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      >
        <option value="">Select a procedure</option>
        <option value="Type A">Type A</option>
        <option value="Type B">Type B</option>
        <option value="Type C">Type C</option>
      </select>
    </div>
    <div>
      <label for="home_zip_code" class="block text-sm font-medium text-gray-700"
        >Home Zip Pincode <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="home_zip_code"
        name="home_zip_code"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
      />
    </div>
    <button
      type="submit"
      style="margin-bottom: 1rem"
      class="w-full mb-4 bg-[#8F58DD] text-white py-2 px-4 rounded-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
    >
      Submit
    </button>
  </form>
</div>

<script src="js/settings.js"></script>
