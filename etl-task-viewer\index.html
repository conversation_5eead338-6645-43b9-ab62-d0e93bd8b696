<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Monitor</title>
    <style>
        :root {
            --primary-color: #8143D9;
            --background-color: #f9fafb;
            --card-color: #ffffff;
            --text-color: #1f2937;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --info-color: #8143D9;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
        }

        .status-dot {
            height: 8px;
            width: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-dot.connected {
            background-color: var(--success-color);
        }

        .status-dot.disconnected {
            background-color: var(--error-color);
        }

        .filters {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .search-box {
            position: relative;
            flex: 1;
            max-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .log-list {
            background-color: var(--background-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .log-item {
            background-color: var(--card-color);
            margin-bottom: 16px;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.2s;
        }

        .log-item:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .case-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .timestamp {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .log-level {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-left: 8px;
        }

        .log-level.info {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
        }

        .log-level.warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .log-level.error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .log-entry {
            background-color: #f9fafb;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .log-entry:last-child {
            margin-bottom: 0;
        }

        .log-entry.error {
            border-left: 3px solid var(--error-color);
            background-color: rgba(239, 68, 68, 0.05);
        }

        .log-entry.warning {
            border-left: 3px solid var(--warning-color);
            background-color: rgba(245, 158, 11, 0.05);
        }

        .log-entry.info {
            border-left: 3px solid var(--info-color);
            background-color: rgba(59, 130, 246, 0.05);
        }

        .log-message {
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .log-details {
            font-size: 0.75rem;
            margin-top: 4px;
            color: #6b7280;
        }

        .empty-state {
            padding: 40px;
            text-align: center;
            color: #6b7280;
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .file-name {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 6px;
            font-style: italic;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: auto;
        }

        .status-badge.success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        @media (max-width: 640px) {
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .filters {
                flex-direction: column;
                gap: 8px;
            }

            .search-box {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Log Monitor</h1>
            <div class="status-indicator">
                <div id="status-dot" class="status-dot disconnected"></div>
                <span id="connection-status">Disconnected</span>
            </div>
        </div>

        <div class="filters">
            <div class="search-box">
                <input type="text" id="search-input" placeholder="Search by case ID or message...">
            </div>
        </div>

        <div class="log-list" id="log-container">
            <div class="empty-state">
                Waiting for data...
            </div>
        </div>
    </div>

    <script src="/config.js"></script>

    <script>
        // WebSocket connection
        const socket = new WebSocket(`wss://${config.apiUrl.replace('https://','')}/db-logs/ws`);
        const statusDot = document.getElementById('status-dot');
        const connectionStatus = document.getElementById('connection-status');
        const logContainer = document.getElementById('log-container');
        const searchInput = document.getElementById('search-input');
        
        let logs = [];

        // Function to extract date string from various formats
        function extractDateString(dateObj) {
            // Log for debugging
            console.log('Original date object:', dateObj);
            
            // Handle MongoDB style date objects
            if (dateObj && typeof dateObj === 'object' && dateObj.$date) {
                return dateObj.$date;
            }
            
            // Handle regular date strings
            if (typeof dateObj === 'string') {
                return dateObj;
            }
            
            // Handle direct Date objects (unlikely but possible)
            if (dateObj instanceof Date) {
                return dateObj.toISOString();
            }
            
            return null;
        }

        // Improved date formatting functions
        function formatDate(dateObj) {
            const dateString = extractDateString(dateObj);
            console.log('Extracted date string:', dateString);
            
            if (!dateString) return 'N/A';
            
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    console.warn('Invalid date from string:', dateString);
                    return 'Invalid date';
                }
                
                return new Intl.DateTimeFormat('default', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                }).format(date);
            } catch (error) {
                console.error('Error formatting date:', error);
                return 'Invalid date';
            }
        }

        function formatTime(dateObj) {
            const dateString = extractDateString(dateObj);
            
            if (!dateString) return 'N/A';
            
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return 'Invalid time';
                
                return new Intl.DateTimeFormat('default', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                }).format(date);
            } catch (error) {
                console.error('Error formatting time:', error);
                return 'Invalid time';
            }
        }

        // Enhanced HTML escaping function for safety
        function escapeHtml(str) {
            if (!str || typeof str !== 'string') return '';
            
            return str
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');
        }

        // WebSocket event listeners
        socket.onopen = function() {
            statusDot.classList.remove('disconnected');
            statusDot.classList.add('connected');
            connectionStatus.textContent = 'Connected';
            console.log('WebSocket connection established');
        };

        socket.onclose = function() {
            statusDot.classList.remove('connected');
            statusDot.classList.add('disconnected');
            connectionStatus.textContent = 'Disconnected';
            console.log('WebSocket connection closed');
            
            // Optional: Add reconnection logic here
            setTimeout(() => {
                if (socket.readyState === WebSocket.CLOSED) {
                    console.log('Attempting to reconnect...');
                    location.reload(); // Simple reconnect by reloading
                }
            }, 5000);
        };

        socket.onerror = function(error) {
            console.error('WebSocket error:', error);
        };

        socket.onmessage = function(event) {
            try {
                // Parse the received data
                const response = JSON.parse(event.data);
                
                // Debug the response structure
                console.log('WebSocket response:', response);
                
                // Check if the message contains the expected structure
                if (response.message === "Logs data" && response.data && response.status === "success") {
                    // Handle both single object and array of objects
                    if (Array.isArray(response.data)) {
                        logs = response.data;
                    } else {
                        logs = [response.data];
                    }
                    
                    // Ensure all logs have the required structure
                    logs = logs.filter(log => log && log.case_id);
                    
                    renderLogs();
                } else {
                    console.log('Received WebSocket message with unexpected format:', response);
                }
            } catch (error) {
                console.error('Error parsing WebSocket data:', error);
            }
        };

        // Search functionality
        searchInput.addEventListener('input', renderLogs);

        function renderLogs() {
            const searchTerm = searchInput.value.toLowerCase();
            
            // Filter logs based on search term
            const filteredLogs = logs.filter(log => {
                // Check if case_id exists and includes the search term
                const caseIdMatch = log.case_id && 
                    typeof log.case_id === 'string' && 
                    log.case_id.toLowerCase().includes(searchTerm);
                
                // Check if logs array exists and any log message includes the search term
                const logsMatch = Array.isArray(log.logs) && log.logs.some(logEntry => 
                    logEntry && logEntry.message && 
                    typeof logEntry.message === 'string' && 
                    logEntry.message.toLowerCase().includes(searchTerm)
                );
                
                return caseIdMatch || logsMatch;
            });
            
            if (filteredLogs.length === 0) {
                logContainer.innerHTML = `
                    <div class="empty-state">
                        ${logs.length > 0 ? 'No matching logs found' : 'Waiting for data...'}
                    </div>
                `;
                return;
            }
            
            // Sort logs by created_at in descending order (newest first)
            filteredLogs.sort((a, b) => {
                const dateStrA = extractDateString(a.created_at);
                const dateStrB = extractDateString(b.created_at);
                
                if (!dateStrA || !dateStrB) return 0;
                
                const dateA = new Date(dateStrA);
                const dateB = new Date(dateStrB);
                
                if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) return 0;
                
                return dateB - dateA;
            });
            
            // Render the logs
            logContainer.innerHTML = filteredLogs.map(log => {
                // Format the main timestamp for this log entry
                const timestamp = formatDate(log.created_at);
                
                // Get log entries if they exist
                const logEntries = Array.isArray(log.logs) ? log.logs : [];
                
                // Sort log entries by timestamp if available
                logEntries.sort((a, b) => {
                    const dateStrA = extractDateString(a.timestamp);
                    const dateStrB = extractDateString(b.timestamp);
                    
                    if (!dateStrA || !dateStrB) return 0;
                    
                    const dateA = new Date(dateStrA);
                    const dateB = new Date(dateStrB);
                    
                    if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) return 0;
                    
                    return dateB - dateA; // Newest first
                });
                
                const logEntriesHtml = logEntries.map(entry => {
                    if (!entry) return '';
                    
                    // Format just the time portion of the timestamp
                    const entryTime = formatTime(entry.timestamp);
                    
                    // Get the log level, defaulting to 'info' if not specified
                    const level = entry.level && typeof entry.level === 'string' ? 
                        entry.level.toLowerCase() : 'info';
                    
                    // Get the message or show a placeholder
                    const message = entry.message || 'No message provided';
                    
                    return `
                        <div class="log-entry ${level}">
                            <div class="log-header">
                                <span class="timestamp">${entryTime}</span>
                                <span class="log-level ${level}">${level}</span>
                            </div>
                            <div class="log-message">${escapeHtml(message)}</div>
                            ${entry.file_name ? `<div class="file-name">File: ${escapeHtml(entry.file_name)}</div>` : ''}
                        </div>
                    `;
                }).join('');
                
                return `
                    <div class="log-item">
                        <div class="log-header">
                            <span class="case-id">Case ID: ${escapeHtml(log.case_id || 'Unknown')}</span>
                            <span class="timestamp">${timestamp}</span>
                        </div>
                        ${logEntriesHtml || '<div class="log-message">No log entries available</div>'}
                    </div>
                `;
            }).join('');
        }

        // Initial empty state
        renderLogs();
        
        // For debugging
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.message, e.filename, e.lineno);
        });
    </script>
</body>
</html>