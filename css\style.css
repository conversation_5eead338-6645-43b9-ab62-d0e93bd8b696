body {
    font-family: 'Montserrat' <PERSON>l, Helvetica, sans-serif;
}

.custom-scrollbar-vertical::-webkit-scrollbar {
    width: 7.5px;
    height: 7.5px;
}

.custom-scrollbar-vertical::-webkit-scrollbar-thumb {
    background-color: rgba(57, 127, 188, 1);
}

.custom-scrollbar-vertical::-webkit-scrollbar-corner {
    background-color: rgba(57, 127, 188, 1);
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.fade-out {
    animation: fadeOut 0.5s ease-in;
}

.worker-card,
.jobs-cards,
.providers-cards,
.locations-cards {
    cursor: move;
}
#caseDate {
    color: black !important;
  }

@media (min-width: 768px) {
    .actual-schedules {
        width: 20rem;
    }
}

@media (min-width: 1024px) {
    .actual-schedules {
        width: 20rem;
    }
}

@media (min-width: 1440px) {
    .actual-schedules {
        width: 20rem;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }

    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

.actual-schedules {
    padding: 0px 10px 20px;
}

.custom-scrollbar-vertical::-webkit-scrollbar {
    width: 10px;
}

.custom-scrollbar-vertical::-webkit-scrollbar-thumb {
    background-color: #7c3aed;
    border-radius: 8px;
    border: 2px solid #f3e8ff;
}

.rotate-image {
    transition: transform 0.3s ease-in-out;
}

.rotated {
    transform: rotate(90deg);
}