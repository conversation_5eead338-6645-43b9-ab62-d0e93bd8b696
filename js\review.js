async function changeReview() {
  const dataMap = {
    rep: {
      data: JSON.parse(localStorage.getItem("rep_lead_changes")),
      container: document.getElementById("rep_changes"),
      title: "Rep lead changes",
      isRep: true,
    },
    agent: {
      data: JSON.parse(localStorage.getItem("agent_changes")),
      container: document.getElementById("agent_changes"),
      title: "Agent changes",
      isRep: false,
    },
  };

  Object.values(dataMap).forEach(({ data, container, title, isRep }) => {
    if (!data || data.length === 0) {
      displayNoDataMessage(container);
    } else {
      const groupedData = groupDataByDate(data);
      const dates = Object.keys(groupedData).sort(
        (a, b) => new Date(a) - new Date(b)
      );
      updateMainContainer(container, title, dates, groupedData, isRep);
    }
  });
}

function displayNoDataMessage(container) {
  container.innerHTML = `
        <p class="flex flex-1 justify-center items-center text-black">
            No Data found for the selected date range
        </p>`;
}

function groupDataByDate(data) {
  return data.reduce((acc, item) => {
    (acc[item.procedure_date] = acc[item.procedure_date] || []).push(item);
    return acc;
  }, {});
}

function createSaveButton(isRep) {
  const div = document.createElement("div");
  div.className = "flex justify-end p-3";
  const button = document.createElement("button");
  button.id = `${isRep ? "rep_save_button" : "agent_save_button"}`;
  button.textContent = "Save";
  button.className =
    "bg-purple-500 text-black px-4 py-2 rounded-md mt-4 transition-all duration-300 hover:bg-purple-700";
  button.addEventListener("click", () =>
    savechanges(isRep ? "rep_lead_changes" : "agent_changes")
  );
  // button.disabled = true;
  div.appendChild(button);
  return div;
}

function updateMainContainer(container, title, dates, groupedData, isRep) {
  container.innerHTML = "";
  const fragment = document.createDocumentFragment();
  fragment.appendChild(createMainContainer(title, dates));
  fragment.appendChild(
    createContentContainer(generateCardsHTML(groupedData, isRep), isRep)
  );
  fragment.appendChild(createSaveButton(isRep));

  container.appendChild(fragment);

  attachEventListeners();
}

function createMainContainer(title, dates) {
  const container = document.createElement("div");
  container.className = "flex flex-col";
  container.innerHTML = `
        <div class="flex justify-end p-3">
            <span class="capitalize font-bold text-black">${title}</span>
        </div>
        <div class="flex justify-center">
            ${dates.map(createDateHeaderHTML).join("")}
        </div>`;
  return container;
}

function createDateHeaderHTML(date) {
  const d = new Date(date);
  return `
        <div class="flex flex-col items-center my-2 px-2 select-none">
            <div class="bg-purple-100 text-purple-600 rounded-full flex flex-col items-center justify-center w-14 h-14">
                <div class="text-xs md:text-sm font-bold pt-1">${d
                  .toLocaleString("default", { month: "short" })
                  .toUpperCase()}</div>
                <div class="text-lg md:text-xl font-bold">${d.getDate()}</div>
            </div>
            <div class="flex flex-row md:flex-col space-x-2 items-center mx-2">
                <div class="text-black text-md uppercase md:mt-1">${d.toLocaleString(
                  "default",
                  { weekday: "short" }
                )}</div>
            </div>
        </div>`;
}

function generateCardsHTML(groupedData, isRep) {
  return Object.entries(groupedData)
    .map(([date, items]) => createPerDayData(date, items, isRep))
    .join("");
}

function createPerDayData(date, items, isRep) {
  const cardsHTML = sortItemsByProvider(items)
    .map((item) => createCard(item, isRep))
    .join("");
  return `
        <div class="flex flex-col min-w-60 mx-2">
            <div class="flex items-center justify-between my-2 px-2 py-4 select-none">
                <hr class="flex-grow border-t border-gray-300">
                <span class="text-right text-black pl-4">${date}</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">${cardsHTML}</div>
        </div>`;
}

function createCard(item, isRep) {
  const temp = document.createElement("div");

  const cardHTML = `<div class="${
    isRep ? "rep_lead_changes" : "agent_changes"
  }" id="${isRep ? "rep" : "agent"}_${item.procedure_date}_${
    item?.site?.id || item?.site_id
  }_${item?.implanting_physician?.id || item?.implanting_physician_id}">
        <div class="relative p-4 flex flex-col space-y-2 ${
          isRep ? "bg-[#E3E3FF]" : "bg-white"
        } rounded-tl-[12px] rounded-tr-[12px]">
            <div class="text-black pointer-events-none">
                <div class="text-lg md:text-xl space-x-1.5 flex items-center font-semibold">
                    <span site_id="${
                      item?.site?.id || item?.site_id
                    }" procedure_date="${
    item.procedure_date
  }" procedure_start_time="${
    item?.procedure_start_time
  }" class="font-bold provider-span truncate pr-[3rem]">
                        ${item?.site?.name || item?.site_name}
                    </span>
                </div>
                <div class="text-sm font-semibold">
                    <span implanting_physician_id="${
                      item?.implanting_physician?.id ||
                      item?.implanting_physician_id
                    }" class="font-bold location-span">
                        ${
                          item?.implanting_physician?.name ||
                          item?.implanting_physician_name
                        }
                    </span>
                </div>
            </div>
            <div class="flex flex-row space-x-1">
                <img src="/svg/cases.svg">
                <div cases-total=${
                  item?.cases || item?.total_case
                } class="text-sm text-[#8F58DD] cases-span">Cases: ${
    item?.cases || item?.total_case
  }</div>
            </div>
            <button class="${
              isRep ? "add-rep-btn" : "add-rep-agent-btn"
            } bg-transparent text-white absolute right-4 top-2 hover:scale-110 transition-all duration-300 ease-in-out" 
                    title="Add more Clinical Specialist" onclick="showAvailablereps('${
                      item.procedure_date
                    }', '${isRep ? "rep" : "agent"}_${item.procedure_date}_${
    item?.site?.id || item?.site_id
  }_${item?.implanting_physician?.id || item?.implanting_physician_id}','${
    item?.site?.name || item?.site_name
  }','${item?.implanting_physician?.name || item?.implanting_physician_name}')">
                <img src="/svg/add.svg">
            </button>
        </div>
        <div class="sortable-container-techs px-2 py-0.5 max-h-full bg-[#F1F1FB]">
    </div></div>`;

  temp.innerHTML = cardHTML.trim();

  const div_data = createReviewWorkerCard(item?.rep || item);
  if (div_data) {
    temp.querySelector(".sortable-container-techs").appendChild(div_data);
  }

  createsortablecontainer();
  return temp.outerHTML;
}

function createsortablecontainer() {
  const sortableSelector = ".sortable-container-techs";
  const draggableSelector = ".worker-card";

  const sortableContainers = document.querySelectorAll(sortableSelector);

  sortableContainers.forEach((container) => {
    new Sortable(container, {
      group: "kanban",
      animation: 150,
      handle: draggableSelector,
      draggable: draggableSelector,
      onEnd: function (event) {
        const draggedElement = event.item;
        const innerDiv = draggedElement.querySelector("div[mod-type]");
        if (innerDiv) {
          innerDiv.setAttribute("mod-type", "update");
        }
        msnry.layout();
      },
    });
  });
}

function createReviewWorkerCard(rep) {
  let workerCardHTML = "";
  if (rep?.id || rep?.assigned_rep_id) {
    workerCardHTML = `
        <div class="worker-card px-2 py-1 mb-2 flex items-center">
            <div class="flex-grow mr-2 rep-span" tech-name="${
              rep?.name || rep?.rep_name || rep?.assigned_rep_name
            }" tech-job="N/A" mod-type="update" tech-aoid="${
      rep?.id || rep?.assigned_rep_id
    }">
                ${rep?.name || rep?.rep_name || rep?.assigned_rep_name}
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-2 py-1 text-black rounded-md hover:bg-purple-400 hover:text-white" title="Remove Rep">
                    <img src="/svg/delete.svg" class="">
                </button>
            </div>
        </div>`;
  }

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = workerCardHTML.trim();

  return tempDiv.firstChild;
}

function createContentContainer(html, isRep) {
  const container = document.createElement("div");
  container.id = `${isRep ? "rep_lead_changes" : "agent_changes"}`;
  container.innerHTML = html;
  return container;
}

function sortItemsByProvider(items) {
  return items.sort((a, b) => {
    const nameA =
      a?.implanting_physician?.name || a?.implanting_physician_name || "";
    const nameB =
      b?.implanting_physician?.name || b?.implanting_physician_name || "";
    return nameA.localeCompare(nameB);
  });
}

function buttonchange() {
  // const rep_save = document.getElementById("rep_save_button");
  // const agent_save = document.getElementById("agent_save_button");
  // if (rep_save) {
  //     rep_save.disabled = false;
  //     rep_save.classList.add("hover:bg-purple-700");
  // }
  // if (agent_save) {
  //     agent_save.disabled = false;
  //     agent_save.classList.add("hover:bg-purple-700");
  // }

  const items = document.querySelectorAll(".handle-state-change");
  const parentElements = Array.from(items).map((item) => item.parentElement);

  parentElements.forEach((item) => {
    const overlay = document.createElement("div");
    overlay.classList.add("overlay-blocker", "cursor-pointer");
    overlay.setAttribute(
      "data-tooltip",
      "You have unsaved changes. Please save or discard them before navigating."
    );

    Object.assign(overlay.style, {
      position: "absolute",
      top: "0",
      left: "0",
      width: "100%",
      height: "100%",
      backgroundColor: "transparent",
      zIndex: "1000",
      pointerEvents: "auto",
    });

    const parentStyle = window.getComputedStyle(item);
    if (parentStyle.position === "static") {
      item.style.position = "relative";
    }

    item.appendChild(overlay);

    const tooltip = document.createElement("div");
    tooltip.classList.add("tooltip");
    tooltip.innerText = overlay.getAttribute("data-tooltip");
    tooltip.style.cssText = `
            position: fixed; /* Changed to fixed */
            top: 50px;
            left: 0;
            padding: 5px 10px;
            background: #333;
            color: #fff;
            width: 350px;
            border-radius: 4px;
            word-wrap: break-word;
            display: none;
            z-index: 1001;
        `;
    document.body.appendChild(tooltip);

    overlay.addEventListener("mouseenter", function () {
      const overlayRect = overlay.getBoundingClientRect();
      const tooltipRect = tooltip.getBoundingClientRect();

      let top = overlayRect.top + window.scrollY;
      let left = overlayRect.right + window.scrollX;

      tooltip.style.top = `${top + 50}px`;
      tooltip.style.left = `${left}px`;

      const viewportWidth = window.innerWidth;
      if (left + tooltipRect.width > viewportWidth) {
        tooltip.style.left = `${viewportWidth - tooltipRect.width - 10}px`;
      }

      tooltip.style.display = "block";
      const parent = overlay.parentElement;
      const closestHandleStateChange = parent.querySelector(
        ".handle-state-change"
      );

      if (closestHandleStateChange) {
        if (closestHandleStateChange.tagName.toLowerCase() === "a") {
          closestHandleStateChange.classList.add("bg-purple-400");
        } else if (
          closestHandleStateChange.tagName.toLowerCase() === "button"
        ) {
          if (closestHandleStateChange.classList.contains("date-btn")) {
            closestHandleStateChange.classList.add(
              "scale-110",
              "bg-purple-600",
              "shadow-2xl",
              "text-white"
            );
          } else if (
            closestHandleStateChange.classList.contains("dropdown-btn")
          ) {
            closestHandleStateChange.classList.add("shadow-xl");
          }
        }
      }
    });

    overlay.addEventListener("mouseleave", function () {
      tooltip.style.display = "none";
      const parent = overlay.parentElement;
      const closestHandleStateChange = parent.querySelector(
        ".handle-state-change"
      );

      if (closestHandleStateChange) {
        if (closestHandleStateChange.tagName.toLowerCase() === "a") {
          closestHandleStateChange.classList.remove("bg-purple-400");
        } else if (
          closestHandleStateChange.tagName.toLowerCase() === "button"
        ) {
          if (closestHandleStateChange.classList.contains("date-btn")) {
            closestHandleStateChange.classList.remove(
              "scale-110",
              "bg-purple-600",
              "shadow-2xl",
              "text-white"
            );
          } else if (
            closestHandleStateChange.classList.contains("dropdown-btn")
          ) {
            closestHandleStateChange.classList.remove("shadow-xl");
          }
        }
      }
    });
  });
}

function attachEventListeners() {
  document
    .querySelectorAll('.worker-card button[title="Remove Rep"]')
    .forEach((removeButton) => {
      removeButton.addEventListener("click", (e) => {
        buttonchange();
        const workerCard = e.target.closest(".worker-card");
        const repName = workerCard.querySelector("[tech-name]").textContent;
        const aoid = workerCard
          .querySelector("[tech-aoid]")
          .getAttribute("tech-aoid");

        removedTechs.push({ Aoid: aoid, rep_name: repName });
        workerCard.remove();
      });
    });
}

async function showAvailablereps(date, id, location, provider) {
  const overlay = document.createElement("div");
  overlay.className = "fixed inset-0 bg-black bg-opacity-50 z-40";

  const parentDiv = document.getElementById("content-div");
  parentDiv.appendChild(overlay);

  const panelDiv = document.createElement("div");
  panelDiv.className =
    "w-[14rem] md:w-96 fixed flex flex-col right-0 top-0 h-full bg-[#F1F1FB] shadow-lg z-50 transform transition-transform duration-300";
  parentDiv.appendChild(panelDiv);

  requestAnimationFrame(() => {
    parentDiv.classList.remove("translate-x-full");
    parentDiv.classList.add("translate-x-0");
  });

  panelDiv.innerHTML = `
        <div class="p-4 relative bg-white m-3 rounded-tl-[12px] rounded-tr-[12px]">
        <div class="space-y-2">
            <h2 class="text-lg md:text-xl font-semibold">Available Clinical Specialist</h2>
            <hr class="border">
            <p class="text-sm md:text-lg">In ${location} | For ${provider}</p>
            </div>
            <div class="relative my-4">
                <input id="search-input" type="text" placeholder="Search Reps" class="w-full p-2 border bg-[#F6F6F6] rounded-[12px] outline-none focus:outline-none">
                <button id="clear-search" class="absolute top-1/2 right-2 transform -translate-y-1/2 text-gray-500">
                    <img src="/svg/clear.svg">
                </button>
            </div>
            <button class="close-btn text-black hover:scale-110 transform transition-transform absolute top-4 right-4">
            <img src="/svg/close.svg">
            </button>
            <div class="flex justify-center items-center text-purple-600">
                <i class="text-xs">(Click a Rep to add them into the Schedule)</i>    
            </div>
        </div>
        <div class="flex-1 flex justify-center items-center loader-holder">
            <div class="loader"></div>
        </div>
        <div id="techs-container" class="gap-4 p-4 overflow-y-auto h-full hidden"></div>
    `;

  const loaderHolder = panelDiv.querySelector(".loader-holder");
  const techsContainer = panelDiv.querySelector("#techs-container");
  const searchInput = panelDiv.querySelector("#search-input");
  const clearSearchButton = panelDiv.querySelector("#clear-search");

  try {
    let availableTechs;

    availableTechs = await fetchUnscheduledTechs(date);

    loaderHolder.remove();

    const allWorkerCards = Array.from(
      document.querySelectorAll("#schedulesContainer .worker-card")
    );
    const existingTechNames = allWorkerCards.map((card) =>
      card.querySelector("div[tech-name]").getAttribute("tech-name")
    );

    const recommendedTechs = sortTechs(
      availableTechs.filter(
        (tech) => !existingTechNames.includes(tech.rep_name)
      )
    );

    const createSection = (title, techs, sectionId) => {
      const techsHtml = techs
        .map(
          (tech) => `
                        <div class="tech-name p-2 rounded cursor-pointer hover:bg-gray-200" data-aoid="${
                          tech?.Aoid || tech?.id
                        }">
                            <div class="text-container text-sm md:text-lg">
                                <div class="tech-name-text font-semibold text-purple-600">${
                                  tech.rep_name
                                }</div>
                            </div>
                        </div>
                    `
        )
        .join("");

      return `
                        <div class="tech-section mb-4">
                            <div class="section-header flex items-center justify-between">
                                <h3 class="text-sm md:text-lg font-semibold text-center mb-2">${title}</h3>
                                    <img src="/svg/arrow.svg" alt="image" class="toggle-button cursor-pointer rotate-image" data-section-id="${sectionId}">

                            </div>
                            <hr class="border">
                            <div class="section-content scale-100 translate-x-0 transition-all duration-300 mt-2" data-section-id="${sectionId}">
                                <div class="flex flex-col gap-2">
                                    ${techsHtml}
                                </div>
                            </div>
                        </div>
                    `;
    };

    function updateTechList() {
      const searchTerm = searchInput.value.toLowerCase();

      const filterTechs = (techs) =>
        techs.filter((tech) =>
          tech.rep_name.toLowerCase().includes(searchTerm)
        );

      techsContainer.innerHTML = `
                        ${
                          removedTechs.length > 0
                            ? createSection(
                                "Removed Clinical Specialist",
                                filterTechs(removedTechs),
                                "removed"
                              )
                            : ""
                        }
                        ${
                          recommendedTechs.length > 0
                            ? createSection(
                                "Recommended Clinical Specialist",
                                filterTechs(recommendedTechs),
                                "recommended"
                              )
                            : ""
                        }
                    `;
      techsContainer.classList.remove("hidden");
      techsContainer.classList.add("flex", "flex-col");
    }

    updateTechList();

    searchInput.addEventListener("input", updateTechList);
    clearSearchButton.addEventListener("click", () => {
      searchInput.value = "";
      updateTechList();
      searchInput.focus();
    });

    techsContainer.addEventListener("click", (event) => {
      if (event.target.closest(".tech-name")) {
        const techDiv = event.target.closest(".tech-name");
        const aoid = techDiv.dataset.aoid;
        const tech =
          availableTechs.find((t) => t.id === aoid) ||
          removedTechs.find((t) => t.Aoid === aoid.toString);
        if (tech) {
          const workerCard = createReviewWorkerCard(tech);

          const allocationDiv = document.getElementById(id);

          if (allocationDiv) {
            allocationDiv
              .querySelector(".sortable-container-techs")
              .appendChild(workerCard);
            createsortablecontainer();
            attachEventListeners();
            buttonchange();
          }
          closeOverlay();

          const techIndex = removedTechs.findIndex((t) => t.Aoid === aoid);
          if (techIndex !== -1) {
            removedTechs.splice(techIndex, 1);
          }
        }
        msnry.layout();
      }
    });
  } catch (error) {
    console.error("Error fetching techs:", error);
  }

  function closeOverlay() {
    panelDiv.classList.remove("translate-x-0");
    panelDiv.classList.add("translate-x-full");

    panelDiv.addEventListener("transitionend", () => {
      parentDiv.removeChild(panelDiv);
      parentDiv.removeChild(overlay);
    });
  }

  const closeButton = panelDiv.querySelector(".close-btn");
  closeButton.addEventListener("click", closeOverlay);

  overlay.addEventListener("click", closeOverlay);

  const handleToggleButtonClick = (event) => {
    const button = event.target;
    button.classList.toggle("rotated");
    const sectionId = button.getAttribute("data-section-id");
    const contentDiv = document.querySelector(
      `.section-content[data-section-id="${sectionId}"]`
    );

    if (contentDiv) {
      if (contentDiv.classList.contains("translate-x-full")) {
        contentDiv.classList.remove("hidden");
        setTimeout(() => {
          contentDiv.classList.remove("translate-x-full", "scale-0");
          contentDiv.classList.add("translate-x-0", "scale-100");
          button.textContent = "+";
        }, 300);
      } else {
        contentDiv.classList.remove("translate-x-0", "scale-100");
        contentDiv.classList.add("translate-x-full", "scale-0");
        button.textContent = "-";
        setTimeout(() => {
          contentDiv.classList.add("hidden");
        }, 300);
      }
    }
  };

  function attachToggleListeners() {
    const toggleButtons = document.querySelectorAll(".toggle-button");
    toggleButtons.forEach((button) => {
      button.addEventListener("click", handleToggleButtonClick);
    });
  }

  attachToggleListeners();
}

async function savechanges(div_id) {
  const repDiv = document.getElementById(div_id).querySelectorAll(`.${div_id}`);
  let repList = [];
  let calendereventList = [];

  repDiv.forEach((rep) => {
    const siteId = rep.querySelector(".provider-span").getAttribute("site_id");
    const siteName = rep.querySelector(".provider-span").textContent.trim();
    const Implanting_physician_id = rep
      .querySelector(".location-span")
      .getAttribute("implanting_physician_id");
    const procedureDate = rep
      .querySelector(".provider-span")
      .getAttribute("procedure_date");
    const procedurestartTime = rep
      .querySelector(".provider-span")
      .getAttribute("procedure_start_time")
      .split(":")
      .slice(0, 2)
      .join(":");
    const total_cases = rep
      .querySelector(".cases-span")
      .getAttribute("cases-total");
    const location = rep.querySelector(".location-span").textContent.trim();
    const techDivs = rep.querySelectorAll(
      ".sortable-container-techs .worker-card"
    );

    let rep_id = null;
    let type = "delete";
    if (techDivs.length > 0) {
      let hasUpdate = false;
      let firstNode = techDivs[0];
      techDivs.forEach((techDiv) => {
        const techElement = techDiv.querySelector(".flex-grow");
        if (techElement) {
          const currentType = techElement.getAttribute("mod-type");
          if (currentType === "update") {
            hasUpdate = true;
          }
        }
      });

      const techElement = firstNode.querySelector(".flex-grow");
      if (techElement) {
        rep_id = techElement.getAttribute("tech-aoid");
        if (hasUpdate) {
          type = "update";
        } else {
          type = techElement.getAttribute("mod-type");
        }
      }
    }

    repList.push({
      site_id: siteId,
      implanting_physician_id: Implanting_physician_id,
      procedure_date: procedureDate,
      Location: location,
      rep_id: rep_id,
      type: type,
    });

    calendereventList.push({
      rep_id: rep_id,
      procedure_date: procedureDate,
      procedure_start_time: procedurestartTime,
      site_name: siteName,
      total_case: total_cases,
    });
  });

  from_date = localStorage.getItem("start-date");
  end_date = localStorage.getItem("end-date");

  const loader = document.getElementById("loader");
  toggleClass(loader, ["flex"], ["hidden"]);

  const update_result = await updateSchedule(repList);
  if (update_result.status === "success") {
    showPopup(
      "",
      `Schedule changes for date range ${from_date} to ${end_date} is got updated and Calender event triggered`,
      "bg-[#8F58DD]"
    );
    toggleClass(loader, ["hidden"], ["flex"]);
    const calender_result = calenderevent({ assignments: calendereventList });

    const items = document.querySelectorAll(".handle-state-change");
    const parentElements = Array.from(items).map((item) => item.parentElement);

    parentElements.forEach((item) => {
      item.removeAttribute("style");
      const overlays = item.querySelectorAll(".overlay-blocker");
      if (overlays) {
        overlays.forEach((overlay) => overlay.remove());
      }
      const tooltips = document.querySelectorAll(".tooltip");
      if (tooltips) {
        tooltips.forEach((tooltip) => tooltip.remove());
      }
    });
    loadTechAllocation();
  } else {
    showPopup(
      "",
      `Schedule changes for date range ${from_date} to ${end_date} is got errored`,
      "bg-[#8F58DD]"
    );
  }
}
