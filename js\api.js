const fetchWithInterceptor = (() => {
  const baseFetch = window.fetch.bind(window); // Reference to the original fetch

  return async (url, options = {}) => {
    // Use "access_token" (consistent with the rest of the code)
    const token = localStorage.getItem("access_token");
    const defaultHeaders = {
      Authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
    };

    // Merge provided headers with default headers
    options.headers = { ...defaultHeaders, ...options.headers };

    try {
      // Make the API request using the original fetch
      let response = await baseFetch(url, options);

      // If a 401 Unauthorized response is returned, attempt to refresh the token
      if (response.status === 401) {
        const refreshResponse = await handleTokenRefresh();

        // Check if the refresh call succeeded
        if (refreshResponse.ok) {
          const data = await refreshResponse.json();

          // Verify that we received a new token
          if (data && data.access_token) {
            const newToken = data.access_token;
            localStorage.setItem("access_token", newToken);

            // Retry the original request with the new token
            options.headers.Authorization = `Bearer ${newToken}`;
            response = await baseFetch(url, options);
          } else {
            // If no token is provided in the response, logout
            handleLogout();
          }
        } else {
          // If refresh fails (e.g., refresh token expired), handle logout
          handleLogout();
        }
      }

      return response;
    } catch (error) {
      console.error("Fetch failed:", error);
      throw error;
    }
  };

  // Logout: clear tokens and redirect to login page
  function handleLogout() {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    window.location.href = "/login.html";
  }

  // Handle token refresh using the stored refresh token
  async function handleTokenRefresh() {
    const refreshToken = localStorage.getItem("refresh_token");
    return baseFetch(`${config.apiUrl}/refresh`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ refreshToken }),
    });
  }
})();

/* Generalized function for API requests using the fetch interceptor */
async function apiRequest(
  endpoint,
  method = "GET",
  body = null,
  requiresAuth = true,
  apiUrls = true
) {
  let url;
  if (apiUrls) {
    url = new URL(config.apiUrl + endpoint);
  } else {
    url = new URL(config.scheduler_apiUrl + endpoint);
  }

  const headers = {
    "Content-Type": "application/json",
  };

  // Include the access token if required
  if (requiresAuth) {
    const access_token = localStorage.getItem("access_token");
    if (!access_token) {
      localStorage.clear();
      redirectToLogin();
      return;
    }
    headers["Authorization"] = `Bearer ${access_token}`;
  }

  try {
    const requestBody = body ? JSON.stringify(body) : null;

    const response = await fetchWithInterceptor(url, {
      method,
      headers,
      body: requestBody,
    });

    if (!response.ok) {
      if (response.status === 403) {
        localStorage.clear();
        window.location.href = "/login.html";
        return;
      }

      const errorData = await response.json();
      const errorMessage =
        errorData.detail?.error_description ||
        errorData.errorMessage ||
        "Request failed";
      throw new Error(errorMessage);
    }

    const responseData = await response.json();

    return responseData;
  } catch (error) {
    console.error("[apiRequest] Error:", error);
    console.error("[apiRequest] Error details:", error.message);
    console.error("[apiRequest] Error stack:", error.stack);

    if (error.message.includes("token")) {
      localStorage.clear();
      window.location.href = "/login.html";
    } else {
      throw error;
    }
  }
}

/* Utility function to display unauthorized error messages */
function unauth(text, callback) {
  const errorText = document.getElementById("error-text");
  const errorMessage = document.getElementById("error-message");

  if (errorText) errorText.textContent = text;
  if (errorMessage) {
    errorMessage.style.display = "block";
    setTimeout(() => {
      errorMessage.style.display = "none";
      if (callback) callback(); // Execute the callback if provided
    }, 5000);
  }
}

/* Redirect users based on their roles */
function redirectToRoleBasedPage(accessToken) {
  const decodedToken = jwt_decode(accessToken);

  if (decodedToken.realm_roles) {
    if (decodedToken.realm_roles.includes("REP_ADVISOR")) {
      window.location.href = "/index.html";
    } else if (decodedToken.realm_roles.includes("ABSTRACTOR")) {
      window.location.href = "/abstractor/index.html";
    } else if (decodedToken.realm_roles.includes("CLINICAL_COORDINATOR")) {
      window.location.href = "/json-editor/index.html";
    } else {
      unauth(
        "You are not a Rep Advisor or an Abstractor - Unauthorized to Login",
        Apilogout
      );
    }
  } else {
    unauth("Invalid token: No roles found", Apilogout);
  }
}

/* Logout */
async function Apilogout() {
  const refreshToken = localStorage.getItem("refresh_token");
  if (!refreshToken) {
    console.error("Refresh token is missing. Cannot logout.");
    localStorage.clear();
    window.location.href = "/login.html";
    return;
  }

  try {
    await apiRequest("/logout", "POST", { refresh_token: refreshToken }, false);
    localStorage.clear();
    window.location.href = "/login.html";
  } catch (error) {
    console.error("Logout error:", error);
  }
}

/* Example API functions below (all using apiRequest) */

async function getprocedureType() {
  const { result } = await apiRequest("/procedure-type");
  return result;
}

async function getExperienceType() {
  const { result } = await apiRequest("/experience-type");
  return result;
}

async function createRepApiCall(repData) {
  const resp = await apiRequest("/users/reps", "POST", repData);
  return resp;
}

async function fetchschedulerdata(from_date, end_date) {
  const { result } = await apiRequest(
    `/schedule/cases?start_date=${from_date}&end_date=${end_date}`
  );
  return result;
}

async function fetchAbstractorData(start_date, end_date) {
  const { result } = await apiRequest(
    `/abstractor/patients?start_date=${start_date}&end_date=${end_date}`
  );
  return result;
}

async function fetchAbstractorTasks() {
  const { result } = await apiRequest(`/user/tasks`);
  return result;
}

async function fetchImplantingPhysicians(site_id) {
  try {
    const { result } = await apiRequest(
      `/sites/${site_id}/implanting-physicians`
    );

    return result;
  } catch (error) {
    console.error("[API] fetchImplantingPhysicians error:", error);
    throw error;
  }
}

async function createSite(data) {
  return await apiRequest("/sites", "POST", data);
}

async function updateSite(site_id, data) {
  console.log("[API] updateSite called with:");

  try {
    const result = await apiRequest(`/sites/${site_id}`, "PUT", data);

    return result;
  } catch (error) {
    console.error("[API] updateSite error:", error);
    throw error;
  }
}

async function createImplantingPhysician(data) {
  console.log("[API] === CREATE IMPLANTING PHYSICIAN API CALL ===");
  console.log(
    "[API] createImplantingPhysician called with data:",
    JSON.stringify(data, null, 2)
  );

  // Extract site_id from the data
  const site_id = data.site_id;
  if (!site_id) {
    console.error("[API] Error: site_id is required but not provided");
    throw new Error("site_id is required");
  }

  console.log("[API] Site ID:", site_id);
  console.log("[API] HTTP Method: POST");
  console.log(`[API] Endpoint: /sites/${site_id}/physicians`);

  try {
    console.log("[API] Making API request...");
    // Use the new endpoint format with site_id in the path
    const result = await apiRequest(
      `/sites/${site_id}/physicians`,
      "POST",
      data
    );
    console.log("[API] API request completed successfully");
    console.log(
      "[API] createImplantingPhysician response:",
      JSON.stringify(result, null, 2)
    );
    return result;
  } catch (error) {
    console.error("[API] createImplantingPhysician error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

async function fetchSpecificImplantingPhysician(physician_id) {
  console.log("[API] === FETCH SPECIFIC IMPLANTING PHYSICIAN API CALL ===");

  // Validate physician_id
  if (
    !physician_id ||
    physician_id === "undefined" ||
    physician_id === undefined
  ) {
    const error = new Error(
      "Invalid physician ID provided to fetchSpecificImplantingPhysician"
    );
    console.error("[API] Error:", error.message);
    console.error("[API] Physician ID:", physician_id);
    throw error;
  }

  console.log("[API] Physician ID:", physician_id);
  console.log(`[API] Endpoint: /sites/physicians/${physician_id}`);
  console.log("[API] HTTP Method: GET");

  try {
    console.log("[API] Making API request to fetch specific physician...");
    const response = await apiRequest(
      `/sites/physicians/${physician_id}`,
      "GET"
    );
    console.log("[API] API request completed successfully");
    console.log(
      "[API] fetchSpecificImplantingPhysician full response:",
      JSON.stringify(response, null, 2)
    );

    const result = response.result;

    // If we have a site_id in the result, store it in sessionStorage
    if (result && result.site_id) {
      console.log("[API] Found site_id in physician data:", result.site_id);
      sessionStorage.setItem("currentSiteId", result.site_id);
      sessionStorage.setItem("lastSelectedSiteId", result.site_id);
      console.log("[API] Stored site_id in sessionStorage:", result.site_id);
    }

    return result;
  } catch (error) {
    console.error("[API] fetchSpecificImplantingPhysician error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

// Function to get the site ID for a physician
async function getPhysicianSiteId(physician_id) {
  console.log("[API] === GET PHYSICIAN SITE ID API CALL ===");

  // Validate physician_id
  if (
    !physician_id ||
    physician_id === "undefined" ||
    physician_id === undefined
  ) {
    const error = new Error(
      "Invalid physician ID provided to getPhysicianSiteId"
    );
    console.error("[API] Error:", error.message);
    console.error("[API] Physician ID:", physician_id);
    throw error;
  }

  console.log("[API] Physician ID:", physician_id);
  console.log(`[API] Endpoint: /sites/physicians/${physician_id}`);
  console.log("[API] HTTP Method: GET");

  try {
    console.log("[API] Making API request to get physician site ID...");
    const response = await apiRequest(
      `/sites/physicians/${physician_id}`,
      "GET"
    );
    console.log("[API] API request completed successfully");

    const result = response.result;

    if (result && result.site_id) {
      console.log("[API] Found site_id in physician data:", result.site_id);
      sessionStorage.setItem("currentSiteId", result.site_id);
      sessionStorage.setItem("lastSelectedSiteId", result.site_id);
      console.log("[API] Stored site_id in sessionStorage:", result.site_id);
      return result.site_id;
    } else {
      console.error("[API] No site_id found in physician data");
      return null;
    }
  } catch (error) {
    console.error("[API] getPhysicianSiteId error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

async function updateImplantingPhysician(physician_id, data) {
  console.log("[API] === UPDATE IMPLANTING PHYSICIAN API CALL ===");
  console.log("[API] Physician ID:", physician_id);
  console.log(
    "[API] updateImplantingPhysician called with data:",
    JSON.stringify(data, null, 2)
  );
  console.log("[API] HTTP Method: PUT");
  console.log(`[API] Endpoint: /sites/physicians/${physician_id}`);

  // Store the site_id in sessionStorage if it's valid
  if (data.site_id && data.site_id !== "undefined" && data.site_id !== "null") {
    console.log("[API] Storing site_id in sessionStorage:", data.site_id);
    sessionStorage.setItem("currentSiteId", data.site_id);
  }

  try {
    console.log("[API] Making API request...");
    const result = await apiRequest(
      `/sites/physicians/${physician_id}`,
      "PUT",
      data
    );
    console.log("[API] API request completed successfully");
    console.log(
      "[API] updateImplantingPhysician response:",
      JSON.stringify(result, null, 2)
    );
    return result;
  } catch (error) {
    console.error("[API] updateImplantingPhysician error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

async function fetchschedulerspecificdata(from_date, to_date) {
  let url = `/schedule/reps/cases?start_date=${from_date}`;
  if (to_date) {
    url += `&end_date=${to_date}`;
  }

  const { result } = await apiRequest(url);
  return result;
}

async function fetchActiveReps() {
  const { result } = await apiRequest("/schedule/reps");
  return result;
}

async function fetchActiveProviders() {
  const { result } = await apiRequest("/schedule/implanting-physicians");
  return result;
}

async function fetchUnscheduledTechs(date) {
  const { result } = await apiRequest(
    `/schedule/reps/available?case_date=${date}`
  );
  return result;
}

async function updateSchedule(data) {
  return await apiRequest("/schedule/cases", "PUT", data);
}

async function triggerRescheduler(start_date, end_date) {
  return await apiRequest(
    `/schedule/reps?start_date=${start_date}&end_date=${end_date}`,
    "GET",
    null,
    true,
    false
  );
}

async function checkJobStatus(job_id) {
  return await apiRequest(`/schedule/reps/${job_id}`, "GET", null, true, false);
}

async function fetchRepSkillSet(rep_id) {
  const { result } = await apiRequest(`/schedule/reps/${rep_id}`);
  return result;
}

async function fetchproviderandexperience() {
  const { result } = await apiRequest(`/schedule/site-procedure-experience`);
  return [result];
}

async function fetchProviderSkillSet(provider_id) {
  const { result } = await apiRequest(
    `/schedule/implanting-physicians/${provider_id}`
  );
  return result;
}

async function updaterepskillset(data) {
  return await apiRequest(`/schedule/reps/${data.id}`, "PUT", data);
}

async function updateproviderskillset(data) {
  return await apiRequest(
    `/schedule/implanting-physicians/${data.id}`,
    "PUT",
    data
  );
}

async function calenderevent(data) {
  return await apiRequest(`/calendar/events/create`, "POST", data, true, false);
}

async function getAllSites() {
  try {
    const { result } = await apiRequest(`/sites`);
    return result;
  } catch (error) {
    console.error("[API] getAllSites error:", error);
    throw error;
  }
}

async function fetchSpecificSite(site_id) {
  console.log("[API] === FETCH SPECIFIC SITE API CALL ===");

  // Validate site_id
  if (!site_id || site_id === "undefined" || site_id === undefined) {
    const error = new Error("Invalid site ID provided to fetchSpecificSite");
    console.error("[API] Error:", error.message);
    console.error("[API] Site ID:", site_id);
    throw error;
  }

  console.log("[API] Site ID:", site_id);
  console.log(`[API] Endpoint: /sites/${site_id}`);
  console.log("[API] HTTP Method: GET");

  try {
    console.log("[API] Making API request to fetch specific site...");
    const { result } = await apiRequest(`/sites/${site_id}`, "GET");
    console.log("[API] API request completed successfully");
    console.log(
      "[API] fetchSpecificSite response:",
      JSON.stringify(result, null, 2)
    );
    return result;
  } catch (error) {
    console.error("[API] fetchSpecificSite error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

async function getCredentials() {
  console.log("[API] === FETCH PROVIDER CREDENTIALS API CALL ===");
  console.log(`[API] Endpoint: /sites/provider/credentials`);
  console.log("[API] HTTP Method: GET");

  try {
    console.log("[API] Making API request to fetch provider credentials...");
    const response = await apiRequest(`/sites/provider/credentials`, "GET");
    console.log("[API] API request completed successfully");
    console.log(
      "[API] getCredentials full response:",
      JSON.stringify(response, null, 2)
    );

    // Extract the result array from the response
    const result = response && response.result ? response.result : [];

    console.log(
      "[API] getCredentials extracted result:",
      JSON.stringify(result, null, 2)
    );

    // For debugging, log each credential
    if (Array.isArray(result)) {
      result.forEach((credential, index) => {
        console.log(
          `[API] Credential ${index + 1}: id=${credential.id}, name=${
            credential.name
          }`
        );
      });
    }

    return response; // Return the full response to let the caller extract what they need
  } catch (error) {
    console.error("[API] getCredentials error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

async function fetchProviderExperience() {
  console.log("[API] === FETCH PROVIDER EXPERIENCE API CALL ===");
  console.log(`[API] Endpoint: /sites/provider/experience`);
  console.log("[API] HTTP Method: GET");

  try {
    console.log(
      "[API] Making API request to fetch provider experience options..."
    );
    const response = await apiRequest(`/sites/provider/experience`, "GET");
    console.log("[API] API request completed successfully");
    console.log(
      "[API] fetchProviderExperience full response:",
      JSON.stringify(response, null, 2)
    );

    // Extract the result array from the response
    const result = response && response.result ? response.result : [];

    console.log(
      "[API] fetchProviderExperience extracted result:",
      JSON.stringify(result, null, 2)
    );

    // For debugging, log each experience option with detailed type information
    if (Array.isArray(result)) {
      result.forEach((experience, index) => {
        console.log(
          `[API] Experience ${index + 1}: id=${
            experience.id
          } (type: ${typeof experience.id}), ` +
            `name=${experience.name} (type: ${typeof experience.name}), ` +
            `value=${experience.value} (type: ${typeof experience.value})`
        );
      });
    }

    return response; // Return the full response to let the caller extract what they need
  } catch (error) {
    console.error("[API] fetchProviderExperience error:", error);
    console.error("[API] Error details:", error.message || error);
    console.error(
      "[API] Error stack:",
      error.stack || "No stack trace available"
    );
    throw error;
  }
}

async function getPatientsApi(site_id, date) {
  const { result } = await apiRequest(
    `/reps/schedule/sites/${site_id}/cases/${date}/rep-unassigned`
  );
  return result;
}

async function scheduleCase(case_id) {
  const { result } = await apiRequest(`/reps/schedule/cases/${case_id}`, "PUT");
  return result;
}

async function getAuthUrl() {
  const url = `${config.apiUrl}/auth?redirect_url=${config.redirect_url}`;
  window.location.href = url;
}

async function getAccessToken() {
  loginLoader(true);
  const queryParams = new URLSearchParams(window.location.search);
  const code = queryParams.get("code");

  if (code) {
    try {
      // Get access token and refresh token
      const { result } = await apiRequest(
        `/code?redirect_url=${config.redirect_url}&code=${code}`,
        "GET",
        null,
        false
      );
      const { refresh_token, access_token } = result;
      localStorage.setItem("refresh_token", refresh_token);
      localStorage.setItem("access_token", access_token);

      // Fetch user profile after obtaining access token
      const userProfileResponse = await fetchWithInterceptor(
        `${config.apiUrl}/users/profile`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${access_token}`,
          },
        }
      );
      const userProfile = await userProfileResponse.json();
      const userId = userProfile.result.user_id;
      localStorage.setItem("user_id", userId);

      redirectToRoleBasedPage(access_token);
    } catch (error) {
      unauth(error.message);
    } finally {
      loginLoader(false);
    }
  } else {
    unauth("Authorization code not found.");
    loginLoader(false);
  }
}

// Export all API functions to the window object
window.apiRequest = apiRequest;
window.getAllSites = getAllSites;
window.fetchImplantingPhysicians = fetchImplantingPhysicians;
window.fetchSpecificImplantingPhysician = fetchSpecificImplantingPhysician;
window.getPhysicianSiteId = getPhysicianSiteId;
window.createImplantingPhysician = createImplantingPhysician;
window.updateImplantingPhysician = updateImplantingPhysician;
window.fetchschedulerspecificdata = fetchschedulerspecificdata;
window.fetchschedulerdata = fetchschedulerdata;
window.fetchActiveReps = fetchActiveReps;
window.fetchActiveProviders = fetchActiveProviders;
window.fetchUnscheduledTechs = fetchUnscheduledTechs;
window.updateSchedule = updateSchedule;
window.triggerRescheduler = triggerRescheduler;
window.checkJobStatus = checkJobStatus;
window.fetchRepSkillSet = fetchRepSkillSet;
window.fetchProviderSkillSet = fetchProviderSkillSet;
window.updaterepskillset = updaterepskillset;
window.updateproviderskillset = updateproviderskillset;
window.calenderevent = calenderevent;
window.getPatientsApi = getPatientsApi;
window.scheduleCase = scheduleCase;
window.getAuthUrl = getAuthUrl;
window.getAccessToken = getAccessToken;
window.getCredentials = getCredentials;
window.fetchProviderExperience = fetchProviderExperience;
