let defaultActiveDate = null;
let activeNav = null;
let activeContent = null;
let activeTechInitialSkillSet = null;
let activeTechCurrentSkillSet = null;
let isSkillSetChanged = false;
let activerepprovider = null;
let activerole = null;
let predictedTechs = null;
let isScheduleChanged = false;
let removedTechs = [];
let predictedDatacount;
let finalpredictedDatacount = [];
let skillSetPlaceholderText = `
    <div class="flex h-full w-full justify-center items-center">
        <p class="font-semibold text-[#8F58DD]">Click to view their Skill Sets...</p>
    </div>
`;

function checkTokenAndRedirect() {
  const token = localStorage.getItem("access_token");

  if (
    !token &&
    window.location.pathname !== "/login.html" &&
    window.location.pathname !== "/callback.html"
  ) {
    localStorage.clear();
    window.location.href = "/login.html";
  }
}

checkTokenAndRedirect();

function setActiveNav(activeNavParam) {
  activeNav = activeNavParam;
  document.getElementById("page-title").innerHTML =
    activeNav === "weekly schedule" ? "schedule" : activeNav;
  localStorage.setItem("activeNav", activeNav);
  document.querySelectorAll(".nav-link").forEach((link) => {
    const isActive = link.getAttribute("data-nav") === activeNav;
    link.classList.toggle("bg-[#8F58DD]", isActive);
    link.classList.toggle("text-white", isActive);
    const img = link.querySelector("img");
    if (img) {
      let currentSrc = img.src;
      if (isActive && !currentSrc.includes("-active.svg")) {
        currentSrc = currentSrc.replace(".svg", "-active.svg");
      } else if (!isActive && currentSrc.includes("-active.svg")) {
        currentSrc = currentSrc.replace("-active.svg", ".svg");
      }
      img.src = currentSrc;
    }
  });
}

function setDefaultActiveDate(date) {
  defaultActiveDate = date;
}

function setVariablesToDefault() {
  predictedTechs =
    activeTechInitialSkillSet =
    activeTechCurrentSkillSet =
    activerepprovider =
      null;
  isScheduleChanged = isSkillSetChanged = false;
}

function setVisibilityForContents() {
  switch (activeNav) {
    case "weekly schedule":
      document.getElementById("date-range-section").classList.add("hidden");
      document.getElementById("date-range-section").classList.add("md:block");
      document.getElementById("rerunScheduleButton").classList.add("hidden");
      document.getElementById("updateSkillSetDiv")?.remove();
      document.getElementById("reviewScheduleButton").classList.add("hidden");
      break;
    case "assignments":
      document.getElementById("date-range-section").classList.remove("hidden");
      document.getElementById("rerunScheduleButton").classList.remove("hidden");
      document.getElementById("updateSkillSetDiv")?.remove();
      document.getElementById("reschedulereview").classList.add("hidden");
      document.getElementById("reviewScheduleButton").classList.add("hidden");
      document
        .getElementById("dateButtonsContainer")
        .classList.remove("hidden");
      document.getElementById("schedulesContainer").classList.remove("hidden");
      break;
    case "settings":
      document.getElementById("date-range-section").classList.add("hidden");
      document
        .getElementById("date-range-section")
        .classList.remove("md:block");
      document.getElementById("rerunScheduleButton").classList.add("hidden");
      document.getElementById("repAllocation").classList.add("hidden");
      document.getElementById("techCalendar").classList.add("hidden");
      document.getElementById("repproviderList").classList.add("hidden");
      document.getElementById("loader").classList.remove("hidden");
      document.getElementById("updateSkillSetDiv")?.remove();
      document.getElementById("reviewScheduleButton").classList.add("hidden");
      break;
  }
}

function removeLoader() {
  document.getElementById("loader").classList.add("hidden");
}

function toggleLoader(loader, show) {
  if (show) {
    loader.style.display = "flex";
  } else {
    loader.style.display = "none";
  }
}

function toggleContentLoader(show) {
  const contentLoader = document.getElementById("content-loader");
  if (contentLoader) {
    if (show) {
      contentLoader.style.display = "flex";
    } else {
      contentLoader.style.display = "none";
    }
  }
}

function toggleClass(element, add, remove) {
  element.classList.add(...add);
  element.classList.remove(...remove);
}

async function changeactivetab() {
  setlocalstoragedate();
  const activeNav = localStorage.getItem("activeNav");
  switch (activeNav) {
    case "weekly schedule":
      loadCalendar();
      break;
    case "assignments":
      loadTechAllocation();
      break;
  }
}

document.addEventListener("DOMContentLoaded", async () => {
  $("#techCalendar").load("calendar.html");
  const flatpickrInstance = flatpickr("#from-date", {
    mode: "range",
    dateFormat: "Y-m-d",
    defaultDate: [getMonday(), getSaturday()],
    onChange: function (selectedDates, dateStr, instance) {
      changeactivetab();
    },
    disable: [
      // Disable weekend dates (Saturday and Sunday)
      function (date) {
        return date.getDay() === 0 || date.getDay() === 6; // 0 is Sunday, 6 is Saturday
      },
    ],
  });
  setlocalstoragedate();

  // Event listeners for navigation buttons
  document.getElementById("prev-week").addEventListener("click", (event) => {
    event.preventDefault();
    const selectedDates = flatpickrInstance.selectedDates;
    const startDate =
      selectedDates && selectedDates.length ? selectedDates[0] : getMonday();
    const prevWeek = new Date(startDate);
    prevWeek.setDate(startDate.getDate() - 7); // Go back 7 days
    // updateDateRange(selectedDates[0], true);
    updateDateRange(prevWeek);
  });

  document.getElementById("next-week").addEventListener("click", (event) => {
    event.preventDefault();
    const selectedDates = flatpickrInstance.selectedDates;
    const startDate =
      selectedDates && selectedDates.length ? selectedDates[0] : getMonday();
    const nextWeek = new Date(startDate);
    nextWeek.setDate(startDate.getDate() + 7); // Go forward 7 days
    // updateDateRange(nextWeek, false);
    updateDateRange(nextWeek, false);
  });

  // function updateDateRange(startDate, flag) {
  //   if (flag) {
  //     startDate = getMonday(startDate);
  //   }
  //   const friday = getSaturdaydate(startDate);
  //   flatpickrInstance.setDate([startDate, friday]);
  //   changeactivetab();
  // }
  function updateDateRange(startDate) {
    const monday = getMonday(startDate);
    const friday = getSaturday(monday);
    flatpickrInstance.setDate([monday, friday]);
    changeactivetab();
  }

  const loader = document.getElementById("loader");

  activeNav = localStorage.getItem("activeNav");

  document.getElementById("repallocationsNav").addEventListener("click", () => {
    loadTechAllocation();
  });
  document
    .getElementById("repallocationsNav-sidemenu")
    .addEventListener("click", () => {
      loadTechAllocation();
    });
  document.getElementById("calendarNav").addEventListener("click", (event) => {
    event.preventDefault();
    loadCalendar();
  });
  document
    .getElementById("calendarNav-sidemenu")
    .addEventListener("click", (event) => {
      event.preventDefault();
      loadCalendar();
    });
  document
    .getElementById("repprovidersNav")
    .addEventListener("click", (event) => {
      event.preventDefault();
      loadTechSkillSet();
    });
  document
    .getElementById("repprovidersNav-sidemenu")
    .addEventListener("click", (event) => {
      event.preventDefault();
      loadTechSkillSet();
    });

  from_date = localStorage.getItem("start-date");
  end_date = localStorage.getItem("end-date");

  if (
    !from_date ||
    from_date === "undefined" ||
    !end_date ||
    end_date === "undefined"
  ) {
    console.error("Invalid date range selected.");
    return;
  }

  result_data = await fetchschedulerdata(from_date, end_date);
  window.data = result_data["cases_details"];
  loader.classList.add("hidden");

  if (activeNav) {
    switch (activeNav) {
      case "weekly schedule":
        loadCalendar();
        break;
      case "assignments":
        loadTechAllocation();
        break;
      case "settings":
        loadTechSkillSet();
        break;
      default:
        loadCalendar();
        break;
    }
  } else {
    loadCalendar();
  }
});

function loginLoader(show) {
  document.getElementById("main-loader").classList.toggle("hidden", !show);
}

document.querySelectorAll(".input-field").forEach((field) => {
  field.addEventListener("animationend", () => field.classList.remove("shake"));
});

function triggerShake(field) {
  field.classList.remove("shake");
  void field.offsetWidth; // Trigger reflow to restart animation
  field.classList.add("shake");
}

async function login() {
  // const username = document.getElementById("username");
  // const password = document.getElementById("password");

  // let fields = [username, password];
  // let hasError = fields.some(field => {
  //     if (!field.value) {
  //         triggerShake(field);
  //         return true;
  //     }
  //     return false;
  // });

  // if (hasError) {
  //     return;  // If there's an error (empty fields), stop the login process.
  // }

  // Show loader before making the API request
  loginLoader(true);

  // // Prepare login data
  // const loginData = {
  //     username: username.value,
  //     password: password.value,
  // };

  // Apilogin(loginData)
  await getAuthUrl();
}

function logout() {
  Apilogout();
}

function setlocalstoragedate() {
  const dateField = document.getElementById("from-date");
  const selectedRange = dateField?.value;
  let [from_date, end_date] = selectedRange
    ? selectedRange.split(" to ")
    : [calender_formatDate(getMonday()), calender_formatDate(getSaturday())];
  if (typeof end_date === "undefined") {
    end_date = from_date;
  }
  localStorage.setItem("start-date", from_date);
  localStorage.setItem("end-date", end_date);

  const today = new Date();
  const formattedDate = today.toISOString().split("T")[0];
  const reschedulebutton = document.getElementById("rerunScheduleButton");
  if (new Date(from_date) >= new Date(formattedDate)) {
    reschedulebutton.disabled = false;
    reschedulebutton.classList.add("hover:bg-purple-400");
    reschedulebutton.title = "Click to reschedule";
  } else {
    reschedulebutton.disabled = true;
    reschedulebutton.classList.remove("hover:bg-purple-400");
    reschedulebutton.title = "Cannot reschedule for the selected date";
  }
}

function getMonday(date = new Date()) {
  const day = date.getDay();
  const diff = day === 0 ? -6 : 1 - day;
  const monday = new Date(date);
  // monday.setDate(monday.getDate() + diff - 1);
  monday.setDate(monday.getDate() + diff);
  return monday;
}

function getSaturday(date = new Date()) {
  const monday = getMonday(date);
  const friday = new Date(monday);
  // friday.setDate(friday.getDate() + 6);
  friday.setDate(friday.getDate() + 4);
  return friday;
}

// function getSaturdaydate(date = new Date()) {
//   const saturday = new Date(date);
//   saturday.setDate(saturday.getDate() + 6);
//   return saturday;
// }

function calender_formatDate(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
}

function opensidemenu() {
  const sidebar = document.getElementById("side-menu");
  toggleClass(sidebar, ["block"], ["hidden"]);

  // Check if we need to refresh the rep list in mobile view
  if (window.mobileViewNeedsRefresh) {
    console.log("Mobile view needs refresh, reloading rep list");
    // Reset the flag
    window.mobileViewNeedsRefresh = false;

    // If we're in the settings tab, refresh the rep list
    if (activeNav === "settings") {
      // Use setTimeout to ensure the sidebar is fully open before refreshing
      setTimeout(async () => {
        try {
          // Fetch the updated list of reps
          const activeReps = await fetchActiveReps();

          // Update the rep list in the UI
          updateTechList("techsNames", "Clinical Specialist", activeReps);
        } catch (error) {
          console.error("Error refreshing rep list in mobile view:", error);
        }
      }, 200);
    }
  }

  // Add event listener to close sidebar when clicking outside
  // Use setTimeout to prevent the event that opened the sidebar from closing it immediately
  setTimeout(() => {
    document.addEventListener("click", closeSidebarOnClickOutside);
  }, 100);
}

function closesidebar() {
  const sidebar = document.getElementById("side-menu");
  toggleClass(sidebar, ["hidden"], ["block"]);

  // Remove event listener when sidebar is closed
  document.removeEventListener("click", closeSidebarOnClickOutside);
}

function closeSidebarOnClickOutside(event) {
  const sidebar = document.getElementById("side-menu");
  const sidebarContent = sidebar.querySelector(".fixed.right-0");
  const hamburgerMenu = document.querySelector('img[src="/svg/menu.svg"]');

  // Only close if clicking on the overlay (outside the sidebar content)
  // and not on the hamburger menu
  if (
    sidebar.classList.contains("block") &&
    !sidebarContent.contains(event.target) &&
    event.target !== sidebarContent &&
    event.target !== hamburgerMenu
  ) {
    closesidebar();
  }
}

document.addEventListener("DOMContentLoaded", () => {
  const container = document.querySelector(".main-techs-container");

  if (container) {
    // Create a visible tab element for better control
    const tabElement = document.createElement("div");
    tabElement.className = "sidebar-toggle-tab";
    tabElement.style.cssText = `
      position: absolute;
      top: 50%;
      right: 0px;
      width: 10px;
      height: 50px;
      background-color: #8F58DD;
      border-radius: 0 5px 5px 0;
      transform: translateY(-50%);
      cursor: pointer;
      z-index: 1001;
    `;
    container.appendChild(tabElement);

    // Add click handler to the tab element
    tabElement.addEventListener("click", (event) => {
      event.stopPropagation(); // Prevent event from bubbling to document

      // Toggle the sidebar open/close state
      if (container.classList.contains("open")) {
        container.classList.remove("open");
      } else {
        container.classList.add("open");
      }
    });

    // Add a click handler to the document to close the sidebar when clicking outside
    document.addEventListener("click", (event) => {
      // If the sidebar is open and the click is outside the sidebar, close it
      if (
        container.classList.contains("open") &&
        !container.contains(event.target) &&
        event.target !== tabElement
      ) {
        container.classList.remove("open");
      }
    });

    // Hide the tab in desktop view
    const mediaQuery = window.matchMedia("(min-width: 768px)");
    const handleMediaChange = (e) => {
      if (e.matches) {
        tabElement.style.display = "none";
      } else {
        tabElement.style.display = "block";
      }
    };

    // Initial check
    handleMediaChange(mediaQuery);

    // Add listener for screen size changes
    mediaQuery.addEventListener("change", handleMediaChange);

    // Add touch gesture support for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    let initialDistance = 0;

    // Create a touch overlay for the left edge of the screen
    const touchOverlay = document.createElement("div");
    touchOverlay.className = "sidebar-touch-overlay";
    touchOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 20px;
      height: 100%;
      z-index: 999;
      background-color: transparent;
    `;
    document.body.appendChild(touchOverlay);

    // Handle swipe from left edge to open sidebar
    touchOverlay.addEventListener("touchstart", (e) => {
      touchStartX = e.touches[0].clientX;

      // If there are two touches (pinch), record the initial distance
      if (e.touches.length === 2) {
        initialDistance = Math.abs(e.touches[0].clientX - e.touches[1].clientX);
      }
    });

    touchOverlay.addEventListener("touchmove", (e) => {
      // For swipe gesture
      if (e.touches.length === 1) {
        touchEndX = e.touches[0].clientX;
        const swipeDistance = touchEndX - touchStartX;

        // If swiping right and sidebar is closed, show a preview
        if (swipeDistance > 20 && !container.classList.contains("open")) {
          // Add dragging class for visual feedback
          container.classList.add("dragging");

          const previewAmount = Math.min(swipeDistance, 256);
          container.style.left = `${-256 + previewAmount}px`;
        }
      }

      // For pinch gesture
      if (e.touches.length === 2) {
        const currentDistance = Math.abs(
          e.touches[0].clientX - e.touches[1].clientX
        );

        // If pinching outward and near the left edge
        if (
          currentDistance > initialDistance + 50 &&
          e.touches[0].clientX < 100
        ) {
          container.classList.add("open");
          container.classList.remove("dragging");
          container.style.left = "0px";
        }
      }
    });

    touchOverlay.addEventListener("touchend", () => {
      // Remove dragging class
      container.classList.remove("dragging");

      // For swipe gesture
      if (touchEndX - touchStartX > 100) {
        // If swiped far enough to the right, open the sidebar
        container.classList.add("open");
        container.style.left = "";
      } else {
        // Otherwise, reset to closed position
        container.classList.remove("open");
        container.style.left = "";
      }

      // Reset touch tracking
      touchStartX = 0;
      touchEndX = 0;
      initialDistance = 0;
    });

    // Add drag gesture support for the entire document
    document.addEventListener("touchstart", (e) => {
      // Only track touches that start near the left edge
      if (e.touches[0].clientX < 30) {
        touchStartX = e.touches[0].clientX;
      }
    });

    document.addEventListener(
      "touchmove",
      (e) => {
        // Only process if we started near the left edge
        if (touchStartX > 0 && touchStartX < 30) {
          touchEndX = e.touches[0].clientX;
          const swipeDistance = touchEndX - touchStartX;

          // If swiping right and sidebar is closed, show a preview
          if (swipeDistance > 20 && !container.classList.contains("open")) {
            // Add dragging class for visual feedback
            container.classList.add("dragging");

            const previewAmount = Math.min(swipeDistance, 256);
            container.style.left = `${-256 + previewAmount}px`;
            e.preventDefault(); // Prevent page scrolling while dragging
          }
        }
      },
      { passive: false }
    );

    document.addEventListener("touchend", () => {
      // Only process if we were tracking a touch
      if (touchStartX > 0) {
        // Remove dragging class
        container.classList.remove("dragging");

        if (touchEndX - touchStartX > 100) {
          // If swiped far enough to the right, open the sidebar
          container.classList.add("open");
          container.style.left = "";
        } else {
          // Otherwise, reset to closed position
          container.classList.remove("open");
          container.style.left = "";
        }

        // Reset touch tracking
        touchStartX = 0;
        touchEndX = 0;
      }
    });
  }
});

function weekCard(item) {
  // Create main container
  const mainDiv = document.createElement("div");
  mainDiv.className = "flex mt-5 mx-1 flex-col";

  // Create first child div
  const topDiv = document.createElement("div");
  topDiv.className = "w-full p-2 px-4 bg-[#F1F1FB] rounded-t-lg";

  // Create header container
  const headerDiv = document.createElement("div");
  headerDiv.className = "flex flex-row items-center justify-between";

  // Create site name element
  const siteName = document.createElement("h1");
  siteName.className = "text-sm font-bold text-ellipsis";
  siteName.textContent = item.site.name;

  // Append site name to header
  headerDiv.appendChild(siteName);

  // Add header to topDiv
  topDiv.appendChild(headerDiv);

  // Create rep name paragraph
  const repName = document.createElement("p");
  repName.className = "text-md font-normal";
  repName.textContent = item.rep.name;

  // Add rep name to topDiv
  topDiv.appendChild(repName);

  // Create cases container
  const casesDiv = document.createElement("div");
  casesDiv.className =
    "flex text-purple-600 flex-row items-center justify-start mt-3 gap-x-2";

  // Add user icon
  const userIcon = document.createElement("i");
  userIcon.className = "fa-solid fa-user";
  casesDiv.appendChild(userIcon);

  // Add cases text
  const casesText = document.createElement("p");
  casesText.textContent =
    item.cases === 1 ? `case: ${item.cases}` : `cases: ${item.cases}`;
  casesDiv.appendChild(casesText);

  // Add casesDiv to topDiv
  topDiv.appendChild(casesDiv);

  // Create second child div
  const bottomDiv = document.createElement("div");
  bottomDiv.className =
    "bg-purple-200 rounded-b-lg flex flex-row p-2 px-4 items-center justify-between";

  // Create implanting physician name
  const physicianName = document.createElement("h1");
  physicianName.className = "text-md font-bold";
  physicianName.textContent = item.implanting_physician.name;

  // Add physician name to bottomDiv
  bottomDiv.appendChild(physicianName);

  // Add both child divs to main container
  mainDiv.appendChild(topDiv);
  mainDiv.appendChild(bottomDiv);

  return mainDiv;
}
const rep = document.getElementById("repproviderSkillSet");
$(rep).load("repform.html");
