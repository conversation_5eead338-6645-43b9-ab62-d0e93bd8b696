{"follow_up_medications": {"verified": "", "medications": {"field_id": "11990", "label": "Medication", "elements": {"fondaparinux": {"field_id": "14949", "label": "Fondaparinux", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "heparin_derivative": {"field_id": "14949", "label": "Heparin Derivative", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "low_molecular_weight_heparin": {"field_id": "14949", "label": "Low Molecular Weight Heparin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "unfractionated_heparin": {"field_id": "14949", "label": "Unfractionated Heparin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "warfarin": {"field_id": "14949", "label": "Warfarin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "aspirin": {"field_id": "14949", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "if_yes": {"dose": {"field_id": "14950", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "81 - 100 MG"}, {"id": 2, "value": "101 - 324 MG"}, {"id": 3, "value": "325 MG"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the category of the medication dose for the follow-up? Please select from the following options: Aspirin 81 to 100 mg or Aspirin 101 to 324 mg or Aspirin 325 mg", "description": "\n**Coding Instruction:**\nIndicate the category of the medication dose.\n**Target Value:**\nThe value on follow-up"}}, "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "aspirin_dipyridamole": {"field_id": "14949", "label": "Aspirin/Dipyridamole", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "vorapaxar": {"field_id": "14949", "label": "Vorapaxar", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "apixaban": {"field_id": "14949", "label": "Apixaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "dabigatran": {"field_id": "14949", "label": "Dabigatran", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "edoxaban": {"field_id": "14949", "label": "Edoxaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "rivaroxaban": {"field_id": "14949", "label": "Rivaroxaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "cangrelor": {"field_id": "14949", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "clopidogrel": {"field_id": "14949", "label": "Clopidogrel", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "other_p2y12": {"field_id": "14949", "label": "Other P2Y12", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "prasugrel": {"field_id": "14949", "label": "Prasug<PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "ticagrelor": {"field_id": "14949", "label": "Ticagrelor", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}, "ticlopidine": {"field_id": "14949", "label": "Ticlopidine", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or being administered at the time of follow-up, or not taking or being administered for an undocumented, medical, or patient reason? Please specify if the patient was taking the medication (Yes), not taking the medication for no reason (No - No Reason), not taking the medication for a medical reason (No - Medical Reason), or not taking the medication for a patient reason (No - Patient Reason).", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.\n**Target Value:**\nThe value on Follow-up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications were prescribed or received by the patient during follow-up? Please provide the assigned identification numbers associated with each medication from the following list:Fondaparinux,Heparin Derivative,Low Molecular Weight Heparin,Unfractionated Heparin,Warfarin,Aspirin,Aspirin/Dipyridamole,Vorapaxar,Apixaban,Dabigatran,Edoxaban,Rivaroxaban,Cangrelor,Clopidogrel,Other P2Y12,Prasugrel,Ticagrelor,Ticlopidine", "description": "\n**Coding Instruction:**\nIndicate the assigned identification number associated with the medications the patient was prescribed or received.\n**Target Value:**\nN/A"}}}