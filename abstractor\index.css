body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f8f6ff;
}

html {
  scroll-behavior: smooth;
}

h1 {
  text-align: center;
  color: #333;
  font-size: 28px;
}

h2 {
  text-align: left;
  margin-left: 15px;
  color: #333;
  font-size: 24px;
}

.title {
  text-align: center;
  color: #8143d9;
  font-size: 28px;
}

.save-button-container {
  display: flex;
  justify-content: end;
  margin-top: 20px;
  margin-right: 80px;
}
.save-button {
  background-color: #8143d9;
  color: white;
  border: none;
  text-align: center;
  padding: 10px;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  width: 100px;
  border-radius: 4px;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #8143d9;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  background-color: #8143d9;
  animation: spin 2s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

#container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.field-container {
  margin-bottom: 20px;
  background: #fff;
  padding: 15px;
  padding-right: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

label {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

input {
  width: 98%;
  padding: 10px;
  margin-top: 5px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
select {
  width: 100%;
  padding: 10px;
  margin-top: 5px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

input[type="radio"] {
  width: auto;
  margin-right: 10px;
}

.date-picker-label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #8143d9;
}

.patient-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: red; /* Change the color as needed */
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
}
.flatpickr-calendar .patient-data {
  background-color: #8143d9; /* your desired color */
  border-radius: 10%;
  width: 12px;
  height: 4px;
  margin: 0 auto;
}

.date-picker-label {
  font-weight: 600;
  color: #4a5568; /* Tailwind's gray-700 */
}

.selected {
  background-color: #a855f7; /* Adjust color to match hover effect */
}

.search-bar {
  padding: 10px;
  margin-bottom: 20px;
  padding-left: 35px; /* Ensure space for the search icon */
  border: 1px solid #ccc;
  border-radius: 5px;
  width: 100%;
}

.search-bar::placeholder {
  font-size: 14px;
  color: #666;
}

.search-icon {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%); /* Align properly */
  font-size: 16px;
  color: #8143d9;
}

.radio-container {
  display: flex;
  flex-wrap: wrap;
}

.radio-container div {
  margin-right: 15px;
}

.accordion-container {
  margin: 20px auto;
  max-width: 1024px;
}

.accordion-item {
  border: 1px solid #ddd;
  margin-bottom: 10px;
  border-radius: 8px;
  background-color: #ffffff;
  overflow: hidden;
}

.accordion-title {
  display: flex;
  justify-content: space-between;
  padding: 20px 20px 10px 20px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  color: #8143d9;
}

.accordion-item:hover {
  background-color: #e1e1e1;
}

.accordion-content {
  display: none;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 20px;
  background-color: #fafafa;
  transition: all 0.3s ease-in-out;
}

.arrow {
  font-size: 18px;
  display: inline-block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f8f6ff;
  text-align: center;
  line-height: 34px;
  margin-left: 10px;
  transition: background-color 0.3s ease;
}

.arrow:hover {
  background-color: #e1e1e1;
}

.day-card {
  text-align: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
}
.day-card.selected-day {
  background-color: #8143d9;
  color: white;
}
.btn-nav {
  margin: 0 8px;
  padding: 4px 8px;
  background-color: #8143d9;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.checkbox-inline {
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

@media (max-width: 768px) {
  .field-container {
    padding: 10px;
  }
  #container {
    padding: 5px;
  }
  .accordion-container {
    margin: 10px;
  }

  input {
    width: 91%;
  }
  h1 {
    text-align: center;
    color: #333;
    font-size: 18px;
  }

  .save-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}

