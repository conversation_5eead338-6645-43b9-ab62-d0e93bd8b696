import {
  formatHeading,
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

// Using formatDisplayDate from utils.js

function updateContainerHighlight(container, data) {
  // We only use the verified status for highlighting the container
  // No need to check individual fields at this time
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }

  // Update the verified checkbox if it exists
  const verifiedCheckbox = document.getElementById(
    "in-hospital-adjudication-verified-checkbox"
  );
  if (verifiedCheckbox) {
    verifiedCheckbox.checked = data.verified && data.verified.value === "True";
  }
}

// Function to render nested fields for conditional content
function renderNestedFields(
  selectedOption,
  parentField,
  container,
  parentKey,
  parentCategory
) {


  // Determine which conditional key to use based on selected option
  let conditionalKey = null;
  if (selectedOption.startsWith("Yes") && parentField.if_yes) {
    conditionalKey = "if_yes";
  } else if (selectedOption === "No" && parentField.if_no) {
    conditionalKey = "if_no";
  } else if (selectedOption === "Alive" && parentField.if_alive) {
    conditionalKey = "if_alive";
  } else if (selectedOption === "Deceased" && parentField.if_deceased) {
    conditionalKey = "if_deceased";
  } else if (selectedOption === "Hemorrhage" && parentField.if_hemorrhage) {
    conditionalKey = "if_hemorrhage";

  } else if (selectedOption === "Infarction" && parentField.if_infarction) {
    conditionalKey = "if_infarction";
  } else if (selectedOption === "Both" && parentField.if_both) {
    conditionalKey = "if_both";
  }



  if (!conditionalKey) return;

  // Show the container
  container.style.display = "block";

  // Render each nested field
  Object.entries(parentField[conditionalKey]).forEach(([subKey, subField]) => {


    const subFieldContainer = document.createElement("div");
    subFieldContainer.classList.add("sub-field-container");
    subFieldContainer.style.marginBottom = "15px";

    // Create label for the sub-field
    const subLabel = document.createElement("label");
    subLabel.classList.add("sub-label");
    subLabel.textContent = `${subField.label}${
      subField.metric ? ` (${subField.metric})` : ""
    } (${subField.field_id})`;
    if (subField.description) {
      subLabel.setAttribute("title", subField.description);
    }
    subFieldContainer.appendChild(subLabel);

    // Handle different input types for nested fields
    if (subField.input_type === "string" || subField.input_type === "text") {
      const input = document.createElement("input");
      input.type = "text";
      input.name = `${parentKey}-${subKey}`;
      input.placeholder = `Enter ${subField.label}`;
      input.value = subField.value || "";

      input.addEventListener("input", (e) => {
        patientData.in_hospital_adjudication[parentCategory][parentKey][
          conditionalKey
        ][subKey].value = e.target.value;
        patientData.in_hospital_adjudication[parentCategory][parentKey][
          conditionalKey
        ][subKey].modified_by = "ABSTRACTOR";
        // Removed updateContainerHighlight call for nested text fields
      });

      subFieldContainer.appendChild(input);
    } else if (subField.input_type === "date") {
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${parentKey}-${subKey}-display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(subField.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = `${parentKey}-${subKey}`;
      dateInput.value = subField.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        patientData.in_hospital_adjudication[parentCategory][parentKey][
          conditionalKey
        ][subKey].value = selectedDate;
        patientData.in_hospital_adjudication[parentCategory][parentKey][
          conditionalKey
        ][subKey].modified_by = "ABSTRACTOR";
        // Removed updateContainerHighlight call for nested date fields
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      subFieldContainer.appendChild(dateWrapper);
    } else if (subField.input_type === "radio") {
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "column";
      radioContainer.style.gap = "10px";

      subField.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginBottom = "10px";

        const input = document.createElement("input");
        input.type = "radio";
        input.name = `radio-${parentCategory}-${parentKey}-${subKey}`;
        input.value = option;
        input.id = `${parentCategory}-${parentKey}-${subKey}-${option}`;
        input.checked = subField.value === option;

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            patientData.in_hospital_adjudication[parentCategory][parentKey][
              conditionalKey
            ][subKey].value = e.target.value;
            patientData.in_hospital_adjudication[parentCategory][parentKey][
              conditionalKey
            ][subKey].modified_by = "ABSTRACTOR";
            // Removed updateContainerHighlight call for nested radio fields
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute(
          "for",
          `${parentCategory}-${parentKey}-${subKey}-${option}`
        );
        optionLabel.innerText = option;

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      subFieldContainer.appendChild(radioContainer);
    } else if (subField.input_type === "multi_select") {

      // Initialize the value as an array if it's not already
      if (!Array.isArray(subField.value)) {
        subField.value = subField.value ? [subField.value] : [];
      }

      // Create a simpler dropdown implementation
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";
      dropdownContainer.style.marginTop = "4px";

      // Create dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.backgroundColor = "#fff";
      dropdownHeader.style.cursor = "pointer";

      // Create selected text display
      const selectedText = document.createElement("span");
      selectedText.classList.add("selected-text");

      // Function to update the selected text
      const updateSelectedText = () => {
        if (!subField.value || subField.value.length === 0) {
          selectedText.textContent = "Select options";
        } else if (subField.value.length === 1) {
          // Find the option object if it exists
          const selectedOption = subField.options.find(
            (opt) =>
              (typeof opt === "object" ? opt.value : opt) === subField.value[0]
          );
          selectedText.textContent = selectedOption
            ? typeof selectedOption === "object"
              ? selectedOption.value
              : selectedOption
            : subField.value[0];
        } else {
          // For multiple selected options, show the actual values
          const selectedValues = subField.value.map((val) => {
            const option = subField.options.find((opt) => {
              const optValue = typeof opt === "object" ? opt.value : opt;
              return optValue === val;
            });
            return option
              ? typeof option === "object"
                ? option.value
                : option
              : val;
          });
          selectedText.textContent = selectedValues.join(", ");
        }
      };

      updateSelectedText();

      // Add dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create dropdown content
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      dropdownContent.style.position = "absolute";
      dropdownContent.style.top = "100%";
      dropdownContent.style.left = "0";
      dropdownContent.style.width = "100%";
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.backgroundColor = "#fff";
      dropdownContent.style.border = "1px solid #ccc";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
      dropdownContent.style.zIndex = "1000";

      // Create a checkbox for each option
      subField.options.forEach((option) => {
        // Handle both object options and string options
        const optionValue = typeof option === "object" ? option.value : option;
        const optionId = typeof option === "object" ? option.id : option;

        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.classList.add("checkbox-wrapper");
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";
        checkboxWrapper.style.padding = "8px 12px";
        checkboxWrapper.style.borderBottom = "1px solid #eee";
        checkboxWrapper.style.textAlign = "left";
        checkboxWrapper.style.cursor = "pointer";

        const input = document.createElement("input");
        input.type = "checkbox";
        input.name = `${parentCategory}-${parentKey}-${subKey}-${optionId}`;
        input.value = optionId;
        input.id = `${parentCategory}-${parentKey}-${subKey}-${optionId}`;
        input.style.marginRight = "8px";

        // Check if this option is in the selected values array
        // Handle both object options and string options for value checking
        const optionValueToCheck = typeof option === "object" ? option.value : option;
        input.checked = subField.value.includes(optionValueToCheck);

        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(subField.value)
            ? [...subField.value]
            : [];

          // Use the actual option value (not the ID) for storage
          const valueToStore = typeof option === "object" ? option.value : option;

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(valueToStore)) {
              currentValues.push(valueToStore);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== valueToStore);
          }

          // Update the model - ensure the nested structure exists
          if (!patientData.in_hospital_adjudication[parentCategory]) {
            patientData.in_hospital_adjudication[parentCategory] = {};
          }
          if (!patientData.in_hospital_adjudication[parentCategory][parentKey]) {
            patientData.in_hospital_adjudication[parentCategory][parentKey] = {};
          }
          if (!patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey]) {
            patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey] = {};
          }
          if (!patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey][subKey]) {
            patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey][subKey] = {
              value: [],
              modified_by: ""
            };
          }

          patientData.in_hospital_adjudication[parentCategory][parentKey][
            conditionalKey
          ][subKey].value = currentValues;
          patientData.in_hospital_adjudication[parentCategory][parentKey][
            conditionalKey
          ][subKey].modified_by = "ABSTRACTOR";

          // Update selected text display
          updateSelectedText();

          // Update container highlighting for nested multi_select
          updateContainerHighlight(
            document.getElementById("inHospitalAdjudication"),
            patientData.in_hospital_adjudication
          );
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute(
          "for",
          `${parentCategory}-${parentKey}-${subKey}-${optionId}`
        );
        if (typeof option === "object" && option.field_id) {
          optionLabel.innerText = `${optionValue} (${option.field_id})`;
        } else {
          optionLabel.innerText = optionValue;
        }

        checkboxWrapper.appendChild(input);
        checkboxWrapper.appendChild(optionLabel);
        dropdownContent.appendChild(checkboxWrapper);
      });

      // Toggle dropdown on click
      dropdownHeader.addEventListener("click", () => {
        const isOpen = dropdownContent.style.display === "block";
        if (isOpen) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;"; // Down arrow
        } else {
          dropdownContent.style.display = "block";
          dropdownArrow.innerHTML = "&#9652;"; // Up arrow
        }
      });

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !dropdownContainer.contains(e.target) &&
          !dropdownContent.contains(e.target)
        ) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
        }
      });

      dropdownContainer.appendChild(dropdownHeader);
      dropdownContainer.appendChild(dropdownContent);
      subFieldContainer.appendChild(dropdownContainer);
    } else if (subField.input_type === "multi_input_field") {

      // Handle nested multi_input_field
      const nestedMultiInputContainer = document.createElement("div");
      nestedMultiInputContainer.classList.add("nested-multi-input-container");
      nestedMultiInputContainer.style.marginTop = "10px";

      // Create radio buttons container
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "column";
      radioContainer.style.gap = "10px";
      radioContainer.style.width = "100%";

      // Create container for nested conditional content
      const nestedConditionalContainer = document.createElement("div");
      nestedConditionalContainer.classList.add("nested-conditional-container");
      nestedConditionalContainer.style.marginTop = "10px";
      nestedConditionalContainer.style.padding = "10px";
      nestedConditionalContainer.style.border = "1px solid #eee";
      nestedConditionalContainer.style.borderRadius = "4px";
      nestedConditionalContainer.style.display = "none"; // Initially hidden

      // Create radio buttons for each option
      subField.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginBottom = "10px";

        // Handle both string options and object options
        const optionValue = typeof option === "object" ? option.value : option;
        const optionId = typeof option === "object" ? option.id : option;




        // If the field value is an object, extract its value property
        let fieldValue = subField.value;
        if (typeof fieldValue === "object" && fieldValue !== null) {
          fieldValue = fieldValue.value;
        }



        const input = document.createElement("input");
        input.type = "radio";
        input.name = `radio-${parentCategory}-${parentKey}-${conditionalKey}-${subKey}`;
        input.value = optionValue;
        input.id = `${parentCategory}-${parentKey}-${conditionalKey}-${subKey}-${optionId}`;
        input.checked = fieldValue === optionValue;



        input.addEventListener("change", (e) => {
          if (e.target.checked) {


            // Find the matching option object if it exists
            const matchingOption = subField.options.find((opt) => {
              const optVal = typeof opt === "object" ? opt.value : opt;
              return optVal === e.target.value;
            });



            // Update the field value - ensure the nested structure exists
            if (!patientData.in_hospital_adjudication[parentCategory]) {
              patientData.in_hospital_adjudication[parentCategory] = {};
            }
            if (!patientData.in_hospital_adjudication[parentCategory][parentKey]) {
              patientData.in_hospital_adjudication[parentCategory][parentKey] = {};
            }
            if (!patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey]) {
              patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey] = {};
            }
            if (!patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey][subKey]) {
              patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey][subKey] = {
                value: "",
                modified_by: ""
              };
            }

            patientData.in_hospital_adjudication[parentCategory][parentKey][
              conditionalKey
            ][subKey].value = e.target.value;
            patientData.in_hospital_adjudication[parentCategory][parentKey][
              conditionalKey
            ][subKey].modified_by = "ABSTRACTOR";


            // Update tile highlighting
            updateTileStyle(subFieldContainer, e.target.value);

            // Clear and render nested conditional content
            nestedConditionalContainer.innerHTML = "";
            nestedConditionalContainer.style.display = "none";

            // Determine which nested conditional content to show
            let nestedConditionalKey = null;
            if (e.target.value.startsWith("Yes") && subField.if_yes) {
              nestedConditionalKey = "if_yes";
            } else if (e.target.value === "No" && subField.if_no) {
              nestedConditionalKey = "if_no";
            } else if (
              e.target.value === "Hemorrhage" &&
              subField.if_hemorrhage
            ) {
              nestedConditionalKey = "if_hemorrhage";
            }

            // Render nested conditional content if available
            if (nestedConditionalKey && subField[nestedConditionalKey]) {

              renderNestedFields(
                e.target.value,
                subField,
                nestedConditionalContainer,
                subKey,
                `${parentCategory}.${parentKey}.${conditionalKey}`
              );
            }

            // Update container highlighting for nested multi_input_field
            updateContainerHighlight(
              document.getElementById("inHospitalAdjudication"),
              patientData.in_hospital_adjudication
            );
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute(
          "for",
          `${parentCategory}-${parentKey}-${conditionalKey}-${subKey}-${optionId}`
        );



        optionLabel.innerText = optionValue;

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      nestedMultiInputContainer.appendChild(radioContainer);
      nestedMultiInputContainer.appendChild(nestedConditionalContainer);
      subFieldContainer.appendChild(nestedMultiInputContainer);

      // If a value is already selected, render the nested conditional content
      if (subField.value) {
        // Determine which nested conditional content to show
        let nestedConditionalKey = null;
        if (subField.value.startsWith("Yes") && subField.if_yes) {
          nestedConditionalKey = "if_yes";
        } else if (subField.value === "No" && subField.if_no) {
          nestedConditionalKey = "if_no";
        } else if (subField.value === "Hemorrhage" && subField.if_hemorrhage) {
          nestedConditionalKey = "if_hemorrhage";
        }

        // Render nested conditional content if available
        if (nestedConditionalKey && subField[nestedConditionalKey]) {

          nestedConditionalContainer.style.display = "block";
          renderNestedFields(
            subField.value,
            subField,
            nestedConditionalContainer,
            subKey,
            `${parentCategory}.${parentKey}.${conditionalKey}`
          );
        }
      }
    }

    container.appendChild(subFieldContainer);
  });
}

export function renderInHospitalAdjudication(patientData) {
  const container = document.getElementById("inHospitalAdjudication");
  if (!container) {
    console.error("Container element not found for inHospitalAdjudication");
    return;
  }

  container.innerHTML = "";
  container.style.position = "relative";
  container.style.paddingBottom = "80px"; // Increased padding at the bottom for the verified checkbox
  container.style.minHeight = "300px"; // Increased minimum height for the container

  // Find dependency skip fields for combined input+checkbox rendering
  const skipFields = new Map(); // Using Map to store category and field key pairs

  // Helper function to process fields in a category or subcategory
  const processFields = (fields, category) => {
    Object.entries(fields).forEach(([fieldKey, value]) => {
      // Skip non-object values or verified field
      if (typeof value !== "object" || fieldKey === "verified") return;

      // If this is a subcategory (contains nested fields)
      if (!value.input_type && !value.field_id) {
        // Process the subcategory fields
        processFields(value, category + "." + fieldKey);
        return;
      }

      // Check if this field has a dependency
      if (value.dependency) {

        // We'll handle all dependency fields in their respective categories
        // No need for special case handling

        // Find the dependent field in the same category
        let dependentFieldKey = null;
        let dependentCategory = category;

        // Search in the current category/subcategory
        const categoryParts = category.split(".");
        let currentObj = patientData.in_hospital_adjudication;

        // Navigate to the correct nested object
        for (const part of categoryParts) {
          if (part === "in_hospital_adjudication") continue;
          currentObj = currentObj[part];
        }

        // Special case for medications
        if (category.includes("medication")) {
          currentObj = currentObj.medications;
        }

        // Find the field with matching field_id
        dependentFieldKey = Object.keys(currentObj).find(
          (k) => currentObj[k].field_id === value.dependency
        );

        if (dependentFieldKey) {

          // Store both fields with their categories
          skipFields.set(`${dependentCategory}.${dependentFieldKey}`, true);

          // Add all dependency fields to skipFields
          skipFields.set(`${category}.${fieldKey}`, true);
        }
      }
    });
  };

  // Process all categories in the patient data
  Object.entries(patientData.in_hospital_adjudication).forEach(
    ([category, fields]) => {
      if (category === "verified") return;
      processFields(fields, "in_hospital_adjudication." + category);
    }
  );


  container.style.border = "2px solid red"; // Default red border
  container.style.borderRadius = "8px"; // Default border radius

  // Make sure in_hospital_adjudication exists in patientData
  if (!patientData.in_hospital_adjudication) {
    console.error("in_hospital_adjudication data not found in patientData");
    patientData.in_hospital_adjudication = {};
  }

  // Ensure the verified field exists
  if (!patientData.in_hospital_adjudication.hasOwnProperty("verified")) {
    patientData.in_hospital_adjudication.verified = "False";
  }

  // Convert the verified field to an object if it's a string or other primitive
  if (
    typeof patientData.in_hospital_adjudication.verified !== "object" ||
    patientData.in_hospital_adjudication.verified === null
  ) {
    const originalValue = patientData.in_hospital_adjudication.verified;
    patientData.in_hospital_adjudication.verified = {
      value: originalValue === "True" ? "True" : "False",
      modified_by: "",
    };
  }

  // Ensure the value property exists and is a string
  if (!patientData.in_hospital_adjudication.verified.hasOwnProperty("value")) {
    patientData.in_hospital_adjudication.verified.value = "False";
  } else if (
    typeof patientData.in_hospital_adjudication.verified.value !== "string"
  ) {
    patientData.in_hospital_adjudication.verified.value = patientData
      .in_hospital_adjudication.verified.value
      ? "True"
      : "False";
  }

  // Ensure the modified_by property exists
  if (
    !patientData.in_hospital_adjudication.verified.hasOwnProperty("modified_by")
  ) {
    patientData.in_hospital_adjudication.verified.modified_by = "";
  }


  // We already identified fields with dependencies above
  // Use the skipFields Map we created earlier
  const dependencyFields = {};

  // First pass: find all fields with dependency key
  Object.entries(patientData.in_hospital_adjudication).forEach(
    ([category, fields]) => {
      if (category === "verified") return;

      Object.entries(fields).forEach(([key, value]) => {
        if (value.dependency) {
          // Add to skipFields so we don't render it separately
          const fullKey = `in_hospital_adjudication.${category}.${key}`;
          skipFields.set(fullKey, true);

          // Find the dependent field (checkbox)
          const dependentFieldId = value.dependency;

          // Find the field with this field_id
          let dependentFieldKey = null;
          let dependentFieldCategory = null;

          Object.entries(patientData.in_hospital_adjudication).forEach(
            ([cat, catFields]) => {
              if (cat === "verified") return;

              Object.entries(catFields).forEach(([k, v]) => {
                if (v.field_id === dependentFieldId) {
                  dependentFieldKey = k;
                  dependentFieldCategory = cat;
                }
              });
            }
          );

          if (dependentFieldKey) {
            // Store the dependency relationship
            dependencyFields[key] = {
              category: category,
              dependentKey: dependentFieldKey,
              dependentCategory: dependentFieldCategory,
            };
          }
        }
      });
    }
  );

  Object.entries(patientData.in_hospital_adjudication).forEach(
    ([category, fields]) => {
      if (category === "verified") return;

      const categoryContainer = document.createElement("div");
      categoryContainer.classList.add("category-container");
      categoryContainer.setAttribute("data-category", category);
      categoryContainer.style.display = "contents";

      const categoryLabel = document.createElement("h4");
      categoryLabel.style.fontWeight = "bold";
      categoryLabel.style.gridColumn = "1 / -1";
      categoryLabel.innerText = formatHeading(category);
      categoryContainer.appendChild(categoryLabel);

      // Create a map to track conditional fields
      const conditionalFields = new Map();

      // Special handling for medication category which has a nested structure
      if (category === "medication" && fields.medications) {
        // Create a medications container
        const medicationsContainer = document.createElement("div");
        medicationsContainer.style.display = "grid";
        medicationsContainer.style.gridTemplateColumns = "repeat(3, 1fr)";
        medicationsContainer.style.gap = "16px";
        medicationsContainer.style.gridColumn = "1 / -1";
        medicationsContainer.style.marginTop = "16px";
        categoryContainer.appendChild(medicationsContainer);

        // Render each medication
        Object.entries(fields.medications).forEach(([medKey, medValue]) => {
          // Skip non-medication fields
          if (medKey === "label" || medKey === "field_id") return;

          // Create a tile wrapper for each medication
          const medTileWrapper = document.createElement("div");
          medTileWrapper.classList.add("tile-wrapper");
          medTileWrapper.style.position = "relative";
          medTileWrapper.style.marginBottom = "36px";

          // Create the field container
          const medFieldContainer = document.createElement("div");
          medFieldContainer.classList.add("field-container");
          medFieldContainer.style.display = "flex";
          medFieldContainer.style.flexDirection = "column";

          // Apply initial highlighting for empty fields
          updateTileStyle(medFieldContainer, medValue.value);
          medFieldContainer.style.height = "100%";
          medFieldContainer.style.boxSizing = "border-box";
          medFieldContainer.style.padding = "16px";
          // Don't set border style here as it will override updateTileStyle
          medFieldContainer.style.borderRadius = "4px";
          medFieldContainer.style.backgroundColor = "#fff";


          // Create the label
          const medLabel = document.createElement("label");
          medLabel.classList.add("label", "cursor-pointer");
          medLabel.textContent = `${medValue.label}${
            medValue.metric ? ` (${medValue.metric})` : ""
          } (${medValue.field_id})`;
          if (medValue.description) {
            medLabel.setAttribute("title", medValue.description);
          }
          medFieldContainer.appendChild(medLabel);

          // Create the modified_by display
          const medModifiedByDisplay = document.createElement("span");
          medModifiedByDisplay.style.display = "block";
          medModifiedByDisplay.style.textAlign = "right";
          medModifiedByDisplay.style.position = "absolute";
          medModifiedByDisplay.style.bottom = "-20px";
          medModifiedByDisplay.style.right = "0";
          medModifiedByDisplay.style.color = "#8143d9";
          medModifiedByDisplay.style.fontSize = "12px";

          // Ensure medValue is an object with a modified_by property
          if (typeof medValue !== "object" || medValue === null) {
            const originalValue = medValue;
            medValue = {
              value: originalValue,
              modified_by: "",
            };
          } else if (!medValue.hasOwnProperty("modified_by")) {
            medValue.modified_by = "";
          }

          medModifiedByDisplay.textContent = medValue.modified_by;

          // Create the select element for the medication
          if (medValue.input_type === "select" && medValue.options) {
            const medSelect = document.createElement("select");
            medSelect.name = medKey;

            // Add a default option
            const defaultOption = document.createElement("option");
            defaultOption.value = "";
            defaultOption.innerText = "Select an option";
            defaultOption.disabled = true;
            defaultOption.selected = !medValue.value;
            medSelect.appendChild(defaultOption);

            // Add options from the data
            medValue.options.forEach((option) => {
              const optionElement = document.createElement("option");
              optionElement.value = option.id;
              optionElement.innerText = option.value;
              if (medValue.value === option.value) {
                optionElement.selected = true;
              }
              medSelect.appendChild(optionElement);
            });

            // Add event listener to update the data model
            medSelect.addEventListener("change", (e) => {
              const selectedOption = medValue.options.find(
                (option) => option.id.toString() === e.target.value
              );
              patientData.in_hospital_adjudication.medication.medications[
                medKey
              ].value = selectedOption.value;
              patientData.in_hospital_adjudication.medication.medications[
                medKey
              ].modified_by = "ABSTRACTOR";

              // Update tile highlighting
              updateTileStyle(medFieldContainer, selectedOption.value);

              medModifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.in_hospital_adjudication
              );
            });

            medFieldContainer.appendChild(medSelect);
          }

          // Append the field container and modified_by display to the tile wrapper
          medTileWrapper.appendChild(medFieldContainer);
          medTileWrapper.appendChild(medModifiedByDisplay);
          medicationsContainer.appendChild(medTileWrapper);
        });

        container.appendChild(categoryContainer);
        return; // Skip the regular field rendering for medication category
      }

      // First pass: identify conditional fields
      Object.entries(fields).forEach(([key, value]) => {
        if (key === "adjudication_status" && value.if_alive) {
          // Store the conditional fields for "Alive" option
          Object.entries(value.if_alive).forEach(
            ([conditionalKey, conditionalValue]) => {
              conditionalFields.set(conditionalKey, {
                parentKey: key,
                parentValue: "Alive",
                data: conditionalValue,
              });
            }
          );
        }
      });

      // Second pass: render fields
      Object.entries(fields).forEach(([key, value]) => {
        // Skip conditional fields - they will be rendered with their parent
        if (conditionalFields.has(key)) return;

        // Skip fields with dependencies - they will be rendered separately
        const fullKey = `in_hospital_adjudication.${category}.${key}`;

        // Skip fields that are part of dependency relationships
        if (skipFields.has(fullKey) || key.startsWith("not_")) {

          return;
        }
        // Skip dependency fields - they will be rendered separately later
        else if (value.dependency) {

          return;
        }

        const tileWrapper = document.createElement("div");
        tileWrapper.classList.add("tile-wrapper");
        tileWrapper.style.position = "relative";
        tileWrapper.style.marginBottom = "24px";

        const fieldContainer = document.createElement("div");
        fieldContainer.classList.add("field-container");

        const label = document.createElement("label");
        label.classList.add("label", "cursor-pointer");
        label.textContent = `${value.label}${
          value.metric ? ` (${value.metric})` : ""
        } (${value.field_id})`;
        if (value.description) {
          label.setAttribute("title", value.description);
        }
        fieldContainer.appendChild(label);

        if (value.input_type === "string" || value.input_type === "text") {
          const input = document.createElement("input");
          input.type = "text";
          input.name = key;
          input.placeholder = `Enter ${value.label}`;
          input.value = value.value || "";

          updateTileStyle(fieldContainer, input.value);

          let previousValue = input.value;

          input.addEventListener("input", (e) => {
            const currentValue = e.target.value;

            // Use validation utilities with temporary callback
            let isValid = false;
            validateStringInput(
              currentValue,
              value.field_id,
              (validatedValue) => {
                isValid = true;
                previousValue = validatedValue;

                // Update model and UI only if value is valid
                patientData.in_hospital_adjudication[category][key].value =
                  validatedValue;
                patientData.in_hospital_adjudication[category][
                  key
                ].modified_by = "ABSTRACTOR";
                updateTileStyle(fieldContainer, validatedValue);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(
                  container,
                  patientData.in_hospital_adjudication
                );
              }
            );

            // If validation failed, revert to previous value
            if (!isValid) {
              e.target.value = previousValue;
            }
          });

          fieldContainer.appendChild(input);
        } else if (value.input_type === "date") {
          const dateWrapper = document.createElement("div");
          dateWrapper.style.position = "relative";

          // Create display input
          const displayInput = document.createElement("input");
          displayInput.type = "text";
          displayInput.name = `${key}_display`;
          displayInput.readOnly = true;
          displayInput.value = formatDisplayDate(value.value);
          displayInput.placeholder = "MM/DD/YYYY";
          displayInput.style.cursor = "pointer";

          // Hidden date input
          const dateInput = document.createElement("input");
          dateInput.type = "date";
          dateInput.name = key;
          dateInput.value = value.value || "";
          dateInput.style.position = "absolute";
          dateInput.style.opacity = "0";
          dateInput.style.cursor = "pointer";

          // Set max date to today
          const today = new Date().toISOString().split("T")[0];
          dateInput.max = today;

          updateTileStyle(fieldContainer, dateInput.value);

          dateInput.addEventListener("change", (e) => {
            const selectedDate = e.target.value;
            displayInput.value = formatDisplayDate(selectedDate);
            patientData.in_hospital_adjudication[category][key].value =
              selectedDate;
            patientData.in_hospital_adjudication[category][key].modified_by =
              "ABSTRACTOR";
            updateTileStyle(fieldContainer, selectedDate);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.in_hospital_adjudication
            );
          });

          // Trigger date picker when clicking display input
          displayInput.addEventListener("click", () => {
            dateInput.showPicker();
          });

          dateWrapper.appendChild(displayInput);
          dateWrapper.appendChild(dateInput);
          fieldContainer.appendChild(dateWrapper);
        } else if (value.input_type === "radio") {
          const radioContainer = document.createElement("div");
          radioContainer.classList.add("radio-container");

          // Create a container for conditional fields if this is adjudication_status
          let conditionalFieldsContainer = null;
          if (key === "adjudication_status" && value.if_deceased) {
            conditionalFieldsContainer = document.createElement("div");
            conditionalFieldsContainer.classList.add(
              "conditional-fields-container"
            );
            conditionalFieldsContainer.style.marginTop = "16px";
            conditionalFieldsContainer.style.paddingLeft = "16px";
            conditionalFieldsContainer.style.borderLeft = "2px solid #8143d9";
            conditionalFieldsContainer.style.display =
              value.value === "Deceased" ? "block" : "none";
          }

          value.options.forEach((option) => {
            const radioWrapper = document.createElement("div");

            const input = document.createElement("input");
            input.type = "radio";
            input.name = `radio-${key}-${category}`;
            input.value = option;
            input.id = `${key}-${option}`;
            input.checked = value.value === option;

            input.addEventListener("change", (event) => {
              if (event.target.checked) {
                patientData.in_hospital_adjudication[category][key].value =
                  event.target.value;
                patientData.in_hospital_adjudication[category][
                  key
                ].modified_by = "ABSTRACTOR";
                updateTileStyle(fieldContainer, event.target.value);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(
                  container,
                  patientData.in_hospital_adjudication
                );

                // Handle conditional fields visibility
                if (
                  key === "adjudication_status" &&
                  conditionalFieldsContainer
                ) {
                  conditionalFieldsContainer.style.display =
                    event.target.value === "Deceased" ? "block" : "none";
                }
              }
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${key}-${option}`);
            optionLabel.innerText = option;
            if (value.description) {
              optionLabel.setAttribute("title", value.description);
            }
            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            radioContainer.appendChild(radioWrapper);
          });

          updateTileStyle(fieldContainer, value.value || "");
          fieldContainer.appendChild(radioContainer);

          // Render conditional fields if this is adjudication_status
          if (
            key === "adjudication_status" &&
            value.if_deceased &&
            conditionalFieldsContainer
          ) {
            // Render the conditional fields for "Deceased" option
            Object.entries(value.if_deceased).forEach(
              ([conditionalKey, conditionalValue]) => {
                const conditionalTileWrapper = document.createElement("div");
                conditionalTileWrapper.classList.add("tile-wrapper");
                conditionalTileWrapper.style.position = "relative";
                conditionalTileWrapper.style.marginBottom = "16px";

                const conditionalFieldContainer = document.createElement("div");
                conditionalFieldContainer.classList.add("field-container");

                const conditionalLabel = document.createElement("label");
                conditionalLabel.classList.add("label", "cursor-pointer");
                conditionalLabel.textContent = `${conditionalValue.label}${
                  conditionalValue.metric ? ` (${conditionalValue.metric})` : ""
                } (${conditionalValue.field_id})`;
                if (conditionalValue.description) {
                  conditionalLabel.setAttribute(
                    "title",
                    conditionalValue.description
                  );
                }
                conditionalFieldContainer.appendChild(conditionalLabel);

                // Handle date input for conditional field
                if (conditionalValue.input_type === "date") {
                  const dateWrapper = document.createElement("div");
                  dateWrapper.style.position = "relative";

                  // Create display input
                  const displayInput = document.createElement("input");
                  displayInput.type = "text";
                  displayInput.name = `${conditionalKey}_display`;
                  displayInput.readOnly = true;
                  displayInput.value = formatDisplayDate(
                    conditionalValue.value
                  );
                  displayInput.placeholder = "MM/DD/YYYY";
                  displayInput.style.cursor = "pointer";

                  // Hidden date input
                  const dateInput = document.createElement("input");
                  dateInput.type = "date";
                  dateInput.name = conditionalKey;
                  dateInput.value = conditionalValue.value || "";
                  dateInput.style.position = "absolute";
                  dateInput.style.opacity = "0";
                  dateInput.style.cursor = "pointer";

                  // Set max date to today
                  const today = new Date().toISOString().split("T")[0];
                  dateInput.max = today;

                  updateTileStyle(conditionalFieldContainer, dateInput.value);

                  dateInput.addEventListener("change", (e) => {
                    const selectedDate = e.target.value;
                    displayInput.value = formatDisplayDate(selectedDate);
                    patientData.in_hospital_adjudication[category][
                      key
                    ].if_deceased[conditionalKey].value = selectedDate;
                    patientData.in_hospital_adjudication[category][
                      key
                    ].if_deceased[conditionalKey].modified_by = "ABSTRACTOR";
                    updateTileStyle(conditionalFieldContainer, selectedDate);
                    conditionalModifiedByDisplay.textContent = "ABSTRACTOR";
                    updateContainerHighlight(
                      container,
                      patientData.in_hospital_adjudication
                    );
                  });

                  // Trigger date picker when clicking display input
                  displayInput.addEventListener("click", () => {
                    dateInput.showPicker();
                  });

                  dateWrapper.appendChild(displayInput);
                  dateWrapper.appendChild(dateInput);
                  conditionalFieldContainer.appendChild(dateWrapper);
                }

                // Create modified_by display for conditional field
                const conditionalModifiedByDisplay =
                  document.createElement("span");
                conditionalModifiedByDisplay.style.display = "block";
                conditionalModifiedByDisplay.style.textAlign = "right";
                conditionalModifiedByDisplay.style.marginTop = "-10px";
                conditionalModifiedByDisplay.style.color = "#8143d9";
                conditionalModifiedByDisplay.style.fontSize = "12px";
                if (!conditionalValue.modified_by) {
                  conditionalValue.modified_by = "";
                }
                conditionalModifiedByDisplay.textContent =
                  conditionalValue.modified_by;

                conditionalTileWrapper.appendChild(conditionalFieldContainer);
                conditionalTileWrapper.appendChild(
                  conditionalModifiedByDisplay
                );
                conditionalFieldsContainer.appendChild(conditionalTileWrapper);
              }
            );

            fieldContainer.appendChild(conditionalFieldsContainer);
          }
        } else if (value.input_type === "select") {
          const select = document.createElement("select");
          select.name = key;
          if (!value.value) {
            const defaultOption = document.createElement("option");
            defaultOption.value = "";
            defaultOption.innerText = "Select an option";
            defaultOption.disabled = true;
            defaultOption.selected = true;
            select.appendChild(defaultOption);
          }
          value.options.forEach((option) => {
            const optionElement = document.createElement("option");
            optionElement.value = option.id.toString();
            optionElement.innerText = option.value;
            optionElement.selected = value.value === option.value;
            select.appendChild(optionElement);
          });
          select.addEventListener("change", (e) => {
            const selectedOption = value.options.find(
              (option) => option.id.toString() === e.target.value
            );
            const newValue = selectedOption ? selectedOption.value : "";

            patientData.in_hospital_adjudication[category][key].value = newValue;
            patientData.in_hospital_adjudication[category][key].modified_by =
              "ABSTRACTOR";
            updateTileStyle(fieldContainer, newValue);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.in_hospital_adjudication
            );
          });
          updateTileStyle(fieldContainer, value.value || "");
          fieldContainer.appendChild(select);
        } else if (value.input_type === "multi_input_field") {
          // Handle multi_input_field type
          const multiInputContainer = document.createElement("div");
          multiInputContainer.classList.add("multi-input-container");
          multiInputContainer.style.marginTop = "10px";

          // Apply initial highlighting for empty fields
          updateTileStyle(fieldContainer, value.value);

          // Create radio buttons container
          const radioContainer = document.createElement("div");
          radioContainer.classList.add("radio-container");
          radioContainer.style.display = "flex";
          radioContainer.style.flexDirection = "column"; // Changed to column for vertical stacking
          radioContainer.style.gap = "10px";
          radioContainer.style.width = "100%";

          // Create container for conditional content
          const conditionalContainer = document.createElement("div");
          conditionalContainer.classList.add("conditional-content-container");
          conditionalContainer.style.marginTop = "10px";
          conditionalContainer.style.padding = "10px";
          conditionalContainer.style.border = "1px solid #eee";
          conditionalContainer.style.borderRadius = "4px";
          conditionalContainer.style.display = "none"; // Initially hidden

          // Create radio buttons for each option
          value.options.forEach((option) => {
            const radioWrapper = document.createElement("div");
            radioWrapper.style.display = "flex";
            radioWrapper.style.alignItems = "center";
            radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

            // Handle both string options and object options
            const optionValue =
              typeof option === "object" ? option.value : option;
            const optionId = typeof option === "object" ? option.id : option;



            const input = document.createElement("input");
            input.type = "radio";
            input.name = `radio-${category}-${key}`; // Make name unique by including both category and key
            input.value = optionValue;
            input.id = `${category}-${key}-${optionId}`; // Make ID unique by including category
            input.checked = value.value === optionValue;


            input.addEventListener("change", (e) => {
              if (e.target.checked) {
                // Update the field value
                patientData.in_hospital_adjudication[category][key].value =
                  e.target.value;
                patientData.in_hospital_adjudication[category][
                  key
                ].modified_by = "ABSTRACTOR";
                updateTileStyle(fieldContainer, e.target.value);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(
                  container,
                  patientData.in_hospital_adjudication
                );

                // Clear and render conditional content
                conditionalContainer.innerHTML = "";
                conditionalContainer.style.display = "none";

                // Determine which conditional content to show
                let conditionalKey = null;
                if (e.target.value.startsWith("Yes") && value.if_yes) {
                  conditionalKey = "if_yes";

                } else if (e.target.value === "No" && value.if_no) {
                  conditionalKey = "if_no";

                } else if (e.target.value === "Alive" && value.if_alive) {
                  conditionalKey = "if_alive";

                } else if (e.target.value === "Deceased" && value.if_deceased) {
                  conditionalKey = "if_deceased";

                } else if (
                  e.target.value === "Infarction" &&
                  value.if_infarction
                ) {
                  conditionalKey = "if_infarction";

                } else if (
                  e.target.value === "Hemorrhage" &&
                  value.if_hemorrhage
                ) {
                  conditionalKey = "if_hemorrhage";

                } else if (e.target.value === "Both" && value.if_both) {
                  conditionalKey = "if_both";

                }

                // Render conditional content if available
                if (conditionalKey && value[conditionalKey]) {


                  renderNestedFields(
                    e.target.value,
                    value,
                    conditionalContainer,
                    key,
                    category
                  );
                }
              }
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${category}-${key}-${optionId}`); // Match the updated ID
            optionLabel.innerText = optionValue;
            if (value.description) {
              optionLabel.setAttribute("title", value.description);
            }

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            radioContainer.appendChild(radioWrapper);
          });

          multiInputContainer.appendChild(radioContainer);
          multiInputContainer.appendChild(conditionalContainer);
          fieldContainer.appendChild(multiInputContainer);

          // If a value is already selected, render the conditional content
          if (value.value) {


            // Determine which conditional content to show
            let conditionalKey = null;
            if (value.value.startsWith("Yes") && value.if_yes) {
              conditionalKey = "if_yes";

            } else if (value.value === "No" && value.if_no) {
              conditionalKey = "if_no";

            } else if (value.value === "Alive" && value.if_alive) {
              conditionalKey = "if_alive";

            } else if (value.value === "Deceased" && value.if_deceased) {
              conditionalKey = "if_deceased";
            }

            // Render conditional content if available
            if (conditionalKey && value[conditionalKey]) {


              conditionalContainer.style.display = "block"; // Make sure the container is visible
              renderNestedFields(
                value.value,
                value,
                conditionalContainer,
                key,
                category
              );
            }
          }
        } else if (value.input_type === "multi_select") {
          // Initialize the value as an array if it's not already
          if (!Array.isArray(value.value)) {
            value.value = value.value ? [value.value] : [];
          }

          // Create a simpler dropdown implementation
          // Create dropdown container with relative positioning
          const dropdownContainer = document.createElement("div");
          dropdownContainer.classList.add("dropdown-container");
          dropdownContainer.style.position = "relative";
          dropdownContainer.style.width = "100%";

          // Make sure all parent containers allow overflow
          fieldContainer.style.overflow = "visible";
          const tileWrapper = fieldContainer.parentElement;
          if (tileWrapper && tileWrapper.classList.contains("tile-wrapper")) {
            tileWrapper.style.overflow = "visible";
          }
          // Also set the container's overflow to visible
          container.style.overflow = "visible";

          // Create dropdown header/button
          const dropdownHeader = document.createElement("div");
          dropdownHeader.classList.add("dropdown-header");
          dropdownHeader.style.padding = "8px 12px";
          dropdownHeader.style.border = "1px solid #ccc";
          dropdownHeader.style.borderRadius = "4px";
          dropdownHeader.style.cursor = "pointer";
          dropdownHeader.style.display = "flex";
          dropdownHeader.style.justifyContent = "space-between";
          dropdownHeader.style.alignItems = "center";
          dropdownHeader.style.backgroundColor = "#fff";

          // Display selected values or placeholder
          const selectedText = document.createElement("span");
          selectedText.classList.add("selected-text");

          // Function to update the selected text display
          const updateSelectedText = () => {
            if (value.value.length === 0) {
              selectedText.textContent = "Select options...";
            } else if (value.value.length === 1) {
              const selectedOption = value.options.find(
                (opt) =>
                  (typeof opt === "object" ? opt.value : opt) === value.value[0]
              );
              selectedText.textContent = selectedOption
                ? typeof selectedOption === "object"
                  ? selectedOption.value
                  : selectedOption
                : value.value[0];
            } else {
              selectedText.textContent = `${value.value.length} options selected`;
            }
          };

          updateSelectedText();

          // Add dropdown arrow
          const dropdownArrow = document.createElement("span");
          dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

          dropdownHeader.appendChild(selectedText);
          dropdownHeader.appendChild(dropdownArrow);

          // Create dropdown content (initially hidden)
          const dropdownContent = document.createElement("div");
          dropdownContent.classList.add("dropdown-content");
          dropdownContent.style.display = "none";
          // Position the dropdown directly under the header
          dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
          dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
          dropdownContent.style.maxHeight = "200px";
          dropdownContent.style.overflowY = "auto";
          dropdownContent.style.overflowX = "hidden";
          dropdownContent.style.backgroundColor = "#fff";
          dropdownContent.style.border = "1px solid #ccc";
          dropdownContent.style.borderRadius = "4px";
          dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
          dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

          // Create a checkbox for each option
          value.options.forEach((option) => {
            const checkboxWrapper = document.createElement("div");
            checkboxWrapper.classList.add("checkbox-wrapper");
            checkboxWrapper.style.display = "flex";
            checkboxWrapper.style.alignItems = "center";
            checkboxWrapper.style.padding = "8px 12px";
            checkboxWrapper.style.borderBottom = "1px solid #eee";
            checkboxWrapper.style.textAlign = "left";
            checkboxWrapper.style.cursor = "pointer";

            const input = document.createElement("input");
            input.type = "checkbox";
            input.name = `${key}-${option.id}`;
            input.value = option.id;
            input.id = `${key}-${option.id}`;
            input.style.marginRight = "4px"; // Add a small space between checkbox and text

            // Check if this option is in the selected values array
            input.checked = value.value.includes(option.value);

            input.addEventListener("change", (e) => {
              // Get the current values array
              let currentValues = Array.isArray(value.value)
                ? [...value.value]
                : [];

              if (e.target.checked) {
                // Add the value if it's not already in the array
                if (!currentValues.includes(option.value)) {
                  currentValues.push(option.value);
                }
              } else {
                // Remove the value if it's in the array
                currentValues = currentValues.filter(
                  (val) => val !== option.value
                );
              }

              // Update the model
              patientData.in_hospital_adjudication[category][key].value =
                currentValues;
              patientData.in_hospital_adjudication[category][key].modified_by =
                "ABSTRACTOR";

              // Update selected text display
              updateSelectedText();

              // Update UI
              updateTileStyle(
                fieldContainer,
                currentValues.length > 0 ? "filled" : ""
              );
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.in_hospital_adjudication
              );
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${key}-${option.id}`);
            optionLabel.style.marginLeft = "0";
            optionLabel.style.display = "inline-block";
            optionLabel.style.whiteSpace = "nowrap";
            optionLabel.style.cursor = "pointer";
            optionLabel.style.flexGrow = "1";

            // Display field_id in parentheses if available
            if (option.field_id) {
              optionLabel.innerText = `${option.value} (${option.field_id})`;
            } else {
              optionLabel.innerText = option.value;
            }

            // Create a wrapper for the checkbox and label to ensure they're tightly aligned
            const inputLabelWrapper = document.createElement("div");
            inputLabelWrapper.style.display = "flex";
            inputLabelWrapper.style.alignItems = "center";
            inputLabelWrapper.style.gap = "0";

            inputLabelWrapper.appendChild(input);
            inputLabelWrapper.appendChild(optionLabel);
            checkboxWrapper.appendChild(inputLabelWrapper);
            dropdownContent.appendChild(checkboxWrapper);
          });

          // Function to position the dropdown content properly
          const positionDropdown = () => {
            const headerRect = dropdownHeader.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            // Position dropdown below the header
            dropdownContent.style.top = `${headerRect.bottom}px`;

            // Ensure dropdown doesn't go off the right edge of the screen
            const rightEdge = headerRect.left + 250; // 250px is our dropdown width
            if (rightEdge > viewportWidth) {
              // Align to the right edge of the header instead
              dropdownContent.style.left = `${headerRect.right - 250}px`;
            } else {
              dropdownContent.style.left = `${headerRect.left}px`;
            }

            // Set max height based on available space
            const spaceBelow = viewportHeight - headerRect.bottom;
            const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
            dropdownContent.style.maxHeight = `${maxHeight}px`;
          };

          // Close dropdown when clicking outside
          document.addEventListener("click", (e) => {
            if (
              !dropdownContainer.contains(e.target) &&
              !dropdownContent.contains(e.target)
            ) {
              dropdownContent.style.display = "none";
              dropdownArrow.innerHTML = "&#9662;";
            }
          });

          // Simple function to close the dropdown
          const closeDropdown = () => {
            dropdownContent.style.display = "none";
            dropdownArrow.innerHTML = "&#9662;";
            window.removeEventListener("scroll", closeDropdown);
          };

          // Clean up event listener when the component is removed
          const cleanupFunc = () => {
            if (document.body.contains(dropdownContent)) {
              document.body.removeChild(dropdownContent);
            }
            // Remove scroll event listener
            window.removeEventListener("scroll", closeDropdown);
          };

          // Store the cleanup function for potential future use
          dropdownContainer.cleanupFunc = cleanupFunc;

          // Add header to container, but add content to document body for better visibility
          dropdownContainer.appendChild(dropdownHeader);

          // Add the dropdown content to the document body when needed
          const showDropdown = () => {
            if (!document.body.contains(dropdownContent)) {
              document.body.appendChild(dropdownContent);
            }
            dropdownContent.style.display = "block";
            positionDropdown();
          };

          // Set up the click handler for the dropdown
          dropdownHeader.onclick = (e) => {
            e.stopPropagation();
            const isOpen = dropdownContent.style.display === "block";

            if (!isOpen) {
              showDropdown();
              dropdownArrow.innerHTML = "&#9652;"; // Up arrow
              window.addEventListener("scroll", closeDropdown);
            } else {
              closeDropdown();
            }
          };

          // Update the tile style based on whether any options are selected
          updateTileStyle(
            fieldContainer,
            value.value.length > 0 ? "filled" : ""
          );

          fieldContainer.appendChild(dropdownContainer);
        } else if (value.input_type === "multi_input_field") {
          const multiInputContainer = document.createElement("div");
          multiInputContainer.classList.add("multi-input-container");
          multiInputContainer.style.marginTop = "10px";

          // Create radio buttons container
          const radioContainer = document.createElement("div");
          radioContainer.classList.add("radio-container");
          radioContainer.style.display = "flex";
          radioContainer.style.flexDirection = "column"; // Changed to column for vertical stacking
          radioContainer.style.gap = "10px";
          radioContainer.style.width = "100%";

          // Create container for conditional content
          const conditionalContainer = document.createElement("div");
          conditionalContainer.classList.add("conditional-content-container");
          conditionalContainer.style.marginTop = "10px";
          conditionalContainer.style.padding = "10px";
          conditionalContainer.style.border = "1px solid #eee";
          conditionalContainer.style.borderRadius = "4px";
          conditionalContainer.style.display = "none"; // Initially hidden

          // Create radio buttons for each option
          value.options.forEach((option) => {
            const radioWrapper = document.createElement("div");
            radioWrapper.style.display = "flex";
            radioWrapper.style.alignItems = "center";
            radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

            const input = document.createElement("input");
            input.type = "radio";
            input.name = `radio-${key}-${category}`;
            input.value = option;
            input.id = `${key}-${option}`;
            input.checked = value.value === option;

            input.addEventListener("change", (e) => {
              if (e.target.checked) {
                // Update the field value
                patientData.in_hospital_adjudication[category][key].value =
                  e.target.value;
                patientData.in_hospital_adjudication[category][
                  key
                ].modified_by = "ABSTRACTOR";
                updateTileStyle(fieldContainer, e.target.value);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(
                  container,
                  patientData.in_hospital_adjudication
                );

                // Clear and render conditional content
                conditionalContainer.innerHTML = "";
                conditionalContainer.style.display = "none";

                // Determine which conditional content to show
                let conditionalKey = null;
                if (e.target.value.startsWith("Yes") && value.if_yes) {
                  conditionalKey = "if_yes";
                } else if (e.target.value === "No" && value.if_no) {
                  conditionalKey = "if_no";
                } else if (e.target.value === "Alive" && value.if_alive) {
                  conditionalKey = "if_alive";
                } else if (e.target.value === "Deceased" && value.if_deceased) {
                  conditionalKey = "if_deceased";
                }

                // Render conditional content if available
                if (conditionalKey && value[conditionalKey]) {
                  renderNestedFields(
                    e.target.value,
                    value,
                    conditionalContainer,
                    key,
                    category
                  );
                }
              }
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${key}-${option}`);
            optionLabel.innerText = option;
            if (value.description) {
              optionLabel.setAttribute("title", value.description);
            }

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            radioContainer.appendChild(radioWrapper);
          });

          multiInputContainer.appendChild(radioContainer);
          multiInputContainer.appendChild(conditionalContainer);
          fieldContainer.appendChild(multiInputContainer);

          // If a value is already selected, render the conditional content
          if (value.value) {
            // Determine which conditional content to show
            let conditionalKey = null;
            if (value.value.startsWith("Yes") && value.if_yes) {
              conditionalKey = "if_yes";
            } else if (value.value === "No" && value.if_no) {
              conditionalKey = "if_no";
            } else if (value.value === "Alive" && value.if_alive) {
              conditionalKey = "if_alive";
            } else if (value.value === "Deceased" && value.if_deceased) {
              conditionalKey = "if_deceased";
            }

            // Render conditional content if available
            if (conditionalKey && value[conditionalKey]) {
              renderNestedFields(
                value.value,
                value,
                conditionalContainer,
                key,
                category
              );
            }
          }

          updateTileStyle(fieldContainer, value.value || "");

          // Function to render nested fields
          function renderNestedFields(
            selectedOption,
            parentField,
            container,
            parentKey,
            parentCategory
          ) {
            // Determine which conditional key to use based on selected option
            let conditionalKey = null;
            if (selectedOption.startsWith("Yes") && parentField.if_yes) {
              conditionalKey = "if_yes";
            } else if (selectedOption === "No" && parentField.if_no) {
              conditionalKey = "if_no";
            } else if (selectedOption === "Alive" && parentField.if_alive) {
              conditionalKey = "if_alive";
            } else if (
              selectedOption === "Deceased" &&
              parentField.if_deceased
            ) {
              conditionalKey = "if_deceased";
            }

            if (!conditionalKey) return;

            // Show the container
            container.style.display = "block";

            // Render each nested field
            Object.entries(parentField[conditionalKey]).forEach(
              ([subKey, subField]) => {
                const subFieldContainer = document.createElement("div");
                subFieldContainer.classList.add("sub-field-container");
                subFieldContainer.style.marginBottom = "15px";

                // Create label for the sub-field
                const subLabel = document.createElement("label");
                subLabel.classList.add("sub-label");
                subLabel.textContent = `${subField.label}${
                  subField.metric ? ` (${subField.metric})` : ""
                } (${subField.field_id})`;
                if (subField.description) {
                  subLabel.setAttribute("title", subField.description);
                }
                subFieldContainer.appendChild(subLabel);

                // Handle different input types for nested fields
                if (
                  subField.input_type === "string" ||
                  subField.input_type === "text"
                ) {
                  const input = document.createElement("input");
                  input.type = "text";
                  input.name = `${parentKey}-${subKey}`;
                  input.placeholder = `Enter ${subField.label}`;
                  input.value = subField.value || "";

                  input.addEventListener("input", (e) => {
                    // Update the nested field value
                    patientData.in_hospital_adjudication[parentCategory][
                      parentKey
                    ][conditionalKey][subKey].value = e.target.value;
                    patientData.in_hospital_adjudication[parentCategory][
                      parentKey
                    ][conditionalKey][subKey].modified_by = "ABSTRACTOR";
                    updateContainerHighlight(
                      container,
                      patientData.in_hospital_adjudication
                    );
                  });

                  subFieldContainer.appendChild(input);
                } else if (subField.input_type === "date") {
                  const dateWrapper = document.createElement("div");
                  dateWrapper.style.position = "relative";

                  // Create display input
                  const displayInput = document.createElement("input");
                  displayInput.type = "text";
                  displayInput.name = `${parentKey}-${subKey}-display`;
                  displayInput.readOnly = true;
                  displayInput.value = formatDisplayDate(subField.value);
                  displayInput.placeholder = "MM/DD/YYYY";
                  displayInput.style.cursor = "pointer";

                  // Hidden date input
                  const dateInput = document.createElement("input");
                  dateInput.type = "date";
                  dateInput.name = `${parentKey}-${subKey}`;
                  dateInput.value = subField.value || "";
                  dateInput.style.position = "absolute";
                  dateInput.style.opacity = "0";
                  dateInput.style.cursor = "pointer";

                  // Set max date to today
                  const today = new Date().toISOString().split("T")[0];
                  dateInput.max = today;

                  dateInput.addEventListener("change", (e) => {
                    const selectedDate = e.target.value;
                    displayInput.value = formatDisplayDate(selectedDate);
                    patientData.in_hospital_adjudication[parentCategory][
                      parentKey
                    ][conditionalKey][subKey].value = selectedDate;
                    patientData.in_hospital_adjudication[parentCategory][
                      parentKey
                    ][conditionalKey][subKey].modified_by = "ABSTRACTOR";
                    updateContainerHighlight(
                      container,
                      patientData.in_hospital_adjudication
                    );
                  });

                  // Trigger date picker when clicking display input
                  displayInput.addEventListener("click", () => {
                    dateInput.showPicker();
                  });

                  dateWrapper.appendChild(displayInput);
                  dateWrapper.appendChild(dateInput);
                  subFieldContainer.appendChild(dateWrapper);
                } else if (subField.input_type === "radio") {
                  const radioContainer = document.createElement("div");
                  radioContainer.classList.add("radio-container");
                  radioContainer.style.display = "flex";
                  radioContainer.style.flexDirection = "column"; // Changed to column for vertical stacking
                  radioContainer.style.gap = "10px";

                  subField.options.forEach((option) => {
                    const radioWrapper = document.createElement("div");
                    radioWrapper.style.display = "flex";
                    radioWrapper.style.alignItems = "center";
                    radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

                    // Handle both string options and object options
                    const optionValue =
                      typeof option === "object" ? option.value : option;
                    const optionId =
                      typeof option === "object" ? option.id : option;





                    // If the field value is an object, extract its value property
                    let fieldValue = subField.value;
                    if (typeof fieldValue === "object" && fieldValue !== null) {

                      fieldValue = fieldValue.value;
                    }



                    const input = document.createElement("input");
                    input.type = "radio";
                    input.name = `radio-${parentCategory}-${parentKey}-${subKey}`; // Make name unique by including parentCategory
                    input.value = optionValue;
                    input.id = `${parentCategory}-${parentKey}-${subKey}-${optionId}`; // Make ID unique by including parentCategory
                    input.checked = fieldValue === optionValue;



                    input.addEventListener("change", (e) => {
                      if (e.target.checked) {


                        // Find the matching option object if it exists
                        const matchingOption = subField.options.find((opt) => {
                          const optVal =
                            typeof opt === "object" ? opt.value : opt;
                          return optVal === e.target.value;
                        });



                        // Update the nested field value with the actual value, not the object
                        patientData.in_hospital_adjudication[parentCategory][
                          parentKey
                        ][conditionalKey][subKey].value = e.target.value;
                        patientData.in_hospital_adjudication[parentCategory][
                          parentKey
                        ][conditionalKey][subKey].modified_by = "ABSTRACTOR";



                        // Update tile highlighting
                        updateTileStyle(subFieldContainer, e.target.value);
                        updateContainerHighlight(
                          container,
                          patientData.in_hospital_adjudication
                        );
                      }
                    });

                    const optionLabel = document.createElement("label");
                    optionLabel.setAttribute(
                      "for",
                      `${parentCategory}-${parentKey}-${subKey}-${optionId}` // Match the updated ID
                    );

                    // Special handling for deficit_type field
                    if (subKey === "deficit_type") {

                      optionLabel.innerText = optionValue;
                    } else {
                      optionLabel.innerText = optionValue;
                    }
                    if (subField.description) {
                      optionLabel.setAttribute("title", subField.description);
                    }

                    radioWrapper.appendChild(input);
                    radioWrapper.appendChild(optionLabel);
                    radioContainer.appendChild(radioWrapper);
                  });

                  subFieldContainer.appendChild(radioContainer);
                } else if (subField.input_type === "select") {
                  const select = document.createElement("select");
                  select.name = `${parentKey}-${subKey}`;

                  // If no value is set, add a default option
                  if (!subField.value) {
                    const defaultOption = document.createElement("option");
                    defaultOption.value = "";
                    defaultOption.innerText = "Select an option";
                    defaultOption.disabled = true;
                    defaultOption.selected = true;
                    select.appendChild(defaultOption);
                  }

                  subField.options.forEach((option) => {
                    const optionElement = document.createElement("option");
                    optionElement.value =
                      typeof option === "object" ? option.id : option;
                    optionElement.innerText =
                      typeof option === "object" ? option.value : option;
                    if (
                      subField.value ===
                      (typeof option === "object" ? option.value : option)
                    ) {
                      optionElement.selected = true;
                    }
                    select.appendChild(optionElement);
                  });

                  select.addEventListener("change", (e) => {
                    const selectedOption = subField.options.find(
                      (option) =>
                        (typeof option === "object" ? option.id : option) ===
                        e.target.value
                    );
                    const newValue = selectedOption
                      ? typeof selectedOption === "object"
                        ? selectedOption.value
                        : selectedOption
                      : "";

                    // Update the nested field value
                    patientData.in_hospital_adjudication[parentCategory][
                      parentKey
                    ][conditionalKey][subKey].value = newValue;
                    patientData.in_hospital_adjudication[parentCategory][
                      parentKey
                    ][conditionalKey][subKey].modified_by = "ABSTRACTOR";
                    updateContainerHighlight(
                      container,
                      patientData.in_hospital_adjudication
                    );
                  });

                  subFieldContainer.appendChild(select);
                } else if (subField.input_type === "multi_select") {
                  // Initialize the value as an array if it's not already
                  if (!Array.isArray(subField.value)) {
                    subField.value = subField.value ? [subField.value] : [];
                  }

                  // Ensure the nested field structure exists in the data model
                  if (!patientData.in_hospital_adjudication[parentCategory]) {
                    patientData.in_hospital_adjudication[parentCategory] = {};
                  }
                  if (!patientData.in_hospital_adjudication[parentCategory][parentKey]) {
                    patientData.in_hospital_adjudication[parentCategory][parentKey] = {};
                  }
                  if (!patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey]) {
                    patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey] = {};
                  }
                  if (!patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey][subKey]) {
                    patientData.in_hospital_adjudication[parentCategory][parentKey][conditionalKey][subKey] = {
                      ...subField,
                      value: Array.isArray(subField.value) ? subField.value : [],
                      modified_by: subField.modified_by || ""
                    };
                  }

                  // Create dropdown container with relative positioning
                  const dropdownContainer = document.createElement("div");
                  dropdownContainer.classList.add("dropdown-container");
                  dropdownContainer.style.position = "relative";
                  dropdownContainer.style.width = "100%";

                  // Make sure all parent containers allow overflow
                  subFieldContainer.style.overflow = "visible";
                  container.style.overflow = "visible";

                  // Create dropdown header/button
                  const dropdownHeader = document.createElement("div");
                  dropdownHeader.classList.add("dropdown-header");
                  dropdownHeader.style.padding = "8px 12px";
                  dropdownHeader.style.border = "1px solid #ccc";
                  dropdownHeader.style.borderRadius = "4px";
                  dropdownHeader.style.cursor = "pointer";
                  dropdownHeader.style.display = "flex";
                  dropdownHeader.style.justifyContent = "space-between";
                  dropdownHeader.style.alignItems = "center";
                  dropdownHeader.style.backgroundColor = "#fff";

                  // Display selected values or placeholder
                  const selectedText = document.createElement("span");
                  selectedText.classList.add("selected-text");

                  // Function to update the selected text display
                  const updateSelectedText = () => {
                    if (subField.value.length === 0) {
                      selectedText.textContent = "Select options...";
                    } else if (subField.value.length === 1) {
                      const selectedOption = subField.options.find((opt) => {
                        const optValue =
                          typeof opt === "object" ? opt.value : opt;
                        return optValue === subField.value[0];
                      });
                      selectedText.textContent = selectedOption
                        ? typeof selectedOption === "object"
                          ? selectedOption.value
                          : selectedOption
                        : subField.value[0];
                    } else {
                      // For multiple selected options, show the actual values
                      const selectedValues = subField.value.map((val) => {
                        const option = subField.options.find((opt) => {
                          const optValue =
                            typeof opt === "object" ? opt.value : opt;
                          return optValue === val;
                        });
                        return option
                          ? typeof option === "object"
                            ? option.value
                            : option
                          : val;
                      });
                      selectedText.textContent = selectedValues.join(", ");
                    }
                  };

                  updateSelectedText();

                  // Add dropdown arrow
                  const dropdownArrow = document.createElement("span");
                  dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

                  dropdownHeader.appendChild(selectedText);
                  dropdownHeader.appendChild(dropdownArrow);

                  // Create dropdown content (initially hidden)
                  const dropdownContent = document.createElement("div");
                  dropdownContent.classList.add("dropdown-content");
                  dropdownContent.style.display = "none";
                  dropdownContent.style.position = "absolute";
                  dropdownContent.style.top = "100%";
                  dropdownContent.style.left = "0";
                  dropdownContent.style.width = "100%";
                  dropdownContent.style.maxHeight = "200px";
                  dropdownContent.style.overflowY = "auto";
                  dropdownContent.style.backgroundColor = "#fff";
                  dropdownContent.style.border = "1px solid #ccc";
                  dropdownContent.style.borderRadius = "4px";
                  dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
                  dropdownContent.style.zIndex = "1000";

                  // Create a checkbox for each option
                  subField.options.forEach((option) => {
                    // Handle both object options and string options
                    const optionValue =
                      typeof option === "object" ? option.value : option;
                    const optionId =
                      typeof option === "object" ? option.id : option;

                    const checkboxWrapper = document.createElement("div");
                    checkboxWrapper.classList.add("checkbox-wrapper");
                    checkboxWrapper.style.display = "flex";
                    checkboxWrapper.style.alignItems = "center";
                    checkboxWrapper.style.padding = "8px 12px";
                    checkboxWrapper.style.borderBottom = "1px solid #eee";
                    checkboxWrapper.style.textAlign = "left";
                    checkboxWrapper.style.cursor = "pointer";

                    const input = document.createElement("input");
                    input.type = "checkbox";
                    input.name = `${parentCategory}-${parentKey}-${subKey}-${optionId}`; // Make name unique by including parentCategory
                    input.value = optionId;
                    input.id = `${parentCategory}-${parentKey}-${subKey}-${optionId}`; // Make ID unique by including parentCategory
                    input.style.marginRight = "8px";

                    // Check if this option is in the selected values array
                    input.checked = subField.value.includes(optionValue);

                    input.addEventListener("change", (e) => {
                      // Get the current values array
                      let currentValues = Array.isArray(subField.value)
                        ? [...subField.value]
                        : [];

                      if (e.target.checked) {
                        // Add the value if it's not already in the array
                        if (!currentValues.includes(optionValue)) {
                          currentValues.push(optionValue);
                        }
                      } else {
                        // Remove the value if it's in the array
                        currentValues = currentValues.filter(
                          (val) => val !== optionValue
                        );
                      }

                      // Update the model - use the properly initialized structure
                      patientData.in_hospital_adjudication[parentCategory][
                        parentKey
                      ][conditionalKey][subKey].value = currentValues;
                      patientData.in_hospital_adjudication[parentCategory][
                        parentKey
                      ][conditionalKey][subKey].modified_by = "ABSTRACTOR";

                      // Also update the subField reference for immediate UI updates
                      subField.value = currentValues;
                      subField.modified_by = "ABSTRACTOR";

                      // Update selected text display
                      updateSelectedText();
                      updateContainerHighlight(
                        container,
                        patientData.in_hospital_adjudication
                      );
                    });

                    const optionLabel = document.createElement("label");
                    optionLabel.setAttribute(
                      "for",
                      `${parentCategory}-${parentKey}-${subKey}-${optionId}` // Match the updated ID
                    );
                    if (typeof option === "object" && option.field_id) {
                      optionLabel.innerText = `${optionValue} (${option.field_id})`;
                    } else {
                      optionLabel.innerText = optionValue;
                    }

                    checkboxWrapper.appendChild(input);
                    checkboxWrapper.appendChild(optionLabel);
                    dropdownContent.appendChild(checkboxWrapper);
                  });

                  // Toggle dropdown on click
                  dropdownHeader.addEventListener("click", () => {
                    const isOpen = dropdownContent.style.display === "block";
                    dropdownContent.style.display = isOpen ? "none" : "block";
                    dropdownArrow.innerHTML = isOpen ? "&#9662;" : "&#9652;";
                  });

                  // Close dropdown when clicking outside
                  document.addEventListener("click", (e) => {
                    if (!dropdownContainer.contains(e.target)) {
                      dropdownContent.style.display = "none";
                      dropdownArrow.innerHTML = "&#9662;";
                    }
                  });

                  dropdownContainer.appendChild(dropdownHeader);
                  dropdownContainer.appendChild(dropdownContent);
                  subFieldContainer.appendChild(dropdownContainer);
                } else if (subField.input_type === "multi_input_field") {
                  // Handle nested multi_input_field
                  const nestedMultiInputContainer =
                    document.createElement("div");
                  nestedMultiInputContainer.classList.add(
                    "nested-multi-input-container"
                  );
                  nestedMultiInputContainer.style.marginTop = "10px";

                  // Apply initial highlighting for empty fields
                  updateTileStyle(subFieldContainer, subField.value);

                  // Create radio buttons container
                  const radioContainer = document.createElement("div");
                  radioContainer.classList.add("radio-container");
                  radioContainer.style.display = "flex";
                  radioContainer.style.flexDirection = "column"; // Changed to column for vertical stacking
                  radioContainer.style.gap = "10px";
                  radioContainer.style.width = "100%";

                  // Create container for conditional content
                  const nestedConditionalContainer =
                    document.createElement("div");
                  nestedConditionalContainer.classList.add(
                    "nested-conditional-content-container"
                  );
                  nestedConditionalContainer.style.marginTop = "10px";
                  nestedConditionalContainer.style.padding = "10px";
                  nestedConditionalContainer.style.border = "1px solid #eee";
                  nestedConditionalContainer.style.borderRadius = "4px";
                  nestedConditionalContainer.style.display = "none"; // Initially hidden

                  // Create radio buttons for each option
                  subField.options.forEach((option) => {
                    const radioWrapper = document.createElement("div");
                    radioWrapper.style.display = "flex";
                    radioWrapper.style.alignItems = "center";
                    radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

                    // Handle both string options and object options
                    const optionValue =
                      typeof option === "object" ? option.value : option;
                    const optionId =
                      typeof option === "object" ? option.id : option;





                    // If the field value is an object, extract its value property
                    let fieldValue = subField.value;
                    if (typeof fieldValue === "object" && fieldValue !== null) {

                      fieldValue = fieldValue.value;
                    }

                    // Special handling for deficit_type field


                    const input = document.createElement("input");
                    input.type = "radio";
                    input.name = `radio-${parentKey}-${subKey}`;
                    input.value = optionValue;
                    input.id = `${parentKey}-${subKey}-${optionId}`;
                    input.checked = fieldValue === optionValue;


                    input.addEventListener("change", (e) => {
                      if (e.target.checked) {


                        // Find the matching option object if it exists
                        const matchingOption = subField.options.find((opt) => {
                          const optVal =
                            typeof opt === "object" ? opt.value : opt;
                          return optVal === e.target.value;
                        });



                        // Update the field value
                        patientData.in_hospital_adjudication[parentCategory][
                          parentKey
                        ][conditionalKey][subKey].value = e.target.value;
                        patientData.in_hospital_adjudication[parentCategory][
                          parentKey
                        ][conditionalKey][subKey].modified_by = "ABSTRACTOR";



                        // Update tile highlighting
                        updateTileStyle(subFieldContainer, e.target.value);
                        updateContainerHighlight(
                          container,
                          patientData.in_hospital_adjudication
                        );

                        // Clear and render conditional content
                        nestedConditionalContainer.innerHTML = "";
                        nestedConditionalContainer.style.display = "none";

                        // Determine which conditional content to show
                        let nestedConditionalKey = null;
                        if (
                          e.target.value.startsWith("Yes") &&
                          subField.if_yes
                        ) {
                          nestedConditionalKey = "if_yes";
                        } else if (e.target.value === "No" && subField.if_no) {
                          nestedConditionalKey = "if_no";
                        } else if (
                          e.target.value === "Alive" &&
                          subField.if_alive
                        ) {
                          nestedConditionalKey = "if_alive";
                        } else if (
                          e.target.value === "Deceased" &&
                          subField.if_deceased
                        ) {
                          nestedConditionalKey = "if_deceased";
                        } else if (
                          e.target.value === "Infarction" &&
                          subField.if_infarction
                        ) {
                          nestedConditionalKey = "if_infarction";
                        } else if (
                          e.target.value === "Hemorrhage" &&
                          subField.if_hemorrhage
                        ) {
                          nestedConditionalKey = "if_hemorrhage";
                        } else if (
                          e.target.value === "Both" &&
                          subField.if_both
                        ) {
                          nestedConditionalKey = "if_both";
                        }

                        // Render conditional content if available
                        if (
                          nestedConditionalKey &&
                          subField[nestedConditionalKey]
                        ) {
                          // Show the container
                          nestedConditionalContainer.style.display = "block";

                          // Render each nested field
                          Object.entries(
                            subField[nestedConditionalKey]
                          ).forEach(([nestedSubKey, nestedSubField]) => {
                            const nestedSubFieldContainer =
                              document.createElement("div");
                            nestedSubFieldContainer.classList.add(
                              "nested-sub-field-container"
                            );
                            nestedSubFieldContainer.style.marginBottom = "15px";

                            // Create label for the nested sub-field
                            const nestedSubLabel =
                              document.createElement("label");
                            nestedSubLabel.classList.add("nested-sub-label");
                            nestedSubLabel.textContent = `${
                              nestedSubField.label
                            }${
                              nestedSubField.metric
                                ? ` (${nestedSubField.metric})`
                                : ""
                            } (${nestedSubField.field_id})`;
                            if (nestedSubField.description) {
                              nestedSubLabel.setAttribute(
                                "title",
                                nestedSubField.description
                              );
                            }
                            nestedSubFieldContainer.appendChild(nestedSubLabel);

                            // Handle different input types for nested sub-fields
                            if (
                              nestedSubField.input_type === "string" ||
                              nestedSubField.input_type === "text"
                            ) {
                              const input = document.createElement("input");
                              input.type = "text";
                              input.name = `${parentKey}-${subKey}-${nestedSubKey}`;
                              input.placeholder = `Enter ${nestedSubField.label}`;
                              input.value = nestedSubField.value || "";

                              input.addEventListener("input", (e) => {
                                // Update the nested sub-field value
                                patientData.in_hospital_adjudication[
                                  parentCategory
                                ][parentKey][conditionalKey][subKey][
                                  nestedConditionalKey
                                ][nestedSubKey].value = e.target.value;
                                patientData.in_hospital_adjudication[
                                  parentCategory
                                ][parentKey][conditionalKey][subKey][
                                  nestedConditionalKey
                                ][nestedSubKey].modified_by = "ABSTRACTOR";
                                updateContainerHighlight(
                                  container,
                                  patientData.in_hospital_adjudication
                                );
                              });

                              nestedSubFieldContainer.appendChild(input);
                            }

                            nestedConditionalContainer.appendChild(
                              nestedSubFieldContainer
                            );
                          });
                        }
                      }
                    });

                    const optionLabel = document.createElement("label");
                    optionLabel.setAttribute(
                      "for",
                      `${parentKey}-${subKey}-${optionId}`
                    );



                    optionLabel.innerText = optionValue;
                    if (subField.description) {
                      optionLabel.setAttribute("title", subField.description);
                    }

                    radioWrapper.appendChild(input);
                    radioWrapper.appendChild(optionLabel);
                    radioContainer.appendChild(radioWrapper);
                  });

                  nestedMultiInputContainer.appendChild(radioContainer);
                  nestedMultiInputContainer.appendChild(
                    nestedConditionalContainer
                  );
                  subFieldContainer.appendChild(nestedMultiInputContainer);

                  // If a value is already selected, render the conditional content
                  if (subField.value) {
                    // Determine which conditional content to show
                    let nestedConditionalKey = null;
                    if (subField.value.startsWith("Yes") && subField.if_yes) {
                      nestedConditionalKey = "if_yes";
                    } else if (subField.value === "No" && subField.if_no) {
                      nestedConditionalKey = "if_no";
                    } else if (
                      subField.value === "Alive" &&
                      subField.if_alive
                    ) {
                      nestedConditionalKey = "if_alive";
                    } else if (
                      subField.value === "Deceased" &&
                      subField.if_deceased
                    ) {
                      nestedConditionalKey = "if_deceased";
                    }

                    // Render conditional content if available
                    if (
                      nestedConditionalKey &&
                      subField[nestedConditionalKey]
                    ) {
                      // Show the container
                      nestedConditionalContainer.style.display = "block";

                      // Render each nested field
                      Object.entries(subField[nestedConditionalKey]).forEach(
                        ([nestedSubKey, nestedSubField]) => {
                          const nestedSubFieldContainer =
                            document.createElement("div");
                          nestedSubFieldContainer.classList.add(
                            "nested-sub-field-container"
                          );
                          nestedSubFieldContainer.style.marginBottom = "15px";

                          // Create label for the nested sub-field
                          const nestedSubLabel =
                            document.createElement("label");
                          nestedSubLabel.classList.add("nested-sub-label");
                          nestedSubLabel.textContent = `${
                            nestedSubField.label
                          }${
                            nestedSubField.metric
                              ? ` (${nestedSubField.metric})`
                              : ""
                          } (${nestedSubField.field_id})`;
                          if (nestedSubField.description) {
                            nestedSubLabel.setAttribute(
                              "title",
                              nestedSubField.description
                            );
                          }
                          nestedSubFieldContainer.appendChild(nestedSubLabel);

                          // Handle different input types for nested sub-fields
                          if (
                            nestedSubField.input_type === "string" ||
                            nestedSubField.input_type === "text"
                          ) {
                            const input = document.createElement("input");
                            input.type = "text";
                            input.name = `${parentKey}-${subKey}-${nestedSubKey}`;
                            input.placeholder = `Enter ${nestedSubField.label}`;
                            input.value = nestedSubField.value || "";

                            input.addEventListener("input", (e) => {
                              // Update the nested sub-field value
                              patientData.in_hospital_adjudication[
                                parentCategory
                              ][parentKey][conditionalKey][subKey][
                                nestedConditionalKey
                              ][nestedSubKey].value = e.target.value;
                              patientData.in_hospital_adjudication[
                                parentCategory
                              ][parentKey][conditionalKey][subKey][
                                nestedConditionalKey
                              ][nestedSubKey].modified_by = "ABSTRACTOR";
                              updateContainerHighlight(
                                container,
                                patientData.in_hospital_adjudication
                              );
                            });

                            nestedSubFieldContainer.appendChild(input);
                          }

                          nestedConditionalContainer.appendChild(
                            nestedSubFieldContainer
                          );
                        }
                      );
                    }
                  }
                }

                container.appendChild(subFieldContainer);
              }
            );
          }
        }

        const modifiedByDisplay = document.createElement("span");
        modifiedByDisplay.style.display = "block";
        modifiedByDisplay.style.textAlign = "right";
        modifiedByDisplay.style.marginTop = "-10px";
        modifiedByDisplay.style.color = "#8143d9";
        modifiedByDisplay.style.fontSize = "12px";

        // Ensure value is an object before accessing or setting properties
        if (typeof value !== "object" || value === null) {
          // Convert value to an object if it's not already
          const originalValue = value;
          value = {
            value: originalValue,
            modified_by: "",
          };
        } else if (!value.hasOwnProperty("modified_by")) {
          // Ensure modified_by property exists
          value.modified_by = "";
        }

        modifiedByDisplay.textContent = value.modified_by;

        tileWrapper.appendChild(fieldContainer);
        tileWrapper.appendChild(modifiedByDisplay);
        categoryContainer.appendChild(tileWrapper);
      });

      container.appendChild(categoryContainer);
    }
  );

  // Render dependency fields as combined fields
  Object.entries(dependencyFields).forEach(([fieldKey, dependency]) => {
    const category = dependency.category;
    const dependentCategory = dependency.dependentCategory;
    const dependentKey = dependency.dependentKey;

    const value = patientData.in_hospital_adjudication[category][fieldKey];
    const dependentField =
      patientData.in_hospital_adjudication[dependentCategory][dependentKey];

    // Create an outer wrapper to hold the field tile and the modified_by display
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px";

    // Create the field container
    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    // Create and append the label element
    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = value.metric
      ? `${value.label} (${value.metric}) (${value.field_id})`
      : `${value.label} (${value.field_id})`;
    if (value.description) {
      label.setAttribute("title", value.description);
    }
    fieldContainer.appendChild(label);

    // Create the modified_by display element
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    if (!value.modified_by) {
      value.modified_by = "";
    }
    modifiedByDisplay.textContent = value.modified_by;

    // Create a wrapper for the input and checkbox to display them side by side
    const inputWrapper = document.createElement("div");
    inputWrapper.style.display = "flex";
    inputWrapper.style.alignItems = "center";
    inputWrapper.style.gap = "10px";

    // Create the select input for Modified Rankin Scale
    const select = document.createElement("select");
    select.name = fieldKey;
    select.style.flexGrow = "1";

    // Add a default option
    if (!value.value) {
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.innerText = "Select an option";
      defaultOption.disabled = true;
      defaultOption.selected = true;
      select.appendChild(defaultOption);
    }

    // Add options from the value.options array
    if (value.options && Array.isArray(value.options)) {
      value.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value =
          typeof option === "object" ? option.value : option;
        optionElement.innerText =
          typeof option === "object" ? option.value : option;
        if (value.value === optionElement.value) {
          optionElement.selected = true;
        }
        select.appendChild(optionElement);
      });
    }

    // Create the N/A checkbox
    const checkboxWrapper = document.createElement("div");
    checkboxWrapper.style.display = "flex";
    checkboxWrapper.style.alignItems = "center";

    const checkbox = document.createElement("input");
    checkbox.type = "checkbox";
    checkbox.name = dependentKey;
    checkbox.id = `${dependentKey}-checkbox`;
    checkbox.checked = dependentField.value === "True";

    const checkboxLabel = document.createElement("label");
    checkboxLabel.setAttribute("for", `${dependentKey}-checkbox`);
    checkboxLabel.innerText =
      `${dependentField.label} (${dependentField.field_id})` ||
      "Not Administered";
    checkboxLabel.style.marginLeft = "5px";

    checkboxWrapper.appendChild(checkbox);
    checkboxWrapper.appendChild(checkboxLabel);

    // Set initial state based on current values
    if (dependentField.value === "True") {
      select.disabled = true;
      select.value = "";
      patientData.in_hospital_adjudication[category][fieldKey].value = "";
    }

    updateTileStyle(
      fieldContainer,
      value.value || (dependentField.value === "True" ? "True" : "")
    );

    // Add event listeners to make them act like radio buttons
    select.addEventListener("change", (e) => {
      const selectedValue = e.target.value;

      // Update model and UI
      patientData.in_hospital_adjudication[category][fieldKey].value =
        selectedValue;
      patientData.in_hospital_adjudication[category][fieldKey].modified_by =
        "ABSTRACTOR";

      // If select has value, disable the checkbox
      if (selectedValue) {
        checkbox.checked = false;
        patientData.in_hospital_adjudication[dependentCategory][
          dependentKey
        ].value = "";
      }

      updateTileStyle(fieldContainer, selectedValue);
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlight(container, patientData.in_hospital_adjudication);
    });

    checkbox.addEventListener("change", (e) => {
      const isChecked = e.target.checked;

      // Update the dependent field value
      patientData.in_hospital_adjudication[dependentCategory][
        dependentKey
      ].value = isChecked ? "True" : "";
      patientData.in_hospital_adjudication[dependentCategory][
        dependentKey
      ].modified_by = "ABSTRACTOR";

      // If checkbox is checked, disable and clear the select
      if (isChecked) {
        select.disabled = true;
        select.value = "";
        patientData.in_hospital_adjudication[category][fieldKey].value = "";
      } else {
        select.disabled = false;
      }

      updateTileStyle(fieldContainer, isChecked ? "True" : "");
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlight(container, patientData.in_hospital_adjudication);
    });

    // Append elements to the wrapper
    inputWrapper.appendChild(select);
    inputWrapper.appendChild(checkboxWrapper);
    fieldContainer.appendChild(inputWrapper);

    // Append field container and modified_by display to the tile wrapper
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);

    // Find the category container and append the tile wrapper to it
    const categoryContainer = document.querySelector(
      `.category-container[data-category="${category}"]`
    );
    if (categoryContainer) {

      categoryContainer.appendChild(tileWrapper);
    } else {

      container.appendChild(tileWrapper);
    }
  });

  // Create the container-level verified checkbox with standard styling
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";

  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "in-hospital-adjudication-verified-checkbox";
  containerVerifiedCheckbox.checked =
    patientData.in_hospital_adjudication.verified.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";

  containerVerifiedCheckbox.addEventListener("change", (e) => {
    patientData.in_hospital_adjudication.verified.value = e.target.checked
      ? "True"
      : "False";
    patientData.in_hospital_adjudication.verified.modified_by = "ABSTRACTOR";
    updateContainerHighlight(container, patientData.in_hospital_adjudication);
  });

  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "in-hospital-adjudication-verified-checkbox"
  );
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";

  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);

  // Initial update of container highlight
  updateContainerHighlight(container, patientData.in_hospital_adjudication);


}
