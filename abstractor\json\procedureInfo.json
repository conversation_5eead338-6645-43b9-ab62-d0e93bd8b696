{"procedureInfo": {"verified": "", "pre_procedure_diagnostics": {"verified": "", "transesophageal_echocardiogram_tee_performed": {"field_id": "14828", "label": "Transesophageal Echocardiogram (TEE) Performed", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"most_recent_tee_date": {"field_id": "14829", "label": "Most Recent TEE Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date of the most recent transesophageal echocardiogram (TEE) performed prior to the device insertion or attempted device insertion during the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the date of the most recent transesophageal echocardiogram (TEE) performed prior to the device insertion or attempted device insertion during the current procedure.\n**Target Value:**\nThe last value between 1 week prior to current procedure and current procedure"}, "atrial_thrombus_detected": {"field_id": "14838", "label": "Atrial Thrombus Detected", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Based on the provided coder dictionary entry, here is a well-formed question to retrieve the requested data:\n\n\"Was an atrial thrombus detected or suspected in the patient? Please provide details on whether the thrombus had clear borders, echogenicity from the surrounding structures, independent mobility, a longest diameter greater than 15mm, and if it was seen in more than one echocardiographic plane.\"", "description": "\n**Coding Instruction:**\nIndicate if an atrial thrombus was detected or suspected.\n**Target Value:**\nThe last value between 1 week prior to current procedure and current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was a transesophageal echocardiogram (TEE) performed prior to the device insertion or attempted device insertion during the current procedure?", "description": "\n**Coding Instruction:**\nIndicate if transesophageal echocardiogram (TEE) was performed prior to the device insertion or attempted device insertion during the current procedure.\n**Target Value:**\nThe last value between 1 week prior to current procedure and current procedure"}, "laa_orifice_maximal_width": {"field_id": "14830", "label": "LAA Orifice Maximal Width", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the maximal orifice width of the left atrial appendage (LAA) in millimeters?", "description": "\n**Coding Instruction:**\nIndicate the maximal orifice width of the left atrial appendage (LAA) in mm.\n**Target Value:**\nThe last value between 1 week prior to current procedure and current procedure"}}, "procedure": {"procedure_start_date_and_time": {"field_id": "7000", "label": "Procedure Start Date and Time", "input_type": "date_time", "value": "", "modified_by": "", "value_source": "Database", "value_path": "case_summary.procedure_date&procedure_time", "value_type": "concat", "q_prompt": "What is the date and time the procedure started, defined as the time at which local anesthetic was first administered for vascular access, or the time of the first attempt at vascular access for the interventional procedure (whichever is earlier)? Please provide the date and time in the format mm/dd/yyyy hours:minutes using the military 24-hour clock.", "description": "\n**Coding Instruction:**\nIndicate the date and time the procedure started.\n**Target Value:**\nAny occurrence on current procedure"}, "procedure_stop_date_and_time": {"field_id": "7005", "label": "Procedure Stop Date and Time", "input_type": "date_time", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the ending date and time at which the operator completes the procedure and breaks scrub at the end of the procedure? If more than one operator is involved, please provide the date and time the last operator breaks scrub for the last time.", "description": "\n**Coding Instruction:**\nIndicate the ending date and time at which the operator completes the procedure and breaks scrub at the end of the procedure.\n**Target Value:**\nThe value on current procedure"}, "operator_name_npi": {"field_id": "14861, 14860, 14862/14863", "label": "Operator Name/NPI", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings"}, "fellow_name_npi_fellowship_program_id": {"field_id": "15433, 15434, 15435/15436/15431", "label": "Fellow Name/NPI/Fellowship Program ID", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings"}, "shared_decision_making": {"field_id": "14732", "label": "Shared Decision Making", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"sdm_tool_used": {"field_id": "14733", "label": "SDM Tool Used", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"sdm_tool_name": {"field_id": "14734", "label": "SDM Tool Name", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What tool was used for shared decision making in the current TAVR procedure? If the tool used is not listed in the provided options, <NAME_EMAIL> to have a selection added.", "description": "\n**Coding Instruction:**\nIndicate what tool was used. If the tool used is not in the drop-down list, <NAME_EMAIL> to have a selection added.\n**Target Value:**\nThe value on current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What shared decision making tool was used for the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if a shared decision making tool was used.\n**Target Value:**\nThe value on current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was shared decision making performed for the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if shared decision making was performed for the procedure.\n**Target Value:**\nThe value on current procedure"}, "procedure_location": {"field_id": "12871", "label": "Procedure Location", "options": [{"id": "1", "value": "OR"}, {"id": "2", "value": "Hybrid OR"}, {"id": "3", "value": "Cath Lab"}, {"id": "4", "value": "Hybrid Cath Lab"}, {"id": "5", "value": "EP Lab"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the location where the TAVR procedure was performed? Please select from the following options:\n- Operating Room\n- Hybrid Operating Room Suite\n- Cardiac Catheterization Laboratory\n- Hybrid Catheterization Laboratory Suite\n- EP Lab", "description": "\n**Coding Instruction:**\nIndicate the location where the procedure was performed.\n**Target Value:**\nThe value on current procedure"}, "sedation": {"field_id": "7130", "label": "Sedation", "options": [{"id": "1", "value": "Minimal Sedation/Anxiolysis"}, {"id": "2", "value": "Deep Sedation/Analgesia"}, {"id": "3", "value": "Moderate Sedation/Analgesia (Conscious Sedation)"}, {"id": "4", "value": "General Anesthesia"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of sedation was used for the TAVR procedure? The options are:\n- Minimal Sedation/Anxiolysis\n- Moderate Sedation/Analgesia (Conscious Sedation)\n- Deep Sedation/Analgesia\n- General Anesthesia", "description": "\n**Coding Instruction:**\nIndicate the type of sedation used for the intervention.\n**Target Value:**\nThe value on current procedure"}, "laa_occlusion_indication": {"field_id": "14837", "label": "LAA Occlusion Indication", "options": [{"id": "1", "value": "High fall risk"}, {"id": "2", "value": "History of major bleed"}, {"id": "3", "value": "Clinically significant bleeding risk (Other than those listed here)"}, {"id": "4", "value": "Increased thromboembolic stroke risk"}, {"id": "5", "value": "Labile INR"}, {"id": "6", "value": "Non-compliance with anticoagulation therapy"}, {"id": "7", "value": "Patient preference"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the documented indication for the left atrial appendage (LAA) occlusion procedure? Please select from the following options:\n- High fall risk\n- History of major bleed\n- Clinically significant bleeding risk (Other than those listed here)\n- Increased thromboembolic stroke risk\n- Labile INR\n- Non-compliance with anticoagulation therapy\n- Patient preference", "description": "\n**Coding Instruction:**\nProvide the documented indication for the left atrial appendage (LAA) occlusion procedure.\n**Target Value:**\nThe value on current procedure"}, "procedure_canceled": {"field_id": "14834", "label": "Procedure Canceled", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"procedure_canceled_reason": {"field_id": "14833", "label": "Procedure Canceled Reason", "options": [{"id": "1", "value": "Anatomy not conducive for implant"}, {"id": "2", "value": "Appendage too large (for device implant)"}, {"id": "3", "value": "Appendage too small (for device implant)"}, {"id": "4", "value": "Catheterization challenge"}, {"id": "5", "value": "Decompensation in patient condition"}, {"id": "6", "value": "Epicardial access issue"}, {"id": "7", "value": "<PERSON><PERSON><PERSON><PERSON> detected"}, {"id": "8", "value": "Unanticipated patient condition"}, {"id": "9", "value": "Patient/Family choice"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the reason for the cancellation of the TAVR procedure? Please select from the following options:\n- Anatomy not conducive for implant\n- Appendage too large (for device implant)\n- Appendage too small (for device implant)\n- Catheterization challenge\n- Decompensation in patient condition\n- Epicardial access issue\n- Thrombus detected\n- Unanticipated patient condition\n- Patient/Family choice", "description": "\n**Coding Instruction:**\nIndicate the reason(s) why the procedure was canceled.\n**Target Value:**\nThe value on current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the procedure canceled after the patient had entered the procedure room but before venous or epicardial access was obtained?", "description": "\n**Coding Instruction:**\nIndicate if the procedure was canceled after the patient had entered the procedure room AND before venous or epicardial access was obtained.\n**Target Value:**\nThe value on current procedure"}}, "device_information": {"access_system": {"field_id": "14839", "label": "Access System", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What access system(s) were utilized during the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate the access system(s) utilized during the current procedure.\n**Target Value:**\nThe value on current procedure"}, "device": {"field_id": "14841", "label": "<PERSON><PERSON>", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the device ID of the device(s) utilized during the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate the device(s) utilized during the current procedure.\n**Target Value:**\nThe value on current procedure"}, "udi": {"field_id": "14843", "label": "UDI", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used in the current TAVR procedure? This identifier is provided by the device manufacturer and is either a GTIN or HIBBC number.", "description": "\n**Coding Instruction:**\n[Reserved for Future Use] Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used. This ID is provided by the device manufacturer, and is either a GTIN or HIBBC number.\n**Target Value:**\nThe value on current procedure"}, "laa_isolation_approach": {"field_id": "14844", "label": "LAA Isolation Approach", "options": ["Epicardial", "Percutaneous"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What approach was used to deliver the closure device for the left atrial appendage (LAA) isolation during the current procedure? Please specify if it was \"Epicardial\" or \"Percutaneous\".", "description": "\n**Coding Instruction:**\nIndicate which approach was used to deliver the closure device.\n**Target Value:**\nThe value on current procedure"}, "device_successfully_deployed": {"field_id": "14968", "label": "<PERSON>ce Successfully Deployed", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the device successfully deployed during the procedure?", "description": "\n**Coding Instruction:**\nIndicate whether the device was successfully deployed.\n**Target Value:**\nThe value on current procedure"}, "reason_device_not_deployed_successfully": {"field_id": "14845", "label": "Reason Device Not Deployed Successfully", "options": [{"id": "1", "value": "Deployed, not released"}, {"id": "2", "value": "Not deployed"}, {"id": "3", "value": "<PERSON><PERSON> retrieved"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "description": "\n**Coding Instruction:**\nIndicate the outcome listed for the device unsuccessfully deployed.\n**Target Value:**\nThe value on current procedure"}}, "procedure_aborted": {"procedure_aborted": {"field_id": "14831", "label": "Procedure Aborted", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"procedure_aborted_reason": {"field_id": "14832", "label": "Procedure Aborted Reason", "options": [{"id": "1", "value": "Anatomy not conducive for implant"}, {"id": "2", "value": "Appendage too large (for device implant)"}, {"id": "3", "value": "Appendage too small (for device implant)"}, {"id": "4", "value": "Catheterization challenge"}, {"id": "5", "value": "Decompensation in patient condition"}, {"id": "6", "value": "Device related"}, {"id": "7", "value": "Transcatheter device retrieval"}, {"id": "8", "value": "Device release criteria not met"}, {"id": "9", "value": "Epicardial access issue"}, {"id": "10", "value": "Surgical device retrieval"}, {"id": "11", "value": "Device associated thrombus developed during procedure"}, {"id": "12", "value": "Unanticipated patient condition"}, {"id": "13", "value": "Patient/Family choice"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the reason for aborting the TAVR procedure? Please select from the following options:\n- Anatomy not conducive for implant\n- Appendage too large (for device implant)\n- Appendage too small (for device implant)\n- Catheterization challenge\n- Decompensation in patient condition\n- Device related\n- Transcatheter device retrieval\n- Device release criteria not met\n- Epicardial access issue\n- Surgical device retrieval\n- Device associated thrombus developed during procedure\n- Unanticipated patient condition\n- Patient/Family choice", "description": "\n**Coding Instruction:**\nIndicate the reason(s) why the procedure was aborted.\n**Target Value:**\nThe value on current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Is the LAAO intervention aborted at any time after venous or epicardial access was obtained?", "description": "\n**Coding Instruction:**\nIndicate if the LAAO intervention was aborted at any time after venous or epicardial access was obtained.\n**Target Value:**\nThe value on current procedure"}}, "device_margin_residual_leak": {"device_margin_residual_leak": {"field_id": "14848", "label": "<PERSON><PERSON> Residual Leak", "input_type": "text", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the size (in mm) of the residual leak noted at the device margin for the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate the size (in mm) of the residual leak noted at the device margin.\n**Target Value:**\nThe value on current procedure"}, "not_assessed": {"field_id": "14849", "label": "Not Assessed", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the device margin assessed for any potential residual leak during the current procedure? If not, please indicate that it was not assessed.", "description": "\n**Coding Instruction:**\nIndicate if the device margin was not assessed for any potential residual leak.\n**Target Value:**\nThe value on current procedure"}}, "guidance_method": {"guidance_method": {"field_id": "7200", "label": "Guidance Method(s)", "options": [{"id": "1", "value": "Intracardiac 3D Echo"}, {"id": "2", "value": "Electro Anatomic Mapping"}, {"id": "3", "value": "Fluoroscopy"}, {"id": "4", "value": "Transesophageal Echo (TEE)"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What guidance method was used for this TAVR procedure? Please select from the following options:\n- Intracardiac three dimensional echocardiography\n- Electro Anatomic Mapping\n- Fluoroscopy\n- Transesophageal Echocardiogram (TEE)", "description": "\n**Coding Instruction:**\nIndicate the assigned identification number associated with the guidance method used for this procedure.\n**Target Value:**\nThe value on current procedure"}}, "conversion_to_open_heart_surgery": {"conversion_to_open_heart_surgery": {"field_id": "14846", "label": "Conversion to Open Heart Surgery", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"reason": {"field_id": "14847", "label": "Reason", "options": [{"id": "1", "value": "Complication"}, {"id": "2", "value": "Device Retrieval"}, {"id": "3", "value": "Unfavorable Anatomy"}, {"id": "4", "value": "Medical decision for open ligation of appendage"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the reason for converting the TAVR procedure to open heart surgery? Please select from the following options:\n- Complication\n- Device Retrieval\n- Unfavorable Anatomy\n- Medical decision for open ligation of appendage", "description": "\n**Coding Instruction:**\nIndicate the reason why the procedure converted to open heart surgical access.\n**Target Value:**\nThe value on current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the status of the procedure regarding conversion to open heart surgery? Indicate if this TAVR procedure was converted to open heart surgery.", "description": "\n**Coding Instruction:**\nIndicate if this procedure converted to open heart surgery.\n**Target Value:**\nThe value on current procedure"}}, "concomitant_procedure": {"concomitant_procedure_performed": {"field_id": "14855", "label": "Concomitant Procedure(s) Performed", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"concomitant_procedure_type": {"field_id": "14857", "label": "Concomitant Procedure Type", "options": [{"id": "1", "value": "AFib Ablation"}, {"id": "2", "value": "ICD"}, {"id": "3", "value": "PCI"}, {"id": "4", "value": "TAVR"}, {"id": "5", "value": "TMVR"}, {"id": "6", "value": "ASD Closure Congenital"}, {"id": "7", "value": "ASD Closure Iatrogenic"}, {"id": "8", "value": "PFO Closure Congenital"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What specific other procedures were performed during the same lab visit as the current TAVR procedure? Please select from the following options:\n- AFib Ablation\n- ICD\n- PCI\n- TAVR\n- TMVR\n- ASD Closure Congenital\n- ASD Closure Iatrogenic\n- PFO Closure Congenital", "description": "\n**Coding Instruction:**\nIndicate which specific other procedures were performed during the same lab visit.\n**Target Value:**\nThe value on current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What other procedures (such as Afib Ablation, ICD, PCI, TAVR, or TMVR) were performed during the same lab visit as the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if other procedures (Afib Ablation, ICD, PCI, TAVR or TMVR) were performed during the same lab visit as this procedure.\n**Target Value:**\nThe value on current procedure"}}}}