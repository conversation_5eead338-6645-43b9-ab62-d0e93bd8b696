<nav id="navbar"
  class="w-[40%] hidden md:block bg-gray-200 text-black absolute right-0 top-0 h-full z-10 shadow-md transform translate-x-full transition-transform duration-300">
  <div class="flex flex-col items-center py-8">
    <button id="closeNav" class="self-end mr-4 text-2xl font-bold hover:text-blue-300">&times;</button>
    <div class="w-full h-full" id="caseDetail"></div>
  </div>
</nav>


<div class="absolute w-full hidden h-full flex items-center justify-center bg-white bg-opacity-75" id="scheduleCases">
  <div
    class="relative bg-white rounded-xl shadow-2xl p-8 w-full max-w-md transform transition-all duration-500 hover:scale-105">
    <!-- Close Button -->
    <button onclick="closeModal()"
      class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring focus:ring-purple-300 rounded-full">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>

    <!-- Header -->
    <h2 class="text-2xl font-extrabold text-purple-800 text-center mb-6">Schedule Cases</h2>

    <!-- Form -->
    <form id="scheduleForm" onsubmit="addSchedule(event)" class="space-y-6">
      <!-- Date Input -->
      <div>
        <label for="caseDate" class="block text-sm font-semibold text-gray-700 mb-2">Date <span
            class="text-red-500">*</span></label>
        <input type="date" id="caseDate" name="caseDate"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition duration-300 sm:text-sm"
          required />
        <p id="dateError" class="text-sm text-red-500 hidden">Please select a date.</p>
      </div>

      <!-- Sites Dropdown -->
      <div>
        <label for="site_id" class="block text-sm font-semibold text-gray-700 mb-2">Sites <span
            class="text-red-500">*</span></label>
        <select id="site_id" name="site_id"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm bg-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition duration-300 sm:text-sm"
          disabled required>
          <option value="" disabled selected>Select a site</option>
        </select>
        <p id="siteError" class="text-sm text-red-500 hidden">Please select a site.</p>
      </div>

      <!-- Patient Dropdown -->
      <div>
        <label for="patientDropdown" class="block text-sm font-semibold text-gray-700 mb-2">Patient <span
            class="text-red-500">*</span></label>
        <select id="patientDropdown" name="patientDropdown"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm bg-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition duration-300 sm:text-sm"
          disabled required>
          <option value="" disabled selected>Select the Patient</option>
          <!-- Patient options will be populated dynamically -->
        </select>
        <p id="patientError" class="text-sm text-red-500 hidden">Please select a patient.</p>
      </div>


      <!-- Submit Button -->
      <div class="flex justify-center">
        <button type="submit"
          class="px-6 py-3 bg-purple-500 text-white text-lg font-bold rounded-lg shadow-lg hover:bg-purple-600 focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:outline-none transition-transform duration-300 transform hover:scale-105">
          Schedule
        </button>
      </div>
    </form>
  </div>
</div>


<!-- Modal Overlay -->
<div class="fixed hidden inset-0 bg-black bg-opacity-50 flex items-center justify-center" id="scheduleModal">
  <!-- Modal Content -->
  <div class="bg-white rounded-lg shadow-lg max-w-sm w-full">
    <!-- Header -->
    <div id="scheduleHeader"
      class="flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg">
      <span id="scheduleHeaderTitle"></span>
    </div>
    <!-- Body -->
    <div class="p-4 text-center">
      <p class="text-gray-600" id="scheduleDes"></p>
    </div>
    <!-- Footer -->
    <div class="p-4 flex justify-center">
      <button id="scheduleOkBtn"
        class="px-4 py-2 bg-green-500 text-white font-medium rounded hover:bg-green-600 focus:ring focus:ring-green-300">
        OK
      </button>
    </div>
  </div>
</div>

<div class="hidden md:block">
  <!-- Calendar Header -->
  <div id="calendar_header" class="flex mx-4 mt-6 items-center justify-between">
    <div id="month_key" class="flex items-center space-x-3">
      <span class="text-lg font-bold" id="monthYear">Month Year</span>
      <button class="rounded-full bg-gray-200 px-3 py-2 hover:bg-gray-300" onclick="prevMonth()">
        <i class="fa-solid fa-chevron-left text-purple-700"></i>
      </button>
      <button class="rounded-full bg-gray-200 px-3 py-2 hover:bg-gray-300" onclick="nextMonth()">
        <i class="fa-solid fa-chevron-right text-purple-700"></i>
      </button>

    </div>
    <div class="flex items-center space-x-3">
      <select class="bg-gray-200 rounded-md px-3 py-2 hover:bg-gray-300" id="month_week"
        onchange="monthWeekCalendarChange()">
        <option value="Week">Week</option>
        <option value="Month">Month</option>
      </select>
      <button class="rounded-md bg-purple-700 text-white px-4 py-2 shadow-md hover:bg-gray-600"
        onclick="addScheduleCases()">
        + Add Schedule Cases
      </button>
    </div>
  </div>

  <!-- <div class="absolute w-full h-full items-center justify-center bg-white bg-opacity-50" id="scheduleCases">

  </div> -->





  <div id="weeklycalendar" class="hidden w-full h-full grid justify-center">

  </div>

  <div id="monthCalendar" class="hidden">
    <!-- Weekdays Header -->
    <div id="weekdays" class="grid mt-6 grid-cols-7 gap-2 text-center">
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Sun</div>
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Mon</div>
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Tue</div>
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Wed</div>
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Thu</div>
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Fri</div>
      <div class="font-bold bg-gray-300 rounded-lg py-2 text-gray-700">Sat</div>
    </div>

    <!-- Calendar Days -->
    <div id="calendarDays" class="mt-4 grid grid-cols-7 gap-2"></div>
  </div>
</div>


<!-- // mobile View  -->
<div class="block sm:hidden h-full flex flex-col ">
  <div class="flex flex-row items-center justify-between gap-x-2">
    <div class="flex flex-row items-end">
      <span class="text-lg font-bold" id="monthYearMobile">Month Year</span>
      <div class="relative flex items-center">
        <!-- Button with calendar icon -->
        <button id="openMonthPicker" class="flex items-center justify-center p-2 text-white  rounded "
          onclick="document.getElementById('monthPicker').showPicker();">
          <i class="fa-solid fa-calendar" style="color: black;"></i>
        </button>

        <!-- Hidden Month Picker -->
        <input id="monthPicker" type="month" class="absolute top-0 left-0 w-0 h-0 opacity-0" />
      </div>
    </div>
    <button class="rounded-md text-xs bg-purple-700 text-white px-4 py-2 shadow-md hover:bg-gray-600"
      onclick="addScheduleCases()">
      + Schedule Cases
    </button>
  </div>


  <div class="week-container-wrapper  w-full flex flex-col h-full">
    <div id="weekContainer" class="flex  justify-between items-center"></div>
    <div id="mobile_view_header" class="flex flex-row mx-2 items-center justify-between mt-2">
      <span id="date_time_mobile" class="text-lg font-semibold"></span>
      <span id="patient_count" class="text-purple-500 text-sm"></span>
    </div>
    <div id="mobile_appointment_list" class="bg-white rounded-lg w-full h-full">
    </div>
  </div>
</div>
</div>
</div>
</div>




<script src="./js/calendar.js"></script>