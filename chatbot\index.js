const chatContainer = document.getElementById("chat-container");
const chatInput = document.getElementById("chat-input");
const charLimit = document.getElementById("char-limit");
const sendButton = document.getElementById("send-button");
const imageModal = document.getElementById("image-modal");
const modalImage = document.getElementById("modal-image");
const closeModal = document.getElementById("close-modal");
const clearBtn = document.getElementById("clear-history-btn");
const clearModal = document.getElementById("clear-history-modal");
const confirmBtn = document.getElementById("confirm-clear-btn");
const cancelClearBtn = document.getElementById("cancel-clear-btn");
const closeClearModal = document.getElementById("close-clear-modal");
const urlParams = new URLSearchParams(window.location.search);
function getParam(key) {
  if (platform === "web" && window.parent && window.parent.location) {
    const parentParams = new URLSearchParams(window.parent.location.search);
    return parentParams.get(key) || urlParams.get(key);
  }
  return urlParams.get(key);
}

function getScreenContext() {
  if (platform === "web" && window.parent && window.parent.location) {
    const parentPath = window.parent.location.pathname;
    return parentPath.startsWith("/") ? parentPath.substring(1) : parentPath;
  }
  return urlParams.get("screen") || "";
}

let platform = urlParams.get("platform");
if (platform !== "mobile") {
  platform = "web";
}

let access_token, userId;
if (platform === "mobile") {
  access_token = urlParams.get("access_token");
  userId = urlParams.get("user_id");
} else {
  access_token = localStorage.getItem("access_token");
  userId = localStorage.getItem("user_id");
}

let caseId = getParam("case_id") || "";
let screenContext = getScreenContext();

// const platform = "mobile";
// const screenContext = "ScheduleTab/Patient Details/Pre Op";

// access_token =
//   "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI4SjFVczVUeG0yamJMM2ZqLXoyVHVXX2VpUXFvZDVNQllsa2Rxckt1WHJJIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qwd0_yq4TKJKMMudyYKF_aGEUrDEDi-g1HH7CodJh6WhW9iJrC_kimKyUqwEmFZB2QSJzh9SZ3VoL4m22cqryjms8oQcuSLbSWKVvNAncUa-mcwwZacWenZcFZwgPEm6U4Q_ba0b4JM6Sp0ep2AUstWJ2HktrWSh-G9XJKHe2bVVGS8cayw-qbvtwFu5j106WRWPb8RhCNdC7YOhUJQro5t8LBLRZQ-q6T1-BfBrDeU8XL4pcsRj9dWBUGnSl-IbhmeH9vtAgv-wzZFx6ZvT7mJJ2Bn0CUpb0aYkoFu7Ke0QO911rjVJhv8DnJcagJfgAbER47tMjuNwtid05LbHKw";
// userId = "678a642b728cefa9e2567a2b";
// caseId = "67ce9156a50521bb534944ac";

let chatHistoryLoaded = false;
let isWaitingForResponse = false;
let defaultMessageEl = null;
let previousInput = "";
let scale = 1;
let startX = 0;
let startY = 0;
let translateX = 0;
let translateY = 0;
let lastTouchDist = null;
let isPanning = false;
let panStartX = 0;
let panStartY = 0;

// Hide loader on page load
window.addEventListener("load", () => {
  const loader = document.getElementById("loader");
  loader.style.display = "none";
});

// Disable the send button initially
sendButton.disabled = true;
sendButton.style.backgroundColor = "#888888";

function scrollToBottom() {
  chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Event listener for input changes in the textarea
chatInput.addEventListener("input", () => {
  chatInput.style.height = "auto";
  chatInput.style.height = `${Math.min(chatInput.scrollHeight, 100)}px`;

  // Update character limit display
  const currentLength = chatInput.value.length;
  charLimit.textContent = `${currentLength}/2000`;

  // Enable/disable send button
  if (isWaitingForResponse || currentLength === 0 || currentLength > 2000) {
    sendButton.disabled = true;
    sendButton.style.backgroundColor = "#888888"; // Disabled color
  } else {
    sendButton.disabled = false;
    sendButton.style.backgroundColor = "#8143d9"; // Enabled color
  }
});

// Add click event listener to the send button
sendButton.addEventListener("click", () => {
  sendMessage();
});

// Handle Enter key for sending messages
chatInput.addEventListener("keydown", function (event) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
});

chatInput.addEventListener("focus", () => {
  setTimeout(scrollToBottom, 300); // Delay ensures the keyboard animation completes
});

// Handle image modal open
document.addEventListener("click", (event) => {
  if (event.target.tagName === "IMG" && event.target.src) {
    modalImage.src = event.target.src;
    imageModal.style.display = "flex";
  }
});

// Handle image modal close
closeModal.addEventListener("click", () => {
  imageModal.style.display = "none";
});

imageModal.addEventListener("click", (event) => {
  if (event.target === imageModal) {
    imageModal.style.display = "none";
  }
});

// Add zoom controls
modalImage.addEventListener("wheel", (event) => {
  event.preventDefault();
  scale += event.deltaY * -0.01;
  scale = Math.min(Math.max(1, scale), 5);
  modalImage.style.transform = `scale(${scale})`;
});

imageModal.addEventListener("touchmove", (event) => {
  if (event.touches.length === 2) {
    event.preventDefault();

    const dist = Math.sqrt(
      Math.pow(event.touches[0].pageX - event.touches[1].pageX, 2) +
        Math.pow(event.touches[0].pageY - event.touches[1].pageY, 2)
    );

    if (lastTouchDist) {
      scale += (dist - lastTouchDist) * 0.005;
      scale = Math.min(Math.max(1, scale), 5); // Limit scale between 1x and 5x
      modalImage.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
    }

    lastTouchDist = dist;
  } else if (event.touches.length === 1 && scale > 1) {
    // Handle panning for a single touch if zoomed in
    if (!isPanning) {
      isPanning = true;
      panStartX = event.touches[0].pageX;
      panStartY = event.touches[0].pageY;
    } else {
      const deltaX = event.touches[0].pageX - panStartX;
      const deltaY = event.touches[0].pageY - panStartY;
      panStartX = event.touches[0].pageX;
      panStartY = event.touches[0].pageY;

      translateX += deltaX;
      translateY += deltaY;

      modalImage.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
    }
  }
});

// Reset zoom and panning on touch end
imageModal.addEventListener("touchend", () => {
  lastTouchDist = null;
  isPanning = false;
});

// Open image modal
document.addEventListener("click", (event) => {
  if (event.target.tagName === "IMG" && event.target.src) {
    modalImage.src = event.target.src;
    imageModal.style.display = "flex";
    scale = 1;
    translateX = 0;
    translateY = 0;
    modalImage.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
  }
});

// Close image modal
closeModal.addEventListener("click", () => {
  imageModal.style.display = "none";
});

imageModal.addEventListener("click", (event) => {
  if (event.target === imageModal) {
    imageModal.style.display = "none";
  }
});

function formatTimestamp(ts) {
  let date;

  // If ts is a string and does not end with "Z" or have a timezone offset, append "Z".
  if (typeof ts === "string" && !/[zZ]|[+\-]\d{2}:?\d{2}$/.test(ts)) {
    date = new Date(ts + "Z");
  } else {
    date = new Date(ts);
  }

  // Local time components
  let hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12; // Convert hour '0' to '12'
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const year = date.getFullYear();

  return `${month}-${day}-${year}, ${hours}:${
    minutes < 10 ? "0" + minutes : minutes
  } ${ampm}`;
}

/* New Helper function: Formats a date to "mm-dd-yyyy, hh:mm AM/PM" using UTC time */
function formatUTCTimestamp(date) {
  let hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12;
  const month = ("0" + (date.getUTCMonth() + 1)).slice(-2);
  const day = ("0" + date.getUTCDate()).slice(-2);
  const year = date.getUTCFullYear();
  return `${month}-${day}-${year}, ${hours}:${
    minutes < 10 ? "0" + minutes : minutes
  } ${ampm}`;
}

const handleNavigateClick = (navigateUrl, caseIdFromResponse, patientName) => {
  if (!navigateUrl || !navigateUrl.screen) {
    console.error("Invalid navigateUrl:", navigateUrl);
    return;
  }
  window.ReactNativeWebView.postMessage(
    JSON.stringify({
      screen: navigateUrl.screen,
      params: navigateUrl.params,
      case_id: caseIdFromResponse,
      patient_name: patientName,
    })
  );
};

function getLastScreen(navigateUrl) {
  let current = navigateUrl;
  let lastScreen = null;

  while (current) {
    if (current.screen) {
      lastScreen = current.screen;
    }
    current = current.params;
  }

  return lastScreen;
}

function formatSectionName(sectionId) {
  if (!sectionId) return "";
  return sectionId
    .replace(/([A-Z])/g, " $1")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ")
    .trim();
}

function appendDefaultMessageCentered() {
  // Ensure we only append if no default message element exists
  if (defaultMessageEl) return;

  // Get the current hour and determine a dynamic greeting accordingly
  const currentHour = new Date().getHours();
  let greeting = "Good Evening";

  if (currentHour >= 5 && currentHour < 12) {
    greeting = "Good Morning";
  } else if (currentHour >= 12 && currentHour < 18) {
    greeting = "Good Afternoon";
  }

  // Create the default message container
  defaultMessageEl = document.createElement("div");
  defaultMessageEl.classList.add(
    "default-message",
    "flex",
    "flex-col",
    "items-center",
    "justify-center",
    "animate-fadeIn"
  );

  // Apply styles
  Object.assign(defaultMessageEl.style, {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "80%",
    maxWidth: "600px",
    padding: "20px",
    backgroundColor: "transparent",
    border: "1px solid rgba(255, 255, 255, 0.2)",
    borderRadius: "12px",
    pointerEvents: "none",
    opacity: "0", // Start hidden for fade-in effect
    transition: "opacity 0.5s ease-in-out",
  });

  // Create inner text container
  const textContainer = document.createElement("div");
  textContainer.classList.add("text-center");

  // Create greeting element
  const greetingEl = document.createElement("p");
  greetingEl.classList.add(
    "text-3xl",
    "font-bold",
    "mb-2",
    "bg-gradient-to-r",
    "from-blue-400",
    "to-purple-500",
    "bg-clip-text",
    "text-transparent"
  );
  greetingEl.textContent = `${greeting}!`;

  // Create subtext element
  const subtextEl = document.createElement("p");
  subtextEl.classList.add("text-lg", "text-gray-600");
  subtextEl.textContent = "How can we assist you today?";

  // Append elements
  textContainer.appendChild(greetingEl);
  textContainer.appendChild(subtextEl);
  defaultMessageEl.appendChild(textContainer);

  // Ensure the parent container is positioned relatively
  chatContainer.parentElement.style.position = "relative";
  chatContainer.parentElement.appendChild(defaultMessageEl);

  // Apply fade-in effect
  setTimeout(() => {
    defaultMessageEl.style.opacity = "1";
  }, 80);
}

function removeDefaultMessage() {
  if (defaultMessageEl) {
    defaultMessageEl.remove();
    defaultMessageEl = null;
  }
}

async function loadChatHistory() {
  const apiUrl = `${config.scheduler_apiUrl}/chat/${userId}`;

  try {
    // Fetch the chat history
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${access_token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status}`);
    }

    const responseData = await response.json();
    console.log("Chat history:", responseData);
    chatContainer.innerHTML = "";

    // If no chat messages, show the default centered message.
    if (!responseData.msg || responseData.msg.length === 0) {
      appendDefaultMessageCentered();
    } else {
      responseData.msg.forEach((message) => {
        const { type, response } = message;
        const messageWrapper = document.createElement("div");
        messageWrapper.classList.add("flex", "my-2", "flex-col");

        // Handle human message
        if (type === "human") {
          // Container alignment for human messages
          messageWrapper.classList.add("items-end", "justify-end");

          const userMessage = document.createElement("div");
          userMessage.classList.add(
            "p-3",
            "rounded-l-lg",
            "rounded-br-lg",
            "bg-gray-50",
            "text-black",
            "max-w-[80%]",
            "shadow-md",
            "overflow-hidden",
            "break-words"
          );
          userMessage.textContent = response.text.query;
          messageWrapper.appendChild(userMessage);

          // Append timestamp for human message (if available)
          if (response.text.time_stamp) {
            const timeStampEl = document.createElement("div");
            timeStampEl.classList.add(
              "pr-1",
              "text-xs",
              "text-gray-500",
              "mt-1",
              "self-end"
            );
            timeStampEl.textContent = formatTimestamp(response.text.time_stamp);
            // timeStampEl.textContent = "01.00 PM, 2023-02-20";
            messageWrapper.appendChild(timeStampEl);
          }
        }
        // Handle AI message
        else if (type === "ai") {
          // Container alignment for AI messages
          messageWrapper.classList.add("items-start");

          const botMessage = document.createElement("div");
          const baseClasses = [
            "py-3",
            "pl-3",
            "pr-2",
            "rounded-r-lg",
            "rounded-bl-lg",
            "bg-white",
            "text-black",
            "max-w-[80%]",
            "shadow-md",
            "break-words",
            "relative", // to position the patient name correctly
          ];

          // Clone the baseClasses array for modification.
          const messageClasses = response.patient_name
            ? ["pt-8", ...baseClasses]
            : baseClasses;

          // Apply classes to botMessage element
          botMessage.classList.add(...messageClasses);

          // Display patient_name if it exists in the response
          if (response.patient_name) {
            const patientNameEl = document.createElement("div");
            patientNameEl.classList.add(
              "absolute",
              "top-0",
              "left-0",
              "text-xs",
              "font-medium",
              "text-gray-700",
              "bg-blue-50",
              "px-2",
              "py-1",
              "rounded-br-md"
            );
            patientNameEl.textContent = response.patient_name;
            // patientNameEl.textContent = "Patient Name";
            botMessage.appendChild(patientNameEl);
          }

          // Append AI response text
          if (response.text) {
            const textElement = document.createElement("div");
            textElement.textContent = response.text;
            botMessage.appendChild(textElement);
          }

          // Append native navigation link if exists
          if (response.navigate_url) {
            const lastScreen = getLastScreen(response.navigate_url);

            const navigateLink = document.createElement("p");
            // Set dataset.type to native explicitly.
            navigateLink.dataset.type = "native";
            navigateLink.classList.add(
              "text-blue-500",
              "underline",
              "history-navigate-link"
            );
            navigateLink.dataset.url = JSON.stringify(response.navigate_url);
            navigateLink.dataset.caseId = response.case_id;
            navigateLink.dataset.patientName = response.patient_name;
            const span = document.createElement("span");
            span.classList.add("url-text");
            span.textContent = lastScreen;
            navigateLink.appendChild(span);

            botMessage.appendChild(navigateLink);
          }

          // Append web navigation link if exists
          if (response.web_navigate_url) {
            const webNavUrl = constructWebNavUrl(
              response.web_navigate_url,
              response.case_id
            );
            if (webNavUrl) {
              const sectionId =
                response.web_navigate_url.params &&
                response.web_navigate_url.params.section_id;
              const formattedSection = sectionId
                ? formatSectionName(sectionId)
                : "Navigate";

              const webNavLink = document.createElement("p");
              webNavLink.dataset.type = "web";
              webNavLink.classList.add(
                "text-blue-500",
                "underline",
                "history-navigate-link"
              );
              webNavLink.dataset.url = webNavUrl;

              const span = document.createElement("span");
              span.classList.add("url-text");
              span.textContent = formattedSection;
              webNavLink.appendChild(span);

              botMessage.appendChild(webNavLink);
            }
          }

          // Append context links if available
          if (response.context && Array.isArray(response.context)) {
            const contextContainer = document.createElement("div");
            contextContainer.classList.add("mt-2", "space-y-1");

            response.context.forEach((url) => {
              const contextLinkWrapper = document.createElement("div");
              contextLinkWrapper.classList.add(
                "truncate",
                "max-w-[100%]",
                "flex",
                "items-end",
                "gap-2",
                "justify-center"
              );

              const bulletPoint = document.createElement("p");
              bulletPoint.classList.add("font-bold", "text-2xl");
              bulletPoint.textContent = ".";

              const contextLink = document.createElement("a");
              contextLink.href = url;
              contextLink.target = "_blank";
              contextLink.classList.add(
                "text-blue-500",
                "underline",
                "overflow-hidden",
                "text-ellipsis",
                "block"
              );
              contextLink.textContent = url;

              contextLinkWrapper.appendChild(bulletPoint);
              contextLinkWrapper.appendChild(contextLink);
              contextContainer.appendChild(contextLinkWrapper);
            });

            botMessage.appendChild(contextContainer);
          }

          messageWrapper.appendChild(botMessage);

          // Append timestamp for AI message (if available)
          if (response.time_stamp) {
            const timeStampEl = document.createElement("div");
            timeStampEl.classList.add(
              "pl-1",
              "text-xs",
              "text-gray-500",
              "mt-1",
              "self-start"
            );
            timeStampEl.textContent = formatTimestamp(response.time_stamp);
            // timeStampEl.textContent = "12:00 PM, 2023-02-20";
            messageWrapper.appendChild(timeStampEl);
          }
        }

        chatContainer.appendChild(messageWrapper);
      });

      // Attach event listeners to all navigation links after rendering messages.
      setTimeout(() => {
        document.querySelectorAll(".history-navigate-link").forEach((link) => {
          link.addEventListener("click", (event) => {
            event.preventDefault();
            const linkType = link.dataset.type;
            let urlData = link.dataset.url;
            if (linkType === "web") {
              if (urlData.indexOf("chatbot/") === 0) {
                urlData = urlData.replace("chatbot/", "");
              }
              if (!urlData.startsWith("/")) {
                urlData = "/" + urlData;
              }
              window.parent.location.href = urlData;
            } else if (linkType === "native") {
              try {
                const navData = JSON.parse(urlData);
                const caseId = link.dataset.caseId;
                const patientName = link.dataset.patientName;
                handleNavigateClick(navData, caseId, patientName);
              } catch (error) {
                console.error("Error parsing native navigation data:", error);
              }
            } else {
              try {
                const navData = JSON.parse(urlData);
                const caseId = link.dataset.caseId;
                handleNavigateClick(navData, caseId);
              } catch (error) {
                console.error(
                  "Error parsing fallback native navigation data:",
                  error
                );
              }
            }
          });
        });
      }, 0);
    }

    // Scroll to the bottom after rendering.
    chatContainer.scrollTop = chatContainer.scrollHeight;
  } catch (error) {
    console.error("Error fetching chat history:", error.message);
  }
}

if (chatInput) {
  sendButton.addEventListener("click", () => {
    removeDefaultMessage();
  });
  chatInput.addEventListener("keydown", (event) => {
    if (event.key === "Enter") {
      removeDefaultMessage();
    }
  });
}

function constructWebNavUrl(webNav, caseId) {
  if (!webNav || !webNav.url) return null;

  // Construct URL with query parameters
  const params = new URLSearchParams({
    case_id: caseId,
    ...webNav.params,
  }).toString();

  return `${webNav.url}?${params}`;
}
async function sendMessage() {
  const inputText = chatInput.value.trim() || previousInput;
  if (!inputText || isWaitingForResponse) return;

  previousInput = inputText;
  isWaitingForResponse = true;
  sendButton.disabled = true;
  sendButton.style.backgroundColor = "#888888"; // Disabled state

  // Create user message container
  const userMessage = document.createElement("div");
  userMessage.classList.add(
    "flex",
    "flex-col",
    "items-end",
    "justify-end",
    "my-2"
  );

  // Create user message bubble
  const userMessageContent = document.createElement("div");
  userMessageContent.classList.add(
    "p-3",
    "rounded-l-lg",
    "rounded-br-lg",
    "bg-gray-50",
    "text-black",
    "max-w-[80%]",
    "shadow-md",
    "overflow-hidden"
  );
  userMessageContent.textContent = inputText;
  userMessage.appendChild(userMessageContent);

  // Add UTC timestamp for human message
  const humanTimestampEl = document.createElement("div");
  humanTimestampEl.classList.add(
    "pr-1",
    "text-xs",
    "text-gray-500",
    "mt-1",
    "self-end"
  );
  humanTimestampEl.textContent = formatTimestamp(new Date());
  userMessage.appendChild(humanTimestampEl);

  chatContainer.appendChild(userMessage);

  // Reset input box and character count
  chatInput.value = "";
  chatInput.style.height = "auto";
  charLimit.textContent = `0/2000`;

  // Create skeleton loader while waiting for bot response
  const skeletonLoader = document.createElement("div");
  skeletonLoader.classList.add("flex", "items-start", "my-2");

  const skeletonContent = document.createElement("div");
  skeletonContent.classList.add(
    "p-3",
    "rounded-r-lg",
    "rounded-bl-lg",
    "bg-white",
    "text-black",
    "w-64",
    "shadow-md",
    "space-y-2"
  );

  // Create animated loading divs
  const line1 = document.createElement("div");
  line1.classList.add(
    "w-full",
    "h-4",
    "bg-gray-200",
    "animate-pulse",
    "rounded-md"
  );

  const line2 = document.createElement("div");
  line2.classList.add(
    "w-3/4",
    "h-4",
    "bg-gray-200",
    "animate-pulse",
    "rounded-md"
  );

  skeletonContent.appendChild(line1);
  skeletonContent.appendChild(line2);
  skeletonLoader.appendChild(skeletonContent);
  chatContainer.appendChild(skeletonLoader);

  // Scroll to the bottom
  scrollToBottom();

  // Prepare API URL
  const apiUrl = `${
    config.scheduler_apiUrl
  }/query/${userId}?query=${encodeURIComponent(
    inputText
  )}&case_id=${caseId}&screen_context=${screenContext}&platform=${platform}`;

  try {
    // Fetch API response
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${access_token}`,
      },
    });

    if (!response.ok) throw new Error(`HTTP Error: ${response.status}`);

    const responseData = await response.json();
    // Remove skeleton loader
    chatContainer.removeChild(skeletonLoader);

    // Create bot message container; add 'relative' to accommodate absolute positioning
    const botMessage = document.createElement("div");
    botMessage.classList.add(
      "relative",
      "flex",
      "flex-col",
      "items-start",
      "my-2"
    );

    // Create bot message bubble
    const botMessageContent = document.createElement("div");
    const baseClasses = [
      "py-3",
      "pl-3",
      "pr-2",
      "rounded-r-lg",
      "rounded-bl-lg",
      "bg-white",
      "text-black",
      "max-w-[80%]",
      "shadow-md",
    ];
    if (responseData.patient_name) {
      baseClasses.unshift("pt-8");
    }
    botMessageContent.classList.add(...baseClasses);

    // Add bot response text if available
    if (responseData.text) {
      const textElement = document.createElement("div");
      textElement.classList.add("overflow-hidden", "break-words");
      textElement.textContent = responseData.text;
      botMessageContent.appendChild(textElement);
    }

    // Add image response if available
    if (responseData.image_url) {
      const imageElement = document.createElement("img");
      imageElement.src = responseData.image_url;
      imageElement.alt = "Response Image";
      imageElement.classList.add(
        "my-2",
        "rounded-md",
        "max-w-full",
        "cursor-pointer"
      );
      botMessageContent.appendChild(imageElement);
    }

    // Add navigation link if available (native)
    if (responseData.navigate_url) {
      const lastScreen = getLastScreen(responseData.navigate_url);
      const navLink = document.createElement("p");
      navLink.classList.add("text-blue-500", "underline", "navigate-link");
      navLink.dataset.type = "native";
      navLink.dataset.url = JSON.stringify(responseData.navigate_url);
      navLink.dataset.caseId = responseData.case_id;
      navLink.dataset.patientName = responseData.patient_name;
      navLink.textContent = lastScreen;
      botMessageContent.appendChild(navLink);
    } else if (responseData.web_navigate_url) {
      // For web navigation, construct a URL string and mark as web.
      const webNavUrl = constructWebNavUrl(
        responseData.web_navigate_url,
        responseData.case_id
      );
      if (webNavUrl) {
        const webNavLink = document.createElement("p");
        webNavLink.classList.add("text-blue-500", "underline", "navigate-link");
        webNavLink.dataset.type = "web";
        webNavLink.dataset.url = webNavUrl;
        // Attach the patient_name from the response
        webNavLink.dataset.patientName = responseData.patient_name || "";
        const sectionId =
          responseData.web_navigate_url.params &&
          responseData.web_navigate_url.params.section_id;
        webNavLink.textContent = sectionId
          ? formatSectionName(sectionId)
          : "Navigate";
        botMessageContent.appendChild(webNavLink);
      }
    }

    // Handle context URLs if available
    if (responseData.context && Array.isArray(responseData.context)) {
      responseData.context.forEach((url) => {
        const contextContainer = document.createElement("div");
        contextContainer.classList.add(
          "truncate",
          "max-w-[100%]",
          "flex",
          "items-end",
          "gap-2",
          "justify-center"
        );
        const bulletPoint = document.createElement("p");
        bulletPoint.classList.add("font-bold", "text-2xl");
        bulletPoint.textContent = ".";
        const contextLink = document.createElement("a");
        contextLink.href = url;
        contextLink.target = "_blank";
        contextLink.classList.add(
          "text-blue-500",
          "underline",
          "overflow-hidden",
          "text-ellipsis",
          "block"
        );
        contextLink.textContent = url;
        contextContainer.appendChild(bulletPoint);
        contextContainer.appendChild(contextLink);
        botMessageContent.appendChild(contextContainer);
      });
    }

    // Append the bot message bubble to the bot message container
    botMessage.appendChild(botMessageContent);

    // --- Updated Code: Display patient name using absolute positioning as in loadchathistory ---
    if (responseData.patient_name) {
      const patientNameEl = document.createElement("div");
      patientNameEl.classList.add(
        "absolute",
        "top-0",
        "left-0",
        "text-xs",
        "font-medium",
        "text-gray-700",
        "bg-blue-50",
        "px-2",
        "py-1",
        "rounded-br-md"
      );
      patientNameEl.textContent = responseData.patient_name;
      botMessage.appendChild(patientNameEl);
    }
    // Display bot's timestamp below the message card if available
    if (responseData.time_stamp) {
      const timeStampEl = document.createElement("div");
      timeStampEl.classList.add(
        "pl-1",
        "text-xs",
        "text-gray-500",
        "mt-1",
        "self-start"
      );
      timeStampEl.textContent = formatTimestamp(responseData.time_stamp);
      botMessage.appendChild(timeStampEl);
    }

    chatContainer.appendChild(botMessage);

    setTimeout(() => {
      document.querySelectorAll(".navigate-link").forEach((link) => {
        link.addEventListener("click", (event) => {
          event.preventDefault();
          const type = link.dataset.type;
          if (type === "web") {
            let targetUrl = link.dataset.url || "";
            if (targetUrl.indexOf("chatbot/") === 0) {
              targetUrl = targetUrl.replace("chatbot/", "");
            }
            if (!targetUrl.startsWith("/")) {
              targetUrl = "/" + targetUrl;
            }
            window.parent.location.href = targetUrl;
          } else if (type === "native") {
            try {
              const navData = JSON.parse(link.dataset.url);
              const caseId = link.dataset.caseId;
              const patientName = link.dataset.patientName;
              handleNavigateClick(navData, caseId, patientName);
            } catch (error) {
              console.error("Error parsing navigate_url:", error);
            }
          }
        });
      });
    }, 0);
  } catch (error) {
    console.error("Error fetching bot response:", error.message);

    // Remove skeleton loader
    chatContainer.removeChild(skeletonLoader);

    // Create error message container
    const errorMessage = document.createElement("div");
    errorMessage.classList.add("flex", "items-start", "my-2");

    // Create error message bubble
    const errorMessageContent = document.createElement("div");
    errorMessageContent.classList.add(
      "p-3",
      "rounded-r-lg",
      "rounded-bl-lg",
      "bg-gray-50",
      "text-black",
      "max-w-[80%]",
      "shadow-md"
    );

    // Set error message text
    errorMessageContent.textContent =
      error.message === "Failed to fetch"
        ? "Network error. Please check your connection and try again."
        : "Something went wrong. Please try again later.";

    // Add retry link
    const retryLink = document.createElement("p");
    retryLink.classList.add("text-blue-500", "underline", "cursor-pointer");
    retryLink.textContent = "Retry";
    retryLink.addEventListener("click", sendMessage);

    errorMessageContent.appendChild(retryLink);
    errorMessage.appendChild(errorMessageContent);
    chatContainer.appendChild(errorMessage);
  }

  // Re-enable the send button
  isWaitingForResponse = false;
  sendButton.disabled = false;
  sendButton.style.backgroundColor = "#8143d9"; // Enabled color

  // Scroll to the bottom
  scrollToBottom();
}

function openChatbot() {
  const chatbotModal = document.getElementById("chatbot-modal");
  const chatbotIcon = document.getElementById("chatbot-icon");

  chatbotModal.classList.remove("hidden");
  setTimeout(() => {
    chatbotModal.style.bottom = "100px";
  }, 0);
  chatbotIcon.classList.remove("fa-robot");
  chatbotIcon.classList.add("fa-times");
}

function closeChatbotOnIcon() {
  const chatbotModal = document.getElementById("chatbot-modal");
  const chatbotIcon = document.getElementById("chatbot-icon");

  chatbotModal.style.bottom = "-100%";
  setTimeout(() => {
    chatbotModal.classList.add("hidden");
  }, 500);
  chatbotIcon.classList.remove("fa-times");
  chatbotIcon.classList.add("fa-robot");
}

function toggleChatbot() {
  const chatbotModal = document.getElementById("chatbot-modal");

  if (chatbotModal.classList.contains("hidden")) {
    openChatbot();
  } else {
    closeChatbotOnIcon();
  }
}

if (clearBtn) {
  clearBtn.addEventListener("click", () => {
    clearModal.classList.remove("hidden"); // Show modal
  });
}

if (cancelClearBtn) {
  cancelClearBtn.addEventListener("click", () => {
    clearModal.classList.add("hidden"); // Hide modal when canceled
  });
}

if (confirmBtn) {
  confirmBtn.addEventListener("click", async () => {
    try {
      const apiUrlDelete = `${config.scheduler_apiUrl}/chat/${userId}`;
      const deleteResponse = await fetch(apiUrlDelete, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${access_token}`,
        },
      });
      if (!deleteResponse.ok) {
        throw new Error(`HTTP Error: ${deleteResponse.status}`);
      }

      const chatContainer = document.getElementById("chat-container");
      if (chatContainer) {
        chatContainer.innerHTML = "";
      }

      clearModal.classList.add("hidden");
      loadChatHistory();
    } catch (error) {
      console.error("Error deleting chat history:", error.message);
    }
  });
}

function triggerChatHistoryLoad() {
  if (!window.chatHistoryLoaded && typeof loadChatHistory === "function") {
    loadChatHistory();
    window.chatHistoryLoaded = true;
  }
}

window.addEventListener("load", () => {
  const loader = document.getElementById("loader");
  if (loader) loader.style.display = "none";
  triggerChatHistoryLoad();
  scrollToBottom();
});
