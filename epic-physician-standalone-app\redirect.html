<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login</title>
    <link rel="stylesheet" href="css/login.css" />
    <link rel="icon" href="images/scheduler_logo.png" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Flatpickr CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="js/jquery.min.js"></script>

    <script src="js/main.js"></script>
    <script src="./config.js"></script>
    <script src="js/api.js"></script>

    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>
  </head>

  <body class="bg-[#F1F1FB]">
    <div>Processing.....</div>
    <script>

      getAccessToken();
      // Generalized function for API requests
      async function apiRequest(
        endpoint,
        method = "GET",
        body = null,
        requiresAuth = true,
        apiUrls = true
      ) {
        let url;
        if (apiUrls) {
          url = new URL(config.apiUrl + endpoint);
        }

        let headers = {
          "Content-Type": "application/json",
        };

        // Include the access token if required
        if (requiresAuth) {
          const access_token = localStorage.getItem("access_token");
          headers["Authorization"] = `Bearer ${access_token}`;
        }

        try {
          console.log("url", url);
          const response = await fetch(url, {
            method,
            headers,
            body: body ? JSON.stringify(body) : null,
          });
          let text = await response.json();
          console.log("response", response.text());
          if (!response.ok) {
            const errorData = await response.json();
            const errorMessage =
              errorData.detail?.error_description ||
              errorData.errorMessage ||
              "Request failed";
            throw new Error(errorMessage);
          }

          return await response.json();
        } catch (error) {
          if (error.message.includes("token")) {
            localStorage.clear();
            window.location.href = "/login.html";
          } else {
            console.error("API error:", error);
            throw error;
          }
        }
      }
      async function getAccessToken() {
        // loginLoader(true);

        const queryParams = new URLSearchParams(window.location.search);
        const code = queryParams.get("code");
        if (code) {
          let access_token_global;
          // const { result } = await apiRequest(`/code?redirect_url=${redirect_url}&code=${code}`, "GET", null, false);
          fetch(`${config.apiUrl}/code?redirect_url=${config.epic_redirect_url}&code=${code}`)
            // Converting received data to JSON
            .then((response) => response.json())
            .then((json) => {
              console.log("json", json.result);
              result = json.result;
              const { refresh_token, access_token } = result;
              localStorage.setItem("refresh_token", refresh_token);
              localStorage.setItem("access_token", access_token);
              const decodedToken = jwt_decode(access_token);
              if (
                decodedToken.realm_roles &&
                decodedToken.realm_roles.includes("CLINICAL_COORDINATOR")
              ) {
                access_token_global = access_token;
                patient_data = localStorage.getItem("patientData");
                console.log("access_token---->1", access_token_global);
                console.log("patient_data", patient_data);
                patient_data = JSON.parse(patient_data);
                for (const name of patient_data["name"]) {
                  if (name.use == "official") {
                    patientName = `${name.given.join(" ")} ${name.family}`;
                    first_name = `${name.given.join(" ")}`;
                    last_name = name.family;
                  }
                }
                gender = patient_data.gender == "female" ? "F" : "M";
                payload = {
                  first_name: first_name,
                  last_name: last_name,
                  dob: patient_data.birthDate,
                  sex: gender,
                  mrn: "1234",
                  procedure_type_id: "6756d856a403e4d2cde949e0",
                };
                console.log("payload", payload);

                fetch(`${config.apiUrl}/users/profile`, {
                  method: "GET",
                  headers: {
                    "Content-type": "application/json; charset=UTF-8",
                    Authorization: `Bearer ${access_token}`,
                  },
                })
                  .then((response) => response.json())
                  .then((json) => {
                    const confirmation = confirm(
                      `You belong to ${json.result.org.name}.  Are you confirmed to create a patient?`
                    );
                    if (!confirmation) {
                      window.location.href = "launch.html";
                    } else {
                      site_id = json.result.org.id;
                      // site_id = '6756d297a403e4d2cde949da'
                      console.log("refresh_token", refresh_token);
                      console.log(
                        "patientData",
                        localStorage.getItem("patientData")
                      );
                      fetch(`${config.apiUrl}/sites/${site_id}/patients`, {
                        method: "POST",
                        body: JSON.stringify(payload),
                        headers: {
                          "Content-type": "application/json; charset=UTF-8",
                          Authorization: `Bearer ${access_token}`,
                        },
                      })
                        .then((response) => response.json())
                        .then((json) => {
                          console.log(json);
                          alert("Patient created Successfully");
                          window.location.href = "launch.html"; // Redirect to launch.html
                          localStorage.clear()
                        });
                    }
                  });
              } else {
                alert(
                  "You are not logged in as CLINICAL COORDINATOR. Please contact admin"
                );
                window.location.href = "launch.html"; // Redirect to launch.html
              }
            });

          // loginLoader(false);

          // window.location.pathname = "/callback.html";
        }
      }
    </script>
  </body>
</html>
