import {
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

/**
 * Updates the visual highlight of the follow-up diagnostic studies container.
 */
function updateContainerHighlight(container, data) {
  // Optionally: implement logic for "allFilled"
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

/**
 * Renders nested conditional input fields for multi_input_field type.
 * @param {string} parentKey - The top-level field key.
 * @param {string} conditionalKey - The chosen conditional key (e.g., if_yes, if_no).
 * @param {string} subKey - Field key inside the conditional group.
 * @param {object} subField - The input field object inside the group.
 * @param {object} parentField - The parent input field (provides patientData context).
 * @param {object} parentDataObj - The data object (usually patientData.follow_up_physical_exam_and_labs).
 * @param {object} modifiedByDisplay - Reference for updating modified_by.
 * @param {HTMLElement} container - The section container.
 * @param {HTMLElement} subFieldContainer - Where to append this UI.
 */
function renderNestedConditionalSubField(
  parentKey,
  conditionalKey,
  subKey,
  subField,
  parentField,
  parentDataObj,
  modifiedByDisplay,
  container,
  subFieldContainer
) {
  // Label
  const subLabel = document.createElement("label");
  subLabel.classList.add("label", "cursor-pointer");
  subLabel.textContent = `${subField.label} ${
    subField.field_id ? `(${subField.field_id})` : ""
  }`;
  if (subField.description)
    subLabel.setAttribute("title", subField.description);
  subFieldContainer.appendChild(subLabel);

  // input_type switch (recursively allow multi_input_field)
  if (subField.input_type === "radio") {
    const subRadioContainer = document.createElement("div");
    subRadioContainer.classList.add("radio-container");
    subRadioContainer.style.display = "flex";
    subRadioContainer.style.gap = "15px";
    (subField.options || []).forEach((option) => {
      const radioWrapper = document.createElement("div");
      const input = document.createElement("input");
      input.type = "radio";
      input.name = `${parentKey}-${subKey}`;
      input.value = option;
      input.id = `${parentKey}-${subKey}-${option}`;
      input.checked = subField.value === option;
      input.addEventListener("change", (e) => {
        if (e.target.checked) {
          parentField[conditionalKey][subKey].value = e.target.value;
          parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, parentDataObj);
        }
      });
      const optionLabel = document.createElement("label");
      optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option}`);
      optionLabel.innerText = option;
      if (subField.description)
        optionLabel.setAttribute("title", subField.description);
      radioWrapper.appendChild(input);
      radioWrapper.appendChild(optionLabel);
      subRadioContainer.appendChild(radioWrapper);
    });
    subFieldContainer.appendChild(subRadioContainer);
  } else if (subField.input_type === "select") {
    const select = document.createElement("select");
    select.name = `${parentKey}-${subKey}`;
    if (!subField.value) {
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.innerText = "Select an option";
      defaultOption.disabled = true;
      defaultOption.selected = true;
      select.appendChild(defaultOption);
    }
    (subField.options || []).forEach((option) => {
      const optionElement = document.createElement("option");
      optionElement.value = option.id;
      optionElement.innerText = option.value;
      if (subField.value && subField.value === option.value)
        optionElement.selected = true;
      select.appendChild(optionElement);
    });
    select.addEventListener("change", (e) => {
      const selOption = subField.options.find(
        (option) => option.id === e.target.value
      );
      const newValue = selOption ? selOption.value : "";
      parentField[conditionalKey][subKey].value = newValue;
      parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlight(container, parentDataObj);
    });
    subFieldContainer.appendChild(select);
  } else if (subField.input_type === "multi_select") {
    // Copy-paste multi-select, but for subfields
    if (!Array.isArray(subField.value))
      subField.value = subField.value ? [subField.value] : [];
    const dropdownContainer = document.createElement("div");
    dropdownContainer.classList.add("dropdown-container");
    dropdownContainer.style.position = "relative";
    dropdownContainer.style.width = "100%";
    subFieldContainer.style.overflow = "visible";
    const dropdownHeader = document.createElement("div");
    dropdownHeader.classList.add("dropdown-header");
    dropdownHeader.style.padding = "8px 12px";
    dropdownHeader.style.border = "1px solid #ccc";
    dropdownHeader.style.borderRadius = "4px";
    dropdownHeader.style.cursor = "pointer";
    dropdownHeader.style.display = "flex";
    dropdownHeader.style.justifyContent = "space-between";
    dropdownHeader.style.alignItems = "center";
    dropdownHeader.style.backgroundColor = "#fff";
    const selectedText = document.createElement("span");
    selectedText.classList.add("selected-text");
    const updateSelectedText = () => {
      if (subField.value.length === 0)
        selectedText.textContent = "Select options...";
      else if (subField.value.length === 1) {
        const selectedOption = subField.options.find(
          (opt) => opt.value === subField.value[0]
        );
        selectedText.textContent = selectedOption
          ? selectedOption.value
          : subField.value[0];
      } else
        selectedText.textContent = `${subField.value.length} options selected`;
    };
    updateSelectedText();
    const dropdownArrow = document.createElement("span");
    dropdownArrow.innerHTML = "&#9662;";
    dropdownHeader.appendChild(selectedText);
    dropdownHeader.appendChild(dropdownArrow);

    const dropdownContent = document.createElement("div");
    dropdownContent.classList.add("dropdown-content");
    dropdownContent.style.display = "none";
    dropdownContent.style.position = "fixed";
    dropdownContent.style.width = "350px";
    dropdownContent.style.maxHeight = "200px";
    dropdownContent.style.overflowY = "auto";
    dropdownContent.style.backgroundColor = "#fff";
    dropdownContent.style.border = "1px solid #ccc";
    dropdownContent.style.borderRadius = "4px";
    dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
    dropdownContent.style.zIndex = "9999";
    (subField.options || []).forEach((option) => {
      const checkboxWrapper = document.createElement("div");
      checkboxWrapper.classList.add("checkbox-wrapper");
      checkboxWrapper.style.display = "flex";
      checkboxWrapper.style.alignItems = "center";
      checkboxWrapper.style.padding = "8px 12px";
      const input = document.createElement("input");
      input.type = "checkbox";
      input.name = `${parentKey}-${subKey}-${option.id}`;
      input.value = option.id;
      input.id = `${parentKey}-${subKey}-${option.id}`;
      input.style.marginRight = "4px";
      input.checked = subField.value.includes(option.value);
      input.addEventListener("change", (e) => {
        let currentValues = Array.isArray(subField.value)
          ? [...subField.value]
          : [];
        if (e.target.checked) {
          if (!currentValues.includes(option.value))
            currentValues.push(option.value);
        } else {
          currentValues = currentValues.filter((val) => val !== option.value);
        }
        parentField[conditionalKey][subKey].value = currentValues;
        parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
        updateSelectedText();
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, parentDataObj);
      });
      const optionLabel = document.createElement("label");
      optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option.id}`);
      optionLabel.innerText = option.field_id
        ? `${option.value} (${option.field_id})`
        : option.value;
      const inputLabelWrapper = document.createElement("div");
      inputLabelWrapper.style.display = "flex";
      inputLabelWrapper.style.alignItems = "center";
      inputLabelWrapper.appendChild(input);
      inputLabelWrapper.appendChild(optionLabel);
      checkboxWrapper.appendChild(inputLabelWrapper);
      dropdownContent.appendChild(checkboxWrapper);
    });
    const positionDropdown = () => {
      const headerRect = dropdownHeader.getBoundingClientRect();
      dropdownContent.style.top = `${headerRect.bottom}px`;
      dropdownContent.style.left = `${headerRect.left}px`;
    };
    document.addEventListener("click", (e) => {
      if (
        !dropdownContainer.contains(e.target) &&
        !dropdownContent.contains(e.target)
      ) {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
      }
    });
    const closeDropdown = () => {
      dropdownContent.style.display = "none";
      dropdownArrow.innerHTML = "&#9662;";
      window.removeEventListener("scroll", closeDropdown);
    };
    dropdownContainer.cleanupFunc = () => {
      if (document.body.contains(dropdownContent))
        document.body.removeChild(dropdownContent);
      window.removeEventListener("scroll", closeDropdown);
    };
    dropdownContainer.appendChild(dropdownHeader);
    const showDropdown = (e) => {
      if (!document.body.contains(dropdownContent))
        document.body.appendChild(dropdownContent);
      dropdownContent.style.display = "block";
      positionDropdown();
    };
    dropdownHeader.onclick = (e) => {
      e.stopPropagation();
      const isOpen = dropdownContent.style.display === "block";
      if (!isOpen) {
        showDropdown();
        dropdownArrow.innerHTML = "&#9652;";
        window.addEventListener("scroll", closeDropdown);
      } else {
        closeDropdown();
      }
    };
    subFieldContainer.appendChild(dropdownContainer);
  } else if (subField.input_type === "multi_input_field") {
    // handle further nesting!
    const nestedMultiInputContainer = document.createElement("div");
    nestedMultiInputContainer.classList.add("multi-input-container");
    nestedMultiInputContainer.style.marginTop = "10px";
    // Radio options for the nested field
    const nestedRadioContainer = document.createElement("div");
    nestedRadioContainer.classList.add("radio-container");
    nestedRadioContainer.style.display = "flex";
    nestedRadioContainer.style.gap = "15px";
    const nestedConditionalContainer = document.createElement("div");
    nestedConditionalContainer.classList.add("conditional-content-container");
    nestedConditionalContainer.style.marginTop = "10px";
    nestedConditionalContainer.style.padding = "10px";
    nestedConditionalContainer.style.border = "1px solid #eee";
    nestedConditionalContainer.style.borderRadius = "4px";
    nestedConditionalContainer.style.display = "none";
    nestedConditionalContainer.style.width = "100%";
    if (subField.options && Array.isArray(subField.options)) {
      subField.options.forEach((option) => {
        const optionWrapper = document.createElement("div");
        optionWrapper.classList.add("option-wrapper");
        optionWrapper.style.display = "inline-flex";
        optionWrapper.style.marginRight = "20px";
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";
        const input = document.createElement("input");
        input.type = "radio";
        input.name = `${parentKey}-${subKey}`;
        input.value = option;
        input.id = `${parentKey}-${subKey}-${option}`;
        input.checked = subField.value === option;
        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            parentField[conditionalKey][subKey].value = e.target.value;
            parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(container, parentDataObj);
            nestedConditionalContainer.innerHTML = "";
            nestedConditionalContainer.style.display = "none";
            let nestedKey = null;
            if (e.target.value === "Yes" && subField.if_yes)
              nestedKey = "if_yes";
            else if (e.target.value === "No" && subField.if_no)
              nestedKey = "if_no";
            else if (e.target.value === "Alive" && subField.if_alive)
              nestedKey = "if_alive";
            else if (e.target.value === "Deceased" && subField.if_deceased)
              nestedKey = "if_deceased";
            if (nestedKey && subField[nestedKey]) {
              nestedConditionalContainer.style.display = "block";
              Object.entries(subField[nestedKey]).forEach(
                ([deepSubKey, deepSubField]) => {
                  const deepSubFieldContainer = document.createElement("div");
                  deepSubFieldContainer.classList.add("sub-field-container");
                  deepSubFieldContainer.style.marginBottom = "15px";
                  renderNestedConditionalSubField(
                    parentKey,
                    nestedKey,
                    deepSubKey,
                    deepSubField,
                    subField,
                    parentDataObj,
                    modifiedByDisplay,
                    container,
                    deepSubFieldContainer
                  );
                  nestedConditionalContainer.appendChild(deepSubFieldContainer);
                }
              );
            }
          }
        });
        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${parentKey}-${subKey}-${option}`);
        optionLabel.innerText = option;
        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        optionWrapper.appendChild(radioWrapper);
        nestedRadioContainer.appendChild(optionWrapper);
        if (subField.value === option) {
          // render immediately
          let nestedKey = null;
          if (option === "Yes" && subField.if_yes) nestedKey = "if_yes";
          else if (option === "No" && subField.if_no) nestedKey = "if_no";
          else if (option === "Alive" && subField.if_alive)
            nestedKey = "if_alive";
          else if (option === "Deceased" && subField.if_deceased)
            nestedKey = "if_deceased";
          if (nestedKey && subField[nestedKey]) {
            nestedConditionalContainer.style.display = "block";
            Object.entries(subField[nestedKey]).forEach(
              ([deepSubKey, deepSubField]) => {
                const deepSubFieldContainer = document.createElement("div");
                deepSubFieldContainer.classList.add("sub-field-container");
                deepSubFieldContainer.style.marginBottom = "15px";
                renderNestedConditionalSubField(
                  parentKey,
                  nestedKey,
                  deepSubKey,
                  deepSubField,
                  subField,
                  parentDataObj,
                  modifiedByDisplay,
                  container,
                  deepSubFieldContainer
                );
                nestedConditionalContainer.appendChild(deepSubFieldContainer);
              }
            );
          }
        }
      });
    }
    nestedMultiInputContainer.appendChild(nestedRadioContainer);
    nestedMultiInputContainer.appendChild(nestedConditionalContainer);
    subFieldContainer.appendChild(nestedMultiInputContainer);
  } else if (
    subField.input_type === "string" ||
    subField.input_type === "text"
  ) {
    const input = document.createElement("input");
    input.type = "text";
    input.name = `${parentKey}-${subKey}`;
    input.placeholder = `Enter ${subField.label}`;
    input.value = subField.value || "";
    let previousValue = input.value;
    input.addEventListener("input", (e) => {
      const currentValue = e.target.value;
      let isValid = false;
      validateStringInput(currentValue, subField.field_id, (validatedValue) => {
        isValid = true;
        previousValue = validatedValue;
        parentField[conditionalKey][subKey].value = validatedValue;
        parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, parentDataObj);
        if (validatedValue !== currentValue) {
          e.target.value = validatedValue;
        }
      });
      if (!isValid) e.target.value = previousValue;
    });
    subFieldContainer.appendChild(input);
  } else if (subField.input_type === "date") {
    const dateWrapper = document.createElement("div");
    dateWrapper.style.position = "relative";
    const displayInput = document.createElement("input");
    displayInput.type = "text";
    displayInput.name = `${parentKey}-${subKey}-display`;
    displayInput.readOnly = true;
    displayInput.value = formatDisplayDate(subField.value);
    displayInput.placeholder = "MM/DD/YYYY";
    displayInput.style.cursor = "pointer";
    const dateInput = document.createElement("input");
    dateInput.type = "date";
    dateInput.name = `${parentKey}-${subKey}`;
    dateInput.value = subField.value || "";
    dateInput.style.position = "absolute";
    dateInput.style.opacity = "0";
    dateInput.style.cursor = "pointer";
    const today = new Date().toISOString().split("T")[0];
    dateInput.max = today;
    dateInput.addEventListener("change", (e) => {
      const selectedDate = e.target.value;
      displayInput.value = formatDisplayDate(selectedDate);
      parentField[conditionalKey][subKey].value = selectedDate;
      parentField[conditionalKey][subKey].modified_by = "ABSTRACTOR";
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlight(container, parentDataObj);
    });
    displayInput.addEventListener("click", () => {
      dateInput.showPicker();
    });
    dateWrapper.appendChild(displayInput);
    dateWrapper.appendChild(dateInput);
    subFieldContainer.appendChild(dateWrapper);
  }
}

/**
 * Main entry point for rendering the FollowUpDiagnosticStudies section.
 * Replicates historyInterventions style and handles all input types,
 * adding full conditional/nesting/multiselect support, but retains dependency handling.
 */
export function renderFollowUpPhysicalExamAndLabs(patientData) {
  const container = document.getElementById("followUpPhysicalExamAndLabs");
  container.innerHTML = "";
  container.style.position = "relative";

  // 1. Detect field dependencies for N/A-style checkboxes (keep original code)
  const skipFields = new Set();
  Object.entries(patientData.follow_up_physical_exam_and_labs).forEach(
    ([fieldKey, value]) => {
      if (value.dependency) {
        const dependentFieldKey = Object.keys(
          patientData.follow_up_physical_exam_and_labs
        ).find(
          (k) =>
            patientData.follow_up_physical_exam_and_labs[k].field_id ===
            value.dependency
        );
        if (dependentFieldKey) {
          skipFields.add(dependentFieldKey);
          skipFields.add(fieldKey);
        }
      }
    }
  );

  // 2. Render regular fields (including all advanced types from historyInterventions, except dependencies handled below)
  Object.entries(patientData.follow_up_physical_exam_and_labs).forEach(
    ([key, value]) => {
      if (key === "verified" || skipFields.has(key)) return;

      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";
      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");

      // Label
      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.textContent = value.metric
        ? `${value.label} (${value.metric}) (${value.field_id})`
        : `${value.label} (${value.field_id})`;
      if (value.description) label.setAttribute("title", value.description);
      fieldContainer.appendChild(label);

      // Modified_by display
      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.marginTop = "-10px";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";
      if (!value.modified_by) value.modified_by = "";
      modifiedByDisplay.textContent = value.modified_by;

      // Input switch: string/text, date, radio, select, multi_select, multi_input_field
      if (value.input_type === "string" || value.input_type === "text") {
        const input = document.createElement("input");
        input.type = "text";
        input.name = key;
        input.placeholder = `Enter ${value.label}`;
        input.value = value.value || "";
        updateTileStyle(fieldContainer, input.value);
        let previousValue = input.value;
        input.addEventListener("input", (e) => {
          const currentValue = e.target.value;
          let isValid = false;
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;
              patientData.follow_up_physical_exam_and_labs[key].value =
                validatedValue;
              patientData.follow_up_physical_exam_and_labs[key].modified_by =
                "ABSTRACTOR";
              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.follow_up_physical_exam_and_labs
              );
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );
          if (!isValid) e.target.value = previousValue;
        });
        fieldContainer.appendChild(input);
      } else if (value.input_type === "date") {
        const dateWrapper = document.createElement("div");
        dateWrapper.style.position = "relative";
        const displayInput = document.createElement("input");
        displayInput.type = "text";
        displayInput.readOnly = true;
        displayInput.value = formatDisplayDate(value.value);
        displayInput.placeholder = "MM/DD/YYYY";
        displayInput.style.cursor = "pointer";
        const dateInput = document.createElement("input");
        dateInput.type = "date";
        dateInput.value = value.value || "";
        dateInput.style.position = "absolute";
        dateInput.style.opacity = "0";
        dateInput.style.cursor = "pointer";
        const today = new Date().toISOString().split("T")[0];
        dateInput.max = today;
        updateTileStyle(fieldContainer, dateInput.value);
        dateInput.addEventListener("change", (e) => {
          const selectedDate = e.target.value;
          displayInput.value = formatDisplayDate(selectedDate);
          patientData.follow_up_physical_exam_and_labs[key].value =
            selectedDate;
          patientData.follow_up_physical_exam_and_labs[key].modified_by =
            "ABSTRACTOR";
          updateTileStyle(fieldContainer, selectedDate);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_physical_exam_and_labs
          );
        });
        displayInput.addEventListener("click", () => {
          dateInput.showPicker();
        });
        dateWrapper.appendChild(displayInput);
        dateWrapper.appendChild(dateInput);
        fieldContainer.appendChild(dateWrapper);
      } else if (value.input_type === "radio") {
        const radioContainer = document.createElement("div");
        radioContainer.classList.add("radio-container");
        (value.options || []).forEach((option) => {
          const radioWrapper = document.createElement("div");
          const input = document.createElement("input");
          input.type = "radio";
          input.name = key;
          input.value = option;
          input.id = `${key}-${option}`;
          input.checked = value.value === option;
          input.addEventListener("change", (e) => {
            if (e.target.checked) {
              patientData.follow_up_physical_exam_and_labs[key].value =
                e.target.value;
              patientData.follow_up_physical_exam_and_labs[key].modified_by =
                "ABSTRACTOR";
              updateTileStyle(fieldContainer, e.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.follow_up_physical_exam_and_labs
              );
            }
          });
          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${key}-${option}`);
          optionLabel.innerText = option;
          radioWrapper.appendChild(input);
          radioWrapper.appendChild(optionLabel);
          radioContainer.appendChild(radioWrapper);
        });
        updateTileStyle(fieldContainer, value.value || "");
        fieldContainer.appendChild(radioContainer);
      } else if (value.input_type === "select") {
        const select = document.createElement("select");
        select.name = key;
        if (!value.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          select.appendChild(defaultOption);
        }
        (value.options || []).forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          if (value.value && value.value === option.value)
            optionElement.selected = true;
          select.appendChild(optionElement);
        });
        const selectedOption = (value.options || []).find(
          (option) => option.value === value.value
        );
        updateTileStyle(
          fieldContainer,
          selectedOption ? selectedOption.value : ""
        );
        select.addEventListener("change", (e) => {
          const selOption = value.options.find(
            (option) => option.id === e.target.value
          );
          const newValue = selOption ? selOption.value : "";
          patientData.follow_up_physical_exam_and_labs[key].value = newValue;
          patientData.follow_up_physical_exam_and_labs[key].modified_by =
            "ABSTRACTOR";
          updateTileStyle(fieldContainer, newValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_physical_exam_and_labs
          );
        });
        fieldContainer.appendChild(select);
      } else if (value.input_type === "multi_select") {
        if (!Array.isArray(value.value))
          value.value = value.value ? [value.value] : [];
        const dropdownContainer = document.createElement("div");
        dropdownContainer.classList.add("dropdown-container");
        dropdownContainer.style.position = "relative";
        dropdownContainer.style.width = "100%";
        fieldContainer.style.overflow = "visible";
        container.style.overflow = "visible";
        const dropdownHeader = document.createElement("div");
        dropdownHeader.classList.add("dropdown-header");
        dropdownHeader.style.padding = "8px 12px";
        dropdownHeader.style.border = "1px solid #ccc";
        dropdownHeader.style.borderRadius = "4px";
        dropdownHeader.style.cursor = "pointer";
        dropdownHeader.style.display = "flex";
        dropdownHeader.style.justifyContent = "space-between";
        dropdownHeader.style.alignItems = "center";
        dropdownHeader.style.backgroundColor = "#fff";
        const selectedText = document.createElement("span");
        selectedText.classList.add("selected-text");
        const updateSelectedText = () => {
          if (value.value.length === 0)
            selectedText.textContent = "Select options...";
          else if (value.value.length === 1) {
            const selectedOption = value.options.find(
              (opt) => opt.value === value.value[0]
            );
            selectedText.textContent = selectedOption
              ? selectedOption.value
              : value.value[0];
          } else
            selectedText.textContent = `${value.value.length} options selected`;
        };
        updateSelectedText();
        const dropdownArrow = document.createElement("span");
        dropdownArrow.innerHTML = "&#9662;";
        dropdownHeader.appendChild(selectedText);
        dropdownHeader.appendChild(dropdownArrow);
        const dropdownContent = document.createElement("div");
        dropdownContent.classList.add("dropdown-content");
        dropdownContent.style.display = "none";
        dropdownContent.style.position = "fixed";
        dropdownContent.style.width = "350px";
        dropdownContent.style.maxHeight = "200px";
        dropdownContent.style.overflowY = "auto";
        dropdownContent.style.backgroundColor = "#fff";
        dropdownContent.style.border = "1px solid #ccc";
        dropdownContent.style.borderRadius = "4px";
        dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
        dropdownContent.style.zIndex = "9999";
        (value.options || []).forEach((option) => {
          const checkboxWrapper = document.createElement("div");
          checkboxWrapper.classList.add("checkbox-wrapper");
          checkboxWrapper.style.display = "flex";
          checkboxWrapper.style.alignItems = "center";
          checkboxWrapper.style.padding = "8px 12px";
          const input = document.createElement("input");
          input.type = "checkbox";
          input.name = `${key}-${option.id}`;
          input.value = option.id;
          input.id = `${key}-${option.id}`;
          input.style.marginRight = "4px";
          input.checked = value.value.includes(option.value);
          input.addEventListener("change", (e) => {
            let currentValues = Array.isArray(value.value)
              ? [...value.value]
              : [];
            if (e.target.checked) {
              if (!currentValues.includes(option.value))
                currentValues.push(option.value);
            } else {
              currentValues = currentValues.filter(
                (val) => val !== option.value
              );
            }
            patientData.follow_up_physical_exam_and_labs[key].value =
              currentValues;
            patientData.follow_up_physical_exam_and_labs[key].modified_by =
              "ABSTRACTOR";
            updateSelectedText();
            updateTileStyle(
              fieldContainer,
              currentValues.length > 0 ? "filled" : ""
            );
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.follow_up_physical_exam_and_labs
            );
          });
          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${key}-${option.id}`);
          optionLabel.innerText = option.field_id
            ? `${option.value} (${option.field_id})`
            : option.value;
          const inputLabelWrapper = document.createElement("div");
          inputLabelWrapper.style.display = "flex";
          inputLabelWrapper.style.alignItems = "center";
          inputLabelWrapper.appendChild(input);
          inputLabelWrapper.appendChild(optionLabel);
          checkboxWrapper.appendChild(inputLabelWrapper);
          dropdownContent.appendChild(checkboxWrapper);
        });
        const positionDropdown = () => {
          const headerRect = dropdownHeader.getBoundingClientRect();
          dropdownContent.style.top = `${headerRect.bottom}px`;
          dropdownContent.style.left = `${headerRect.left}px`;
        };
        document.addEventListener("click", (e) => {
          if (
            !dropdownContainer.contains(e.target) &&
            !dropdownContent.contains(e.target)
          ) {
            dropdownContent.style.display = "none";
            dropdownArrow.innerHTML = "&#9662;";
          }
        });
        const closeDropdown = () => {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
          window.removeEventListener("scroll", closeDropdown);
        };
        dropdownContainer.cleanupFunc = () => {
          if (document.body.contains(dropdownContent))
            document.body.removeChild(dropdownContent);
          window.removeEventListener("scroll", closeDropdown);
        };
        dropdownContainer.appendChild(dropdownHeader);
        const showDropdown = () => {
          if (!document.body.contains(dropdownContent))
            document.body.appendChild(dropdownContent);
          dropdownContent.style.display = "block";
          positionDropdown();
        };
        dropdownHeader.onclick = (e) => {
          e.stopPropagation();
          const isOpen = dropdownContent.style.display === "block";
          if (!isOpen) {
            showDropdown();
            dropdownArrow.innerHTML = "&#9652;";
            window.addEventListener("scroll", closeDropdown);
          } else {
            closeDropdown();
          }
        };
        updateTileStyle(fieldContainer, value.value.length > 0 ? "filled" : "");
        fieldContainer.appendChild(dropdownContainer);
      } else if (value.input_type === "multi_input_field") {
        // (Replicate advanced conditional multi input structure, with nested support)
        const multiInputContainer = document.createElement("div");
        multiInputContainer.classList.add("multi-input-container");
        const radioContainer = document.createElement("div");
        radioContainer.classList.add("radio-container");
        radioContainer.style.display = "flex";
        radioContainer.style.gap = "15px";
        const conditionalContentContainer = document.createElement("div");
        conditionalContentContainer.classList.add(
          "conditional-content-container"
        );
        conditionalContentContainer.style.marginTop = "10px";
        conditionalContentContainer.style.padding = "10px";
        conditionalContentContainer.style.border = "1px solid #eee";
        conditionalContentContainer.style.borderRadius = "4px";
        conditionalContentContainer.style.display = "none";
        conditionalContentContainer.style.width = "100%";
        (value.options || []).forEach((option) => {
          const radioWrapper = document.createElement("div");
          radioWrapper.style.display = "inline-flex";
          radioWrapper.style.marginRight = "20px";
          const input = document.createElement("input");
          input.type = "radio";
          input.name = key;
          input.value = option;
          input.id = `${key}-${option}`;
          input.checked = value.value === option;
          input.addEventListener("change", (e) => {
            if (e.target.checked) {
              patientData.follow_up_physical_exam_and_labs[key].value =
                e.target.value;
              patientData.follow_up_physical_exam_and_labs[key].modified_by =
                "ABSTRACTOR";
              updateTileStyle(fieldContainer, e.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.follow_up_physical_exam_and_labs
              );
              conditionalContentContainer.innerHTML = "";
              conditionalContentContainer.style.display = "none";
              let conditionalKey = null;
              if (e.target.value === "Yes" && value.if_yes)
                conditionalKey = "if_yes";
              else if (e.target.value === "No" && value.if_no)
                conditionalKey = "if_no";
              else if (e.target.value === "Alive" && value.if_alive)
                conditionalKey = "if_alive";
              else if (e.target.value === "Deceased" && value.if_deceased)
                conditionalKey = "if_deceased";
              if (conditionalKey && value[conditionalKey]) {
                conditionalContentContainer.style.display = "block";
                Object.entries(value[conditionalKey]).forEach(
                  ([subKey, subField]) => {
                    const subFieldContainer = document.createElement("div");
                    subFieldContainer.classList.add("sub-field-container");
                    subFieldContainer.style.marginBottom = "15px";
                    renderNestedConditionalSubField(
                      key,
                      conditionalKey,
                      subKey,
                      subField,
                      value,
                      patientData.follow_up_physical_exam_and_labs,
                      modifiedByDisplay,
                      container,
                      subFieldContainer
                    );
                    conditionalContentContainer.appendChild(subFieldContainer);
                  }
                );
              }
            }
          });
          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${key}-${option}`);
          optionLabel.innerText = option;
          radioWrapper.appendChild(input);
          radioWrapper.appendChild(optionLabel);
          radioContainer.appendChild(radioWrapper);
          if (value.value === option) {
            // render immediately
            let conditionalKey = null;
            if (option === "Yes" && value.if_yes) conditionalKey = "if_yes";
            else if (option === "No" && value.if_no) conditionalKey = "if_no";
            else if (option === "Alive" && value.if_alive)
              conditionalKey = "if_alive";
            else if (option === "Deceased" && value.if_deceased)
              conditionalKey = "if_deceased";
            if (conditionalKey && value[conditionalKey]) {
              conditionalContentContainer.style.display = "block";
              Object.entries(value[conditionalKey]).forEach(
                ([subKey, subField]) => {
                  const subFieldContainer = document.createElement("div");
                  subFieldContainer.classList.add("sub-field-container");
                  subFieldContainer.style.marginBottom = "15px";
                  renderNestedConditionalSubField(
                    key,
                    conditionalKey,
                    subKey,
                    subField,
                    value,
                    patientData.follow_up_physical_exam_and_labs,
                    modifiedByDisplay,
                    container,
                    subFieldContainer
                  );
                  conditionalContentContainer.appendChild(subFieldContainer);
                }
              );
            }
          }
        });
        updateTileStyle(fieldContainer, value.value || "");
        multiInputContainer.appendChild(radioContainer);
        multiInputContainer.appendChild(conditionalContentContainer);
        fieldContainer.appendChild(multiInputContainer);
      }

      // Add to tile
      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);
      container.appendChild(tileWrapper);
    }
  );

  // 3. Render dependency fields as combined input+checkbox tiles (retain original logic)
  Object.entries(patientData.follow_up_physical_exam_and_labs).forEach(
    ([fieldKey, value]) => {
      if (!value.dependency || !skipFields.has(fieldKey)) return;
      const dependentFieldKey = Object.keys(
        patientData.follow_up_physical_exam_and_labs
      ).find(
        (k) =>
          patientData.follow_up_physical_exam_and_labs[k].field_id ===
          value.dependency
      );
      if (!dependentFieldKey) return;
      const dependentField =
        patientData.follow_up_physical_exam_and_labs[dependentFieldKey];
      if (dependentField.input_type !== "checkbox") return;
      // Create the same composite UI for this:
      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";
      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");
      // Label
      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.textContent = value.metric
        ? `${value.label} (${value.metric}) (${value.field_id})`
        : `${value.label} (${value.field_id})`;
      if (value.description) label.setAttribute("title", value.description);
      fieldContainer.appendChild(label);
      // Modified by
      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.marginTop = "-10px";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";
      if (!value.modified_by) value.modified_by = "";
      modifiedByDisplay.textContent = value.modified_by;
      // Input/Checkbox composite
      const inputWrapper = document.createElement("div");
      inputWrapper.style.display = "flex";
      inputWrapper.style.alignItems = "center";
      inputWrapper.style.gap = "10px";

      // Create the appropriate input based on input_type
      let inputElement;

      if (value.input_type === "select") {
        // Create a select element for select input type
        inputElement = document.createElement("select");
        inputElement.name = fieldKey;
        inputElement.style.flexGrow = "1";

        if (!value.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          inputElement.appendChild(defaultOption);
        }

        (value.options || []).forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          if (value.value && value.value === option.value)
            optionElement.selected = true;
          inputElement.appendChild(optionElement);
        });

        inputElement.addEventListener("change", (e) => {
          const selOption = value.options.find(
            (option) => option.id === e.target.value
          );
          const newValue = selOption ? selOption.value : "";
          patientData.follow_up_physical_exam_and_labs[fieldKey].value =
            newValue;
          patientData.follow_up_physical_exam_and_labs[fieldKey].modified_by =
            "ABSTRACTOR";

          if (newValue) {
            checkbox.checked = false;
            patientData.follow_up_physical_exam_and_labs[
              dependentFieldKey
            ].value = "";
          }

          updateTileStyle(fieldContainer, newValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_physical_exam_and_labs
          );
        });
      } else {
        // Default to text input for other types
        inputElement = document.createElement("input");
        inputElement.type = "text";
        inputElement.name = fieldKey;
        inputElement.placeholder = `Enter ${value.label}`;
        inputElement.value = value.value || "";
        inputElement.style.flexGrow = "1";

        let previousValue = inputElement.value;
        inputElement.addEventListener("input", (e) => {
          const currentValue = e.target.value;
          let isValid = false;
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;
              patientData.follow_up_physical_exam_and_labs[fieldKey].value =
                validatedValue;
              patientData.follow_up_physical_exam_and_labs[
                fieldKey
              ].modified_by = "ABSTRACTOR";
              if (validatedValue) {
                checkbox.checked = false;
                patientData.follow_up_physical_exam_and_labs[
                  dependentFieldKey
                ].value = "";
              }
              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.follow_up_physical_exam_and_labs
              );
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );
          if (!isValid) e.target.value = previousValue;
        });
      }

      const checkboxWrapper = document.createElement("div");
      checkboxWrapper.style.display = "flex";
      checkboxWrapper.style.alignItems = "center";
      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.name = dependentFieldKey;
      checkbox.id = `${dependentFieldKey}-checkbox`;
      checkbox.checked = dependentField.value === "True";
      const checkboxLabel = document.createElement("label");
      checkboxLabel.setAttribute("for", `${dependentFieldKey}-checkbox`);
      checkboxLabel.innerText =
        `${dependentField.label} (${dependentField.field_id})` || "N/A";
      checkboxLabel.style.marginLeft = "5px";
      checkboxWrapper.appendChild(checkbox);
      checkboxWrapper.appendChild(checkboxLabel);

      if (dependentField.value === "True") {
        inputElement.disabled = true;
        if (value.input_type === "select") {
          // For select, reset to default option
          inputElement.value = "";
        } else {
          // For text input
          inputElement.value = "";
        }
        patientData.follow_up_physical_exam_and_labs[fieldKey].value = "";
      }

      updateTileStyle(
        fieldContainer,
        value.value || (dependentField.value === "True" ? "True" : "")
      );

      checkbox.addEventListener("change", (e) => {
        const isChecked = e.target.checked;
        patientData.follow_up_physical_exam_and_labs[dependentFieldKey].value =
          isChecked ? "True" : "";
        patientData.follow_up_physical_exam_and_labs[
          dependentFieldKey
        ].modified_by = "ABSTRACTOR";
        if (isChecked) {
          inputElement.disabled = true;
          if (value.input_type === "select") {
            // For select, reset to default option
            inputElement.value = "";
          } else {
            // For text input
            inputElement.value = "";
          }
          patientData.follow_up_physical_exam_and_labs[fieldKey].value = "";
        } else {
          inputElement.disabled = false;
        }
        updateTileStyle(fieldContainer, isChecked ? "True" : "");
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(
          container,
          patientData.follow_up_physical_exam_and_labs
        );
      });

      inputWrapper.appendChild(inputElement);
      inputWrapper.appendChild(checkboxWrapper);
      fieldContainer.appendChild(inputWrapper);
      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);
      container.appendChild(tileWrapper);
    }
  );

  // 4. Add verified checkbox (container-level)
  if (
    typeof patientData.follow_up_physical_exam_and_labs.verified !== "object" ||
    patientData.follow_up_physical_exam_and_labs.verified === null
  ) {
    patientData.follow_up_physical_exam_and_labs.verified = {
      value: patientData.follow_up_physical_exam_and_labs.verified || "False",
      modified_by: "",
    };
  }
  const verifiedData = patientData.follow_up_physical_exam_and_labs.verified;
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";
  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "physicalExamAndLabs-verified-checkbox";
  containerVerifiedCheckbox.checked = verifiedData.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";
  containerVerifiedCheckbox.addEventListener("change", (e) => {
    verifiedData.value = e.target.checked ? "True" : "False";
    updateContainerHighlight(
      container,
      patientData.follow_up_physical_exam_and_labs
    );
  });
  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "physicalExamAndLabs-verified-checkbox"
  );
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";
  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);
  // Initial highlight
  updateContainerHighlight(
    container,
    patientData.follow_up_physical_exam_and_labs
  );
}
