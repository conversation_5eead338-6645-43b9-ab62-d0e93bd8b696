export function formatHeading(heading, type) {
  if (type === "chadscore") {
    const parts = heading.split("_");

    // Extract and format the first two parts for the score.
    let scoreParts = parts.splice(0, 2);

    // Uppercase the first part.
    scoreParts[0] = scoreParts[0].toUpperCase();

    // Format "vasc" properly.
    if (scoreParts[1].toLowerCase() === "vasc") {
      scoreParts[1] = "VASc";
    } else {
      scoreParts[1] =
        scoreParts[1].charAt(0).toUpperCase() +
        scoreParts[1].slice(1).toLowerCase();
    }

    // Join with a dash
    let scoreStr = scoreParts.join("-");

    // Replace all occurrences of '2' with Unicode subscript '₂'
    scoreStr = scoreStr.replace(
      /2/g,
      `<span style="font-size:1.6em;">₂</span>`
    );

    // Format the remaining parts (capitalize words)
    const remainder = parts
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

    return remainder ? `${scoreStr} ${remainder}` : scoreStr;
  } else {
    return heading
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  }
}

export const formatSubhead = (text) => {
  return text.replace(/2/g, `<span style="font-size:1.6em;">₂</span>`);
};
export function formatDisplayDate(dateStr) {
  if (!dateStr) return ""; // Return empty string for null, undefined, or empty string

  const date = new Date(dateStr);
  // Check if date is invalid
  if (isNaN(date.getTime())) return "";

  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const year = date.getFullYear();
  return `${month}/${day}/${year}`;
}

export function formatDisplayDateTime(dateTimeStr) {
  if (!dateTimeStr) return ""; // Return empty string for null, undefined, or empty string

  const date = new Date(dateTimeStr);
  // Check if date is invalid
  if (isNaN(date.getTime())) return "";

  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const year = date.getFullYear();

  // Format time in 12-hour format with AM/PM
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours ? hours : 12; // Convert 0 to 12 for 12 AM

  return `${month}/${day}/${year} ${hours}:${minutes} ${ampm}`;
}

export const pairFieldsWithNA = (demographics) => {
  const pairedFields = {};

  Object.entries(demographics).forEach(([key, value]) => {
    if (key.toLowerCase().endsWith("_na")) {
      const baseKey = key.slice(0, -3); // Remove "_na" suffix
      pairedFields[baseKey] = { field: baseKey, naField: key };
    } else if (!pairedFields[key]) {
      pairedFields[key] = { field: key, naField: null };
    }
  });

  return pairedFields;
};

export const updateTileStyle = (fieldContainer, value) => {
  if (value && value.toString().trim() !== "") {
    fieldContainer.style.border = "2px solid #8143d9";
  } else {
    // Highlight empty fields with yellow border
    fieldContainer.style.border = "2px solid #facc15";
  }
};

export const validateStringInput = (value, fieldId, callback) => {
  // Early return if required parameters are missing
  if (!fieldId || !callback) return;

  // If value is undefined or null, treat it as an empty string
  if (value === undefined || value === null) {
    value = "";
  }

  // const charOnlyFields = [
  //   "2000",
  //   "2010",
  //   "2020",
  //   "3025",
  //   "4435",
  //   "14734",
  //   "14839",
  //   "14841",
  //   "12153",
  //   "14967",
  //   "14948",
  //   "14276",
  //   "14312",
  //   "14917",
  // ];

  // Field groups for different validation types
  const charOnlyFields = [
    "2000",
    "2010",
    "2020",
    "2030",
    "2045",
    "14781",
    "2999",
    "12846",
    "3025",
    "3030",
    "14861",
    "14860",
    "14862",
    "15433",
    "15434",
    "15435",
    "15431",
    "14843",
    "10999",
    "1010",
    "1020",
    "1050",
    "1060",
    "1070",
  ];
  // const integerFields = [
  //   "2040",
  //   "2045",
  //   "3030",
  //   "2065",
  //   "12846",
  //   "5115",
  //   "6000",
  //   "6005",
  //   "6010",
  //   "6015/6020",
  //   "6045",
  //   "14861, 14860, 14862/14863",
  //   "14830",
  //   "14848",
  //   "14843",
  //   "14871",
  //   "14869",
  //   "14868",
  //   "14993",
  //   "14994",
  //   "14920",
  //   "14921",
  //   "7210",
  //   "7215",
  //   "14278",
  // ];

  const integerFields = ["14840", "14842"];
  const alphaNumericFields = ["15433, 15434, 15435/15436/15431, 2065"];
  const floatOnlyFields = [
    "5115",
    "6000",
    "6005",
    "6010",
    "6015",
    "6020",
    "6030",
    "6040",
    "6045",
    "6050",
    "14210",
    "13213",
    "14830",
    "14848",
    "7210",
    "7215",
    "14278",
    "14920",
    "14921",
    "14868",
    "14871",
    "14869",
    "13690",
    "14884",
    "14886",
    "14888",
    "14993",
    "14994",
    "2040",
    "14780",
    "14863",
    "15436",
    "1000",
    "1040",
    "1071",
  ];

  // Apply specific validation based on field ID
  if (charOnlyFields.includes(fieldId)) {
    parseCharInput(value, callback);
  } else if (alphaNumericFields.includes(fieldId)) {
    parseAlphaNumericInput(value, callback);
  } else if (fieldId === "2030") {
    parseSSNInput(value, callback);
  } else if (integerFields.includes(fieldId)) {
    parseIntInput(value, callback);
  } else if (floatOnlyFields.includes(fieldId)) {
    parseFloatInput(value, callback);
  } else {
    // Default behavior for unspecified field IDs
    callback(value);
  }
};

export const parseCharInput = (value, callback) => {
  // Check if the user is trying to clear the field
  if (value === "") {
    callback("");
    return;
  }

  // Remove any numbers or special characters
  const filteredValue = value.replace(/[^a-zA-Z\s]/g, "");

  // Always call the callback, even if the filtered value is empty
  // This allows backspacing to work properly
  callback(filteredValue);
};

export const parseAlphaNumericInput = (value, callback) => {
  // Check if the user is trying to clear the field
  if (value === "") {
    callback("");
    return;
  }

  // Filter out any non-alphanumeric characters
  const filteredValue = value.replace(/[^a-zA-Z0-9]/g, "");

  // Always call the callback, even if the filtered value is empty
  // This allows backspacing to work properly
  callback(filteredValue);
};

export const parseFloatInput = (value, callback, min = 0, max, decimal) => {
  // Remove any non-numeric characters except for a single decimal point
  let filteredValue = value.replace(/[^0-9.]/g, "");

  // Ensure only one decimal point is present
  const decimalCount = (filteredValue.match(/\./g) || []).length;
  if (decimalCount > 1) {
    filteredValue = filteredValue.replace(/\.+$/, ""); // Remove extra decimals at the end
  }

  // Check if the input is empty or just backspacing
  if (filteredValue === "") {
    // If the user is actively clearing the field, allow empty string
    if (value === "") {
      callback("");
      return;
    }
    // Only default to min if the field is completely empty
    callback(min.toString());
    return;
  }

  if (filteredValue.endsWith(".")) {
    callback(filteredValue); // Allow trailing decimal point for user input
    return;
  }

  let parsedValue = parseFloat(filteredValue);

  // Enforce minimum value
  if (parsedValue < min) {
    parsedValue = min;
  }

  // Enforce maximum value
  if (max !== undefined && parsedValue > max) {
    parsedValue = max;
  }

  // Enforce decimal precision if provided
  if (decimal !== undefined) {
    parsedValue = parseFloat(parsedValue.toFixed(decimal));
  }

  callback(parsedValue.toString());
};

export const parseIntInput = (value, callback, min = 0, max) => {
  // Remove any non-numeric characters
  const filteredValue = value.replace(/[^0-9]/g, "");

  // Check if the input is empty or just backspacing
  if (filteredValue === "") {
    // If the user is actively clearing the field, allow empty string
    if (value === "") {
      callback("");
      return;
    }
    // Only default to min if the field is completely empty
    callback(min.toString());
    return;
  }

  // Parse the filtered value as an integer
  let parsedValue = parseInt(filteredValue, 10);

  // Enforce that the result is no less than min
  if (parsedValue < min) {
    parsedValue = min;
  }

  // If max is provided and parsedValue exceeds it, restrict to max
  if (max !== undefined && parsedValue > max) {
    parsedValue = max;
  }

  callback(parsedValue.toString());
};

export const parseSSNInput = (value, callback) => {
  // Check if the user is trying to clear the field
  if (value === "") {
    callback("");
    return;
  }

  const filteredValue = value.replace(/[^0-9]/g, "");
  if (filteredValue.length > 9) return;

  // If there's no input after filtering, return empty string
  if (filteredValue === "") {
    callback("");
    return;
  }

  const formattedSSN = filteredValue.replace(
    /^(\d{3})(\d{0,2})(\d{0,4})$/,
    (match, p1, p2, p3) => {
      if (p3) return `${p1}-${p2}-${p3}`;
      if (p2) return `${p1}-${p2}`;
      return p1;
    }
  );
  callback(formattedSSN);
};

export const parseEmailInput = (value, callback) => {
  // Basic email validation regex
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // Remove spaces from the input
  const trimmedValue = value.trim();

  // If the email is valid or empty, pass it through
  if (emailRegex.test(trimmedValue) || trimmedValue === "") {
    callback(trimmedValue);
  } else {
    // If the email is invalid but we want to allow partial input during typing,
    // we can still pass the value through without validation
    // This allows the user to type without interruption
    callback(trimmedValue);
  }
};
