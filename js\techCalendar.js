function formatDateStr(dateStr) {
  const dateObj = new Date(dateStr + "T00:00:00Z");
  const formattedDate = dateObj.getUTCDate().toString().padStart(2, "0");
  const formattedDay = dateObj
    .toLocaleDateString("en-US", { weekday: "short", timeZone: "UTC" })
    .toUpperCase();
  const formattedMonth = dateObj
    .toLocaleDateString("en-US", { month: "short", timeZone: "UTC" })
    .toUpperCase();
  return { formattedDate, formattedDay, formattedMonth };
}

function formatTime(timeStr) {
  const today = new Date().toISOString().split("T")[0]; // Get today's date in YYYY-MM-DD format
  const dateTimeStr = `${today}T${timeStr}`; // Combine today's date with the time string

  const options = {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  };
  const date = new Date(dateTimeStr);
  return date.toLocaleString("en-US", options);
}

function sortItemsByProvider(items) {
  return items.sort((a, b) =>
    a.implanting_physician.name.localeCompare(b.implanting_physician.name)
  );
}

function createPerDayColumn(date, items, isToday) {
  const result = finalpredictedDatacount.find(
    (item) => item.procedure_date === date
  );
  const difference = result.rep_count - result.site_physician_count;
  const isEqualAndPositive = difference < 0;
  const { formattedDate, formattedDay, formattedMonth } = formatDateStr(date);
  const sortedItems = sortItemsByProvider(items);

  // Check if there are cases for this date
  const hasCases = items.length > 0;
  console.log(`Date: ${date}, Has Cases: ${hasCases}, Items: ${items.length}`);

  const cardsHTML = sortedItems
    .map((item) => {
      return `
        <div class="w-full">
            <div class="rounded-lg p-2 bg-[#F1F1FB]">
                <div class="text-lg text-gray-600 truncate">${
                  item.site.name
                }</div>
                <div class="font-semibold text-md text-gray-700 mb-2 truncate">${
                  item.implanting_physician.name
                }</div>
                <div class="flex flex-row justify-between space-x-1">
                    <div class="flex items-center space-x-1">
                        <img src="/svg/cases.svg" alt="Cases Icon">
                        <div class="text-purple-600 text-sm truncate">Cases: ${
                          item.cases
                        }</div>
                    </div>
                    <div class="text-purple-600 text-sm truncate">${
                      item.rep.name
                    }</div>
                </div>
            </div>
            <div class="rounded-lg p-2 bg-[#F1F1FB] mt-1">
                <div class="flex flex-row justify-end">
                    <div class="bg-white text-gray-500 text-sm truncate">Start Time: ${formatTime(
                      item.start_time
                    )}</div>
                </div>
            </div>
            </div>
        `;
    })
    .join("");

  return `
        <div class="flex flex-col mx-2 items-start md:items-center">
            <div class="flex flex-col items-center my-2 px-2 select-none rounded-[12px] py-4 md:w-[7rem] relative">
                <!-- Date circle -->
                <div class="${isToday ? "bg-[#8F58DD]" : "bg-purple-100"} ${
    isToday ? "text-white" : "text-purple-600"
  } ${
    isEqualAndPositive ? "border border-red-600" : ""
  } rounded-full flex flex-col items-center justify-center w-12 h-12 cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 text-sm mt-3" onclick="loadTechAllocation(event, '${date}')">
                    <div class="font-bold pt-1">${formattedMonth}</div>
                    <div class="font-bold">${formattedDate}</div>
                </div>

                <!-- Purple dot above the date circle with absolute positioning -->
                ${
                  hasCases
                    ? `<div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-[#8F58DD] rounded-full shadow-lg z-20 border-2 border-white"></div>`
                    : ""
                }
                <div class="flex flex-row md:flex-col space-x-2 items-center mx-2">
                <div class="text-black text-md uppercase md:mt-1">${formattedDay}</div>
                </div>
            </div>
            <div class="space-y-4 w-full max-w-full">${cardsHTML}</div>
        </div>
    `;
}

function createDateHeader({
  isToday,
  isEqualAndPositive,
  formattedMonth,
  formattedDate,
  formattedDay,
  date,
  predictedData,
}) {
  // Main container
  const data = groupByProcedureDate(predictedData);
  const filterData = data.filter(
    (item) =>
      item.date.split("-").reverse().join("/") === date.toLocaleDateString()
  );
  const card = document.createElement("div");
  if (filterData && filterData.length > 0) {
    filterData[0].data.map((item) => {
      card.appendChild(weekCard(item));
    });
  }

  const mainDiv = document.createElement("div");
  mainDiv.className = "flex flex-col   items-start md:items-center";

  // Inner div for date section
  const dateContainer = document.createElement("div");
  dateContainer.className =
    "flex flex-row md:flex-col items-center my-2  select-none py-4 md:w-[7rem]";
  // Circle div (date)
  const circleDiv = document.createElement("div");
  circleDiv.className = `
      ${isToday ? "bg-[#8F58DD]" : "bg-purple-100"}
      ${isToday ? "text-white" : "text-purple-600"}
      ${isEqualAndPositive ? "border border-red-600" : ""}
      rounded-full flex flex-col text-sm items-center justify-center w-12 h-12 cursor-pointer
      transition-all duration-300 hover:shadow-2xl hover:scale-105
    `.trim();
  // circleDiv.setAttribute("onclick", `loadTechAllocation(event, '${date}')`);

  // Add month text
  const monthDiv = document.createElement("div");
  monthDiv.className = "text-xs md:text-sm font-bold pt-1";
  monthDiv.textContent = formattedMonth;
  circleDiv.appendChild(monthDiv);

  // Add date text
  const dateTextDiv = document.createElement("div");
  dateTextDiv.className = "text-xs md:text-sm font-bold";
  dateTextDiv.textContent = formattedDate;
  circleDiv.appendChild(dateTextDiv);

  // Append circleDiv to dateContainer
  dateContainer.appendChild(circleDiv);

  // Day container
  const dayContainer = document.createElement("div");
  dayContainer.className =
    "flex flex-row md:flex-col space-x-2 items-center mx-2";

  // Day text
  const dayText = document.createElement("div");
  dayText.className = "text-black text-md md:mt-1";
  dayText.textContent = formattedDay;
  dayContainer.appendChild(dayText);

  // Append dayContainer to dateContainer
  dateContainer.appendChild(dayContainer);

  // Cards container
  const cardsContainer = document.createElement("div");
  cardsContainer.className = "flex flex-row flex-wrap md:flex-col gap-6 w-64";
  cardsContainer.appendChild(card); // Inject cardsHTML directly

  // Append dateContainer and cardsContainer to mainDiv
  mainDiv.appendChild(dateContainer);
  mainDiv.appendChild(cardsContainer);

  return mainDiv;
}

const monthWeekCalendarChange = () => {
  loadCalendar();
};

// function getWeekdays(today) {
//   // Get the start of the week (Monday)
//   const startOfWeek = new Date(today);
//   const day = startOfWeek.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
//   const diffToMonday = day === 0 ? -6 : 1 - day; // Adjust for Sunday (0) being the end of the week
//   startOfWeek.setDate(new Date(today).getDate() + diffToMonday);

//   // Generate weekdays (Monday to Friday)
//   const weekdays = [];
//   for (let i = 0; i < 5; i++) {
//     const weekday = new Date(startOfWeek);
//     weekday.setDate(startOfWeek.getDate() + i);
//     weekdays.push(weekday);
//   }

//   return weekdays;
// }

function getWeekdays(from, to) {
  // Parse the from and to dates
  const startDate = new Date(from);
  const endDate = new Date(to);

  // Array to hold weekdays
  const weekdays = [];

  // Loop through dates between from and to
  for (
    let current = new Date(startDate);
    current <= endDate;
    current.setDate(current.getDate() + 1)
  ) {
    const day = current.getDay(); // Get the day of the week (0 = Sunday, 6 = Saturday)

    // Exclude weekends (Saturday and Sunday)
    if (day !== 0 && day !== 6) {
      weekdays.push(new Date(current)); // Add weekday to the array
    }
  }

  return weekdays;
}

// function getCorrectDate(from_date) {
//   // Parse the date as a local time zone date, treating the input date as UTC
//   const localDate = new Date(from_date + "T00:00:00"); // append a time to avoid default UTC conversion

//   // Get the time zone offset in minutes
//   const timezoneOffset = localDate.getTimezoneOffset();
//   const sign = timezoneOffset > 0 ? "-" : "+";
//   const offsetHours = String(
//     Math.floor(Math.abs(timezoneOffset) / 60)
//   ).padStart(2, "0");
//   const offsetMinutes = String(Math.abs(timezoneOffset) % 60).padStart(2, "0");
//   const timezone = `GMT${sign}${offsetHours}${offsetMinutes}`;

//   // Get the formatted date in the local time zone (based on the system time zone)
//   const options = {
//     timeZoneName: "short",
//     hour12: false,
//     weekday: "short", // Mon, Tue, etc.
//     year: "numeric",
//     month: "short", // Jan, Feb, etc.
//     day: "numeric",
//     hour: "2-digit",
//     minute: "2-digit",
//     second: "2-digit",
//   };
//   const formattedDate = localDate.toLocaleString("en-US", options);

//   // Remove the comma from the formatted date
//   const formattedDateWithoutComma = formattedDate.replace(/,/g, "");

//   // Extract the time zone abbreviation (e.g., "CST")
//   const timeZoneName = formattedDateWithoutComma.split(" ").pop();

//   // Construct the final formatted date string without the comma
//   const finalFormattedDate = `${formattedDateWithoutComma} ${timezone} (${timeZoneName})`;

//   return finalFormattedDate;
// }

function getCorrectDate(from_date) {
  const localDate = new Date(from_date + "T00:00:00");
  const timezoneOffset = localDate.getTimezoneOffset();
  const adjustedDate = new Date(
    localDate.getTime() - timezoneOffset * 60 * 1000
  );

  return adjustedDate;
}

async function loadCalendar() {
  setActiveNav("weekly schedule");
  setVariablesToDefault();
  setVisibilityForContents();
  document.getElementById("techCalendar").classList.remove("hidden");
  const val = document.getElementById("month_week").value;
  if (val === "Week") {
    document.getElementById("date-range-section").classList.add("md:block");
    document.getElementById("monthCalendar").classList.add("hidden");
    const techCalendar = document.getElementById("weeklycalendar");
    techCalendar.innerHTML = "";
    document.getElementById("repAllocation").classList.add("hidden");
    document.getElementById("repproviderList").classList.add("hidden");
    const loader = document.getElementById("loader");
    loader.classList.remove("hidden");
    let predictedData;
    try {
      from_date = localStorage.getItem("start-date");
      end_date = localStorage.getItem("end-date");

      if (
        !from_date ||
        from_date === "undefined" ||
        !end_date ||
        end_date === "undefined"
      ) {
        console.error("Invalid date range selected.");
        return;
      }
      result_data = await fetchschedulerdata(from_date, end_date);
      predictedData = result_data["cases_details"];
      predictedDatacount = result_data["count"];

      window.data = predictedData;
    } catch (error) {
      console.error("Error fetching predicted count data:", error);
      loader.classList.add("hidden");
      return;
    }

    const fromDate = new Date(from_date);
    const endDate = new Date(end_date);

    // Generate all dates in the given range
    const allDates = {};
    let daysCount = 0;
    for (let d = new Date(fromDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split("T")[0];
      allDates[dateStr] = [];
      daysCount++;
    }
    techCalendar.classList.forEach((className) => {
      if (className.startsWith("grid-cols-")) {
        techCalendar.classList.remove(className);
      }
    });
    techCalendar.classList.add(`grid-cols-${daysCount}`);

    // Merge predictedData with allDates
    const groupedFilteredPredictedData = predictedData.reduce((acc, item) => {
      (acc[item.procedure_date] = acc[item.procedure_date] || []).push(item);
      return acc;
    }, allDates);

    const predictedDataMap = predictedDatacount.reduce((acc, item) => {
      acc[item.procedure_date] = item;
      return acc;
    }, {});

    // let completeData = [];
    for (let d = new Date(fromDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split("T")[0];
      finalpredictedDatacount.push(
        predictedDataMap[dateStr] || {
          procedure_date: dateStr,
          site_physician_count: 0,
          rep_count: 0,
        }
      );
    }

    const today = new Date().toISOString().split("T")[0];
    techCalendar.innerHTML = Object.entries(groupedFilteredPredictedData)
      .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB))
      .map(([date, items]) => createPerDayColumn(date, items, date === today))
      .join("");

    const startDate = getCorrectDate(from_date);
    const toDate = getCorrectDate(end_date);
    const week = getWeekdays(startDate, toDate);
    // techCalendar.innerHTML = "";
    // week.map((date) => {
    //   const dateObj = new Date(date);
    //   techCalendar.appendChild(
    //     createDateHeader({
    //       isToday: true,
    //       isEqualAndPositive: false,
    //       formattedMonth: new Intl.DateTimeFormat("en-US", {
    //         month: "short",
    //       }).format(dateObj),
    //       formattedDate: dateObj.getDate(),
    //       formattedDay: new Intl.DateTimeFormat("en-US", {
    //         weekday: "short",
    //       }).format(dateObj),
    //       date: date,
    //       predictedData: predictedData,
    //     })
    //   );
    // });
    // if (predictedData.length === 0) {
    //   const div = document.createElement("div");
    //   div.className =
    //     "flex flex-row col-span-7  w-full items-center justify-center font-bold";
    //   div.textContent = "Please select the different date";
    //   techCalendar.appendChild(div);
    // }
    loader.classList.add("hidden");
    // techCalendar.classList.add("flex");
    techCalendar.classList.remove("hidden");
    document.getElementById("month_key").classList.add("hidden");
    document
      .getElementById("calendar_header")
      .classList.replace("justify-between", "justify-end");
    return;
  }
  document.getElementById("date-range-section").classList.remove("md:block");
  document.getElementById("repAllocation").classList.add("hidden");
  document.getElementById("repproviderList").classList.add("hidden");
  document.getElementById("weeklycalendar").classList.add("hidden");
  document.getElementById("monthCalendar").classList.remove("hidden");
  document.getElementById("month_key").classList.remove("hidden");
  document
    .getElementById("calendar_header")
    .classList.replace("justify-end", "justify-between");
  // techCalendarMonth
  return;
}
