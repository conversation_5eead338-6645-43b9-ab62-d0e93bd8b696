{"in_hospital_adjudication": {"verified": "", "demographics": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient.last_name", "value_type": "string", "q_prompt": "What is the patient's last name, including any hyphenated names, as recorded on arrival at this facility?", "description": "\n**Coding Instruction:**\nIndicate the patient's last name. Hyphenated names should be recorded with a hyphen.\n**Target Value:**\nThe value on arrival at this facility"}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient.first_name", "value_type": "string", "q_prompt": "What is the patient's first name as recorded upon arrival at this facility?", "description": "\n**Coding Instruction:**\nIndicate the patient's first name.\n**Target Value:**\nThe value on arrival at this facility"}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient._id", "value_type": "object", "q_prompt": "What is the unique Patient ID number created and automatically inserted by the software that identifies this patient at this facility?", "description": "\n**Coding Instruction:**\nIndicate the number created and automatically inserted by the software that uniquely identifies this patient.\n**Target Value:**\nThe value on arrival at this facility"}, "procedure_start_date": {"field_id": "7001", "label": "Procedure Start Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient._id", "value_type": "object"}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the optional patient identifier, such as the medical record number, that can be associated with the patient?", "description": "\n**Coding Instruction:**\nIndicate an optional patient identifier, such as medical record number, that can be associated with the patient.\n**Target Value:**\nN/A"}, "study_patient_id": {"field_id": "3030", "label": "Study Patient ID", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the research study patient identification number as assigned by the research protocol? If the patient is in more than one research study, please list each identification number separately.", "description": "\n**Coding Instruction:**\nIndicate the research study patient identification number as assigned by the research protocol.\n**Target Value:**\nN/A"}, "adjudication_event": {"field_id": "14312", "label": "Adjudication Event", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What event is being adjudicated? Please select from the following options:\n\n- Air Embolism\n- Cardiac Arrest\n- Heart Failure\n- Heart Valve Damage\n- Left Atrial Thrombus\n- Myocardial Infarction\n- Pericardial Effusion (no intervention required)\n- Pericarditis\n- Anaphylaxis\n- Arterial Thrombosis\n- Deep Vein Thrombosis\n- Systemic Thromboembolism (other than stroke) (Complete Adjudication)\n- Esophageal Injury (resulting from TEE probe)\n- Hepatic Injury\n- New Requirement for Dialysis\n- Device Explant\n- Device Infection\n- Device Migration\n- Device Thrombus\n- Device Systemic Embolization (catheter retrieval)\n- Device Systemic Embolization (surgical retrieval)\n- AV Fistula (no intervention required)\n- AV Fistula (requiring surgical repair) (Complete Adjudication)\n- Pseudoaneurysm (no intervention required)\n- Pseudoaneurysm (requiring endovascular repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring surgical repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring thrombin injection only) (Complete Adjudication)\n- Hemorrhagic Stroke (Complete Adjudication)\n- Intracranial Hemorrhage (other than hemorrhagic stroke) (Complete Adjudication)\n- Ischemic Stroke (Complete Adjudication)\n- TIA (Complete Adjudication)\n- Undetermined Stroke (Complete Adjudication)\n- Access Site Bleeding (Complete Adjudication)\n- GI Bleeding (Complete Adjudication)\n- Hematoma (Complete Adjudication)\n- Hemothorax (not requiring drainage) (Complete Adjudication)\n- Hemothorax (requiring drainage) (Complete Adjudication)\n- Other Hemorrhage (non-intracranial) (Complete Adjudication)\n- Pericardial Effusion (requiring open heart surgery) (Complete Adjudication)\n- Pericardial Effusion with tamponade (requiring percutaneous drainage) (Complete Adjudication)\n- Pericardial Effusion without tamponade (requiring percutaneous drainage) (Complete Adjudication)\n- Retroperitoneal Bleeding (Complete Adjudication)\n- Vascular Complications (Complete Adjudication)\n- Pleural Effusion\n- Pneumonia\n- Pneumothorax (no intervention required)\n- Pneumothorax (requiring intervention)\n- Pulmonary Embolism\n- Respiratory Failure", "description": "\n**Coding Instruction:**\nIndicate the event being adjudicated.\n**Target Value:**\nN/A"}, "adjudication_event_date": {"field_id": "14313", "label": "Adjudication Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date on which the clinical event being adjudicated occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the clinical event being adjudicated occurred.\n**Target Value:**\nN/A"}}, "neurologic": {"adjudication_status": {"field_id": "14902", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "multi_input_field", "if_deceased": {"adjudication_date_of_death": {"field_id": "14903", "label": "Adjudication Date of Death", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the patient was declared deceased?", "description": "\n**Coding Instruction:**\nIndicate the date the patient was declared deceased.\n**Target Value:**\nAny value between start of current procedure and discharge"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the patient's adjudication status (alive or deceased) on the date the adjudication was performed?", "description": "\n**Coding Instruction:**\nIndicate whether the patient was alive or deceased on the date the adjudication was performed.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "symptom_onset_date": {"field_id": "14904", "label": "Symptom Onset Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date of symptom onset associated with this event?", "description": "\n**Coding Instruction:**\nIndicate the date of symptom onset associated with this event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "neurologic_deficit_with_rapid_onset": {"field_id": "14905", "label": "Neurologic Deficit with Rapid Onset", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"neurologic_deficit_clinical_presentation": {"field_id": "14906", "label": "Neurologic Deficit Clinical Presentation", "options": ["Stroke-related", "Non-Stroke-related"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the clinical presentation of the neurologic deficit observed in the patient? Please specify if it was stroke-related, non-stroke-related, or other.", "description": "\n**Coding Instruction:**\nIndicate the clinical presentation of the neurologic deficit.\n**Target Value:**\nAny value between start of current procedure and discharge"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient experience a sudden onset of a focal or global neurologic deficit at any time between the start of the current TAVR procedure and discharge?", "description": "\n**Coding Instruction:**\nIndicate if the patient had a sudden onset of a focal or global neurologic deficit regardless of the duration of symptoms.\nRapid onset means sudden or maximal within minutes.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "diagnosis_confirmation_by_neurology": {"field_id": "14907", "label": "Diagnosis Confirmation by Neurology", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the diagnosis confirmed by a neurologist or a neurosurgeon?", "description": "\n**Coding Instruction:**\nIndicate if the diagnosis was confirmed by a neurologist or a neurosurgeon.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "brain_imaging_performed": {"field_id": "14908", "label": "Brain Imaging Performed", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"brain_imaging_type": {"field_id": "14909", "label": "Brain Imaging Type", "options": [{"id": "1", "value": "Cerebral Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Other"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of neurologic imaging was performed during the procedure? Please select from the following options:\n- Cerebral Angiography\n- Computed Tomography\n- Magnetic Resonance Imaging", "description": "\n**Coding Instruction:**\nIndicate the type of neurologic imaging which was performed.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "deficit_type": {"field_id": "14910", "label": "Deficit Type", "options": [{"id": "1", "value": "No deficit"}, {"id": "2", "value": "Infarction"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Both"}], "if_hemorrhage": {"hemorrhagic_stroke_type": {"field_id": "14911", "label": "Hemorrhagic Stroke Type", "options": [{"id": "1", "value": "Intracerebral"}, {"id": "2", "value": "Subarachnoid"}, {"id": "3", "value": "Subdural"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of hemorrhagic stroke did the patient experience, specifically indicating the location of the hemorrhage? The options are:\n- Intracerebral\n- Subarachnoid\n- Subdural", "description": "\n**Coding Instruction:**\nFor patients presenting with an intracranial hemorrhage, indicate the hemorrhage location.\n**Target Value:**\nAll values between start of current procedure and discharge"}}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of deficit was identified by the neuroimaging study during the TAVR procedure? The options are:\n- No deficit\n- Infarction\n- Hemorrhage\n- Both", "description": "\n**Coding Instruction:**\nIndicate the type of deficit identified by the neuroimaging study.\n**Target Value:**\nAll values between start of procedure and end of procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo any neuro imaging, such as CT, MRI, or cerebral angiography, to confirm the diagnosis?", "description": "\n**Coding Instruction:**\nIndicate if neuro imaging (such as CT, MRI, cerebral angiography) was performed in an attempt to confirm the diagnosis.\n**Target Value:**\nAll values between start of current procedure and discharge"}, "subsequent_iv_rtpa_administered": {"field_id": "14912", "label": "Subsequent IV rtPA Administered", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was intravenous recombinant tissue plasminogen activator (rtPA) administered as a treatment option related to this event?", "description": "\n**Coding Instruction:**\nIndicate if intravascular (IV) recombinant tissue plasminogen activator (rtPA) was used as a treatment option related to this event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "subsequent_endovascular_therapeutic_intervention": {"field_id": "14913", "label": "Subsequent Endovascular Therapeutic Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo any endovascular interventional therapy as a treatment option related to this event?", "description": "\n**Coding Instruction:**\nIndicate if an endovascular interventional therapy was performed as a treatment option related to this event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "symptoms_duration": {"field_id": "14914", "label": "Symptoms Duration", "options": [{"id": "1", "value": "< 1 Hour"}, {"id": "2", "value": "1 – 24 Hours"}, {"id": "3", "value": "> 24 Hours"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the duration of the neurologic symptoms experienced by the patient? Please select one of the following options:\n- Less than 1 Hour\n- 1 - 24 Hours\n- Greater than 24 Hours", "description": "\n**Coding Instruction:**\nIndicate the duration (in hours) of the neurologic symptoms.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "trauma": {"field_id": "14915", "label": "<PERSON>rauma", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient experience any physical trauma within 24 hours prior to the neurologic event?", "description": "\n**Coding Instruction:**\nIndicate if the patient experienced a physical trauma within 24 hours prior to the neurologic event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "modified_rankin_scale_mrs": {"field_id": "14916", "label": "Modified Rankin Scale (mRS)", "dependency": "14917", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "6: Death"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the patient's functional ability according to the Modified Rankin Scale (mRS) administered following the event? Please select one of the following options:\n- 0: No symptoms at all\n- 1: No significant disability despite symptoms (Able to carry out all usual duties and activities)\n- 2: Slight disability (Unable to carry out all previous activities, but able to look after own affairs without assistance)\n- 3: Moderate disability (Requiring some help, but able to walk without assistance)\n- 4: Moderately severe disability (Unable to walk without assistance and unable to attend to own bodily needs without assistance)\n- 5: Severe disability (Bedridden, incontinent and requiring constant nursing care and attention)\n- 6: Death", "description": "\n**Coding Instruction:**\nIndicate the patients functional ability according to the modified Rankin Scale (mRS) administered following the event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "not_administered": {"field_id": "14917", "label": "Not Administered", "input_type": "checkbox", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the status of the Modified Rankin Scale (mRS) administration following the event? Specifically, indicate if the mRS was not administered.", "description": "\n**Coding Instruction:**\nIndicate if the modified Rankin Scale (mRS) was not administered following the event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "procedure_related_neurologic_event": {"field_id": "14918", "label": "Procedure Related Neurologic Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the likelihood that the neurologic event is related to the LAAO procedure based on the clinician's best clinical judgment? Please select one of the following options:\n- Certain: The clinical adverse event occurs in a plausible time relationship to the procedure and cannot be explained by concurrent disease or other drugs or devices.\n- Probable: The clinical adverse event occurs within a reasonable time sequence to the procedure and it is unlikely to be attributed to concurrent disease or other drugs or devices.\n- Possible: The clinical adverse event occurs within a reasonable time sequence to the procedure, but the event could also be explained by concurrent disease or other drugs or devices.\n- Unlikely: The clinical adverse event, based upon its temporal relationship to the procedure, makes a causal relationship improbable. Additionally, other drugs, devices or underlying disease provide plausible explanations.\n- Unclassifiable: The clinical adverse event is reported yet more data is essential for a proper assessment OR the clinical adverse event is reported yet the causality cannot be judged because information is insufficient or contradictory, and cannot be supplemented or verified.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "device_related_neurologic_event": {"field_id": "14931", "label": "Device Related Neurologic Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the likelihood that the neurologic event is related to the LAAO device based on the clinician's best clinical judgment? Please select one of the following options:\n- Certain: The clinical adverse event occurs in a plausible time relationship to the procedure and cannot be explained by concurrent disease or other drugs or devices.\n- Probable: The clinical adverse event occurs within a reasonable time sequence to the procedure and it is unlikely to be attributed to concurrent disease or other drugs or devices.\n- Possible: The clinical adverse event occurs within a reasonable time sequence to the procedure, but the event could also be explained by concurrent disease or other drugs or devices.\n- Unlikely: The clinical adverse event, based upon its temporal relationship to the procedure, makes a causal relationship improbable. Additionally, other drugs, devices or underlying disease provide plausible explanations.\n- Unclassifiable: The clinical adverse event is reported yet more data is essential for a proper assessment OR the clinical adverse event is reported yet the causality cannot be judged because information is insufficient or contradictory, and cannot be supplemented or verified.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between start of current procedure and discharge"}}, "bleeding": {"adjudication_status": {"field_id": "14924", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "multi_input_field", "if_deceased": {"adjudication_date_of_death": {"field_id": "14930", "label": "Adjudication Date of Death", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the patient was declared deceased?", "description": "\n**Coding Instruction:**\nIndicate the date the patient was declared deceased.\n**Target Value:**\nAny value between start of current procedure and discharge"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the adjudication status of the patient (alive or deceased) on the date the adjudication was performed?", "description": "\n**Coding Instruction:**\nIndicate whether the patient was alive or deceased on the date the adjudication was performed.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "invasive_intervention_required": {"field_id": "14929", "label": "Invasive Intervention Required", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was a surgical or percutaneous intervention required to treat the patient for the bleeding event during the current procedure?", "description": "\n**Coding Instruction:**\nIndicate if there was a surgical or percutaneous intervention required to treat the patient for this bleeding event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "rbc_transfusion": {"field_id": "14919", "label": "RBC Transfusion", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was there at least one transfusion of packed red blood cells (PRBCs) given to treat the patient for this bleeding event during the current procedure and before discharge?", "description": "\n**Coding Instruction:**\nIndicate if there was at least one transfusion of PRBCs given to treat the patient for this bleeding event.\n**Target Value:**\nAll values between start of current procedure and discharge"}, "number_of_rbc_units_transfused": {"field_id": "14920", "label": "Number of RBC Units Transfused", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the total number of packed red blood cell (PRBC) units transfused for the treatment of the bleeding event during the current procedure and up until discharge?", "description": "\n**Coding Instruction:**\nIndicate the number of PRBC units transfused for treatment of this bleeding event.\n**Target Value:**\nAll values between start of current procedure and discharge"}, "hemoglobin_pre_transfusion": {"field_id": "14921", "label": "Hemoglobin Pre-Transfusion", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the lowest hemoglobin (Hgb) value (g/dL) obtained via lab assay or point of care assay between the intra or post-procedure bleeding event and prior to the transfusion for the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, between the intra or post procedure bleeding event and prior to the transfusion.\n**Target Value:**\nAll values between start of current procedure and discharge"}, "diagnostic_imaging_performed": {"field_id": "14922", "label": "Diagnostic Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was any diagnostic imaging, such as CT or MRI, performed to confirm the diagnosis during the current procedure up until discharge?", "description": "\n**Coding Instruction:**\nIndicate if imaging (such as CT, MRI) was performed in an attempt to confirm the diagnosis.\n**Target Value:**\nAll values between start of current procedure and discharge"}, "end_organ_damage": {"field_id": "14923", "label": "End Organ Damage", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient experience end organ damage following the bleeding event associated with the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the patient was diagnosed with end organ damage after this bleeding event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "major_surgery_within_past_30_days": {"field_id": "14927", "label": "Major Surgery within Past 30 days", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo any major surgery within 30 days prior to the bleeding event?", "description": "\n**Coding Instruction:**\nIndicate if the patient underwent surgery within 30 days prior to this bleeding event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "percutaneous_coronary_intervention_within_past_30_days": {"field_id": "14928", "label": "Percutaneous Coronary Intervention within Past 30 days", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo a percutaneous coronary artery or percutaneous valvular intervention within 30 days prior to the bleeding event associated with the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the patient had a percutaneous coronary artery or percutaneous valvular intervention within 30 days prior to this bleeding event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "procedure_related_bleeding_event": {"field_id": "14925", "label": "Procedure Related Bleeding Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the likelihood that the bleeding event observed and documented in the medical record, which was associated with a hematocrit drop of 10% and/or a hemoglobin drop of 3 g/dL or that required transfusion or surgical intervention, is related to the LAAO procedure based on the clinician's best clinical judgment? Please select from the following options: Certain, Probable, Possible, Unlikely, or Unclassifiable.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "device_related_bleeding_event": {"field_id": "14926", "label": "<PERSON>ce Related Bleeding Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Based on the provided coder dictionary entry, here is a well-formed question to retrieve the requested data:\n\n\"Based on the clinician's best clinical judgment, what is the likelihood that the observed bleeding event is related to the LAAO device? Please select one of the following options: Certain, Probable, Possible, Unlikely, or Unclassifiable.\"", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between start of current procedure and discharge"}}, "systemic_thromboembolism": {"verified": "", "adjudication_status": {"field_id": "14932", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "multi_input_field", "if_deceased": {"adjudication_date_of_death": {"field_id": "14933", "label": "Adjudication Date of Death", "format": "mm/dd/yyyy", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the patient was declared deceased?", "description": "\n**Coding Instruction:**\nIndicate the date the patient was declared deceased.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "death_cause": {"field_id": "14934", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the cause of death for the patient? Specifically, indicate if the death was due to systemic thromboembolization, focal end-organ hypoperfusion resulting from systemic thromboembolism, or a therapeutic intervention to treat systemic thromboembolism.", "description": "\n**Coding Instruction:**\nIf deceased, indicate if the patient's death cause was due to systemic thromboembolization, or focal end-organ hypoperfusion resulting from systemic thromboembolism, or therapeutic intervention to treat systemic thromboembolism.\n**Target Value:**\nAny value between start of current procedure and discharge"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the adjudication status of the patient (alive or deceased) on the date the adjudication was performed?", "description": "\n**Coding Instruction:**\nIndicate whether the patient was alive or deceased on the date the adjudication was performed.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "focal_end_organ_hypoperfusion_present": {"field_id": "14935", "label": "Focal End-Organ Hypoperfusion Present", "options": ["No", "Yes"], "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Is there evidence that focal end-organ hypoperfusion resulted from the systemic thromboembolism event during the current procedure?", "description": "\n**Coding Instruction:**\nIndicate if focal end-organ hypoperfusion resulted from the systemic thromboembolism event.\n**Target Value:**\nAny value between start of current procedure and discharge"}, "systemic_thromboembolization_imaging_evidence": {"field_id": "14939", "label": "Systemic Thromboembolization Imaging Evidence", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"imaging_method": {"field_id": "14936", "label": "Imaging Method", "options": [{"id": "1", "value": "Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Ultrasound"}, {"id": "5", "value": "Other Imaging"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What imaging method was used to identify systemic thromboembolism during the TAVR procedure? Please select from the following options:\n- Angiography\n- Computed Tomography\n- Magnetic Resonance Imaging\n- Ultrasound\n- Other Imaging", "description": "\n**Coding Instruction:**\nIndicate the imaging method to identify systemic thromboembolism.\n**Target Value:**\nAll values between start of current procedure and discharge"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the imaging evidence indicating systemic thromboembolism during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if imaging evidence indicated systemic thromboembolism.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "therapeutic_intervention_performed": {"field_id": "14937", "label": "Therapeutic Intervention Performed", "options": ["No", "Yes"], "input_type": "multi_input_field", "if_yes": {"intervention_type": {"field_id": "14938", "label": "Intervention Type", "options": [{"id": "1", "value": "Catheter"}, {"id": "2", "value": "Pharmacological"}, {"id": "3", "value": "Surgical"}, {"id": "4", "value": "Other"}], "input_type": "multi_select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of intervention was performed during the TAVR procedure? Please select from the following options: Catheter, Pharmacological, Surgical, or Other.", "description": "\n**Coding Instruction:**\nIndicate the intervention type.\n**Target Value:**\nAll values between start of procedure and end of procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was any pharmacological, catheter, surgical, or other therapeutic intervention performed to treat the systemic thromboembolism during the procedure?", "description": "\n**Coding Instruction:**\nIndicate if any pharmacological, catheter, surgical, or other therapeutic intervention was performed to treat the systemic thromboembolism.\n**Target Value:**\nAll values between start of procedure and end of procedure"}}, "medication": {"verified": "", "field_id": "14940", "label": "Medication", "medications": {"fondaparinux": {"field_id": "14941", "label": "Fondaparinux", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No "}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "heparin_derivative": {"field_id": "14941", "label": "Heparin Derivative", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "low_molecular_weight_heparin": {"field_id": "14941", "label": "Low Molecular Weight Heparin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "unfractionated_heparin": {"field_id": "14941", "label": "Unfractionated Heparin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "warfarin": {"field_id": "14941", "label": "Warfarin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "aspirin_81_100_mg": {"field_id": "14941", "label": "Aspirin(81-100 mg)", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "aspirin_101_324_mg": {"field_id": "14941", "label": "Aspirin(101-324 mg)", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "aspirin_325_mg": {"field_id": "14941", "label": "Aspirin 325 mg", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "aspirin_dipyridamole": {"field_id": "14941", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "vorapaxar": {"field_id": "14941", "label": "Vorapaxar", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "apixaban": {"field_id": "14941", "label": "Apixaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "dabigatran": {"field_id": "14941", "label": "Dabigatran", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "edoxaban": {"field_id": "14941", "label": "Edoxaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "rivaroxaban": {"field_id": "14941", "label": "Rivaroxaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "cangrelor": {"field_id": "14941", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "clopidogrel": {"field_id": "14941", "label": "Clopidogrel", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "other_p2y12": {"field_id": "14941", "label": "Other P2Y12", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "prasugrel": {"field_id": "14941", "label": "Prasug<PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "ticagrelor": {"field_id": "14941", "label": "Ticagrelor", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}, "ticlopidine": {"field_id": "14941", "label": "Ticlopidine", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between start of procedure and end of procedure"}}, "value_source": "Embeddings", "q_prompt": "What medications was the patient taking or administered at the time of the event? Please provide the NCDR assigned identification number for each medication from the following list:Fondaparinux,Heparin Derivative,Low Molecular Weight Heparin,Unfractionated Heparin,Warfarin,Aspirin 81 to 100 mg,Aspirin 101 to 324 mg,Aspirin 325 mg,Aspirin/Dipyridamole,Vorapaxar,Apixaban,Dabigatran,Edoxaban,Rivaroxaban,Cangrelor,Clopidogrel,Other P2Y12,Prasugrel,Ticagrelor,T<PERSON>lopidine", "description": "\n**Coding Instruction:**\nIndicate the NCDR assigned identification number for the medications the patient was taking or administered at the time of the event.\n**Target Value:**\nAll values between start of current procedure and discharge"}}}