<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Callback</title>
    <script src="https://cdn.jsdelivr.net/npm/fhirclient/build/fhir-client.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        h2 {
            color: #333;
            margin-bottom: 10px;
        }

        .info {
            font-size: 18px;
            margin: 10px 0;
            color: #555;
        }

        .info span {
            font-weight: bold;
            color: #222;
        }

        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            transition: 0.3s;
        }

        .button:hover {
            background: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h2>Patient Information</h2>
        <p class="info">Name: <span id="name">N/A</span></p>
        <p class="info">Birth Date: <span id="birthDate">N/A</span></p>
        <p class="info">Gender: <span id="gender">N/A</span></p>
        <button class="button" id="fetchButton" onclick="getAuthUrl()">Send Details to AtriAI</button>
    </div>
    <script src="../config.js"></script>

    <script type="text/javascript">

        const queryString = window.location.search;
        const searchParams = new URLSearchParams(queryString);
        const callback_code = searchParams.get('code');

        let myApp = {};
        FHIR.oauth2.ready().then((client) => {
            // console.log('clint', client)
            myApp.smart = client;
            getPatient()

        });
        const getPatient = async () => {
            let patient = await fetch(`${myApp.smart.state.serverUrl}/Patient/${myApp.smart.patient.id}`, {
                headers: {
                    "Accept": "application/json",
                    "Authorization": `Bearer ${myApp.smart.state.tokenResponse.access_token}`
                }
            }).then((data) => data);
            let patientName = '';
            let patientData = await patient.json();
            // console.log("patientData0", patientData)
            localStorage.setItem("patientData", JSON.stringify(patientData));
            localStorage.setItem("epic_access_token", myApp.smart.state.tokenResponse.access_token);
            localStorage.setItem("epic_server_url", myApp.smart.state.serverUrl);
            localStorage.setItem("epic_patient_id", myApp.smart.patient.id);
            for (const name of patientData.name) {

                if (name.use == 'official') {
                    patientName = `${name.given.join(' ')} ${name.family}`
                }
            }
            document.getElementById("name").innerHTML = patientName;
            document.getElementById("birthDate").innerHTML = patientData.birthDate;
            document.getElementById("gender").innerHTML = patientData.gender;
        }

        async function getAuthUrl() {
            const patientdata = JSON.parse(localStorage.getItem('patientData'));
            if (patientdata.issue) {
                alert("Patient Data not found")
                window.location.href = "launch.html";
                localStorage.clear()
            } else {
                const url = `${config.apiUrl}/auth?redirect_url=${config.epic_vendor_redirect_url}`
                window.location.href = url;
            }
        }
    </script>

</body>

</html>