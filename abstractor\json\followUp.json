{"follow_up": {"assessment_date": {"field_id": "11000", "label": "Assessment Date", "options": [], "input_type": "string", "value": ""}, "follow_up_interval": {"field_id": "14851", "label": "Follow-up Interval", "options": [{"id": "", "value": "45 day"}, {"id": "", "value": "6 month"}, {"id": "", "value": "1 year"}, {"id": "", "value": "2 year"}], "input_type": "select", "value": "id"}, "reference_episode_arrival_date": {"field_id": "14946", "label": "Reference Episode Arrival Date", "options": [], "input_type": "string", "value": ""}, "reference_episode_discharge_date": {"field_id": "14338", "label": "Reference Episode Discharge Date", "options": [], "input_type": "string", "value": ""}, "reference_procedure_start_date_time": {"field_id": "11001", "label": "Reference Procedure Start Date/Time", "options": [], "input_type": "string", "value": ""}, "method_to_determine_follow_up_status": {"field_id": "11003", "label": "Method(s) to Determine Follow-up Status", "options": [{"id": "", "value": "Office Visit"}, {"id": "", "value": "Medical Records"}, {"id": "", "value": "Letter from Medical Provider"}, {"id": "", "value": "Phone Call"}, {"id": "", "value": "Social Security Death Master File"}, {"id": "", "value": "Hospitalized"}, {"id": "", "value": "Other"}], "input_type": "select", "value": "id"}, "follow_up_status": {"field_id": "11004", "label": "Follow-up Status", "options": [{"id": "", "value": "Alive"}, {"id": "", "value": "Deceased"}, {"id": "", "value": "Lost to Follow-up"}], "input_type": "select", "value": "id"}, "date_of_death": {"field_id": "11006", "label": "Date of Death", "options": [], "input_type": "string", "value": ""}, "cause_of_death": {"field_id": "11007", "label": "Cause of Death", "options": [{"id": "", "value": "Acute myocardial infarction"}, {"id": "", "value": "Sudden cardiac death"}, {"id": "", "value": "Heart failure"}, {"id": "", "value": "Stroke"}, {"id": "", "value": "Cardiovascular procedure"}, {"id": "", "value": "Cardiovascular hemorrhage"}, {"id": "", "value": "Other cardiovascular reason"}, {"id": "", "value": "Pulmonary"}, {"id": "", "value": "Renal"}, {"id": "", "value": "Gastrointestinal"}, {"id": "", "value": "Hepatobiliary"}, {"id": "", "value": "Pancreatic"}, {"id": "", "value": "Infection"}, {"id": "", "value": "Inflammatory/Immunologic"}, {"id": "", "value": "Hemorrhage"}, {"id": "", "value": "Non-cardiovascular procedure or surgery"}, {"id": "", "value": "<PERSON>rauma"}, {"id": "", "value": "Suicide"}, {"id": "", "value": "Neurological"}, {"id": "", "value": "Malignancy"}, {"id": "", "value": "Other non-cardiovascular reason"}], "input_type": "select", "value": "id"}, "barthel_index_evaluation": {"barthel_index_evaluation_performed": {"field_id": "14891", "label": "Barthel Index Evaluation Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "feeding": {"field_id": "14892", "label": "Feeding", "options": [{"id": "", "value": "Unable"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "bathing": {"field_id": "14893", "label": "Bathing", "options": [{"id": "", "value": "Dependent"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "grooming": {"field_id": "14894", "label": "Grooming", "options": [{"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "dressing": {"field_id": "14895", "label": "Dressing", "options": [{"id": "", "value": "Dependent"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "bowels": {"field_id": "14896", "label": "Bowels", "options": [{"id": "", "value": "Incontinent"}, {"id": "", "value": "Inconsistent"}, {"id": "", "value": "Continent"}], "input_type": "select", "value": "id"}, "bladder": {"field_id": "14897", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": "", "value": "Incontinent"}, {"id": "", "value": "Inconsistent"}, {"id": "", "value": "Continent"}], "input_type": "select", "value": "id"}, "toilet_use": {"field_id": "14898", "label": "Toilet Use", "options": [{"id": "", "value": "Dependent"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "transfers": {"field_id": "14899", "label": "Transfers", "options": [{"id": "", "value": "Unable"}, {"id": "", "value": "Major <PERSON><PERSON> Needed"}, {"id": "", "value": "Minor Assist Needed"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "mobility": {"field_id": "14900", "label": "Mobility", "options": [{"id": "", "value": "Immobile"}, {"id": "", "value": "Wheelchair"}, {"id": "", "value": "One Person Assist"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "stairs": {"field_id": "14901", "label": "Stairs", "options": [{"id": "", "value": "Unable"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}}}, "diagnostic_studies": {"lvef_assessed": {"field_id": "14858", "label": "LVEF Assessed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "lvef": {"field_id": "13690", "label": "LVEF", "options": [], "input_type": "string", "value": ""}, "transthoracic_echo_performed": {"field_id": "14859", "label": "Transthoracic Echo (TTE) Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_tte": {"field_id": "14873", "label": "Date of TTE", "options": [], "input_type": "string", "value": ""}, "transesophageal_echo_performed_tee": {"field_id": "14874", "label": "Transesophageal Echo Performed (TEE)", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_tee": {"field_id": "14875", "label": "Date of TEE", "options": [], "input_type": "string", "value": ""}, "cardiac_ct_performed": {"field_id": "14876", "label": "Cardiac CT Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_cardiac_ct": {"field_id": "14877", "label": "Date of Cardiac CT", "options": [], "input_type": "string", "value": ""}, "cardiac_mri_performed": {"field_id": "14878", "label": "Cardiac MRI Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_cardiac_mri": {"field_id": "14879", "label": "Date of Cardiac MRI", "options": [], "input_type": "string", "value": ""}, "intracardiac_echo_performed": {"field_id": "14880", "label": "Intracardiac Echo Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_intracardiac_echo": {"field_id": "14881", "label": "Date of Intracardiac Echo", "options": [], "input_type": "string", "value": ""}, "atrial_thrombus_detected": {"field_id": "14882", "label": "Atrial Thrombus Detected", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "device_margin_residual_leak": {"field_id": "14884", "label": "<PERSON><PERSON> Residual Leak", "options": [{"id": "", "value": "Not Assessed"}], "input_type": "select", "value": "id"}}, "physical_exam_and_labs": {"creatinine": {"field_id": "14886", "label": "Creatinine", "options": [{"id": "", "value": "Not Drawn"}], "input_type": "radio", "value": ""}, "hemoglobin": {"field_id": "14888", "label": "Hemoglobin", "options": [{"id": "", "value": "Not Drawn"}], "input_type": "radio", "value": ""}, "modified_rankin_scale_mrs": {"field_id": "13148", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "", "value": "0: No symptoms at all"}, {"id": "", "value": "1: No significant disability despite symptoms"}, {"id": "", "value": "2: Slight disability"}, {"id": "", "value": "3: Moderate disability"}, {"id": "", "value": "4: Moderately severe disability"}, {"id": "", "value": "5: Severe disability"}, {"id": "", "value": "6: Death"}, {"id": "", "value": "Not Administered"}], "input_type": "select", "value": "id"}}}