const values = [
  "21mm WATCHMAN LAA Closure Device",
  "24mm WATCHMAN LAA Closure Device",
  "27mm WATCHMAN LAA Closure Device",
  "30mm WATCHMAN LAA Closure Device",
  "33mm WATCHMAN LAA Closure Device",
  "20 mm Amplatzer Septal Occluder",
  "22 mm Amplatzer Septal Occluder",
  "24 mm Amplatzer Septal Occluder",
  "26 mm Amplatzer Septal Occluder",
  "30 mm Amplatzer Septal Occluder",
  "32 mm Amplatzer Septal Occluder",
  "34 mm Amplatzer Septal Occluder",
  "36 mm Amplatzer Septal Occluder",
  "38 mm Amplatzer Septal Occluder",
  "LARIAT Suture Delivery Device W 40 mm X H 20 mm X L 70 mm (30-02)",
  "LARIAT + Suture Delivery Device W 45 mm X H 20 mm X L 70 mm (30-05)",
  "LARIAT + Suture Delivery Device W 45 mm X H 20 mm X L 70 mm (90-01)",
  "7 mm Amplatzer Septal Occluder",
  "8 mm Amplatzer Septal Occluder",
  "9 mm Amplatzer Septal Occluder",
  "10 mm Amplatzer Septal Occluder",
  "11 mm Amplatzer Septal Occluder",
  "12 mm Amplatzer Septal Occluder",
  "13 mm Amplatzer Septal Occluder",
  "14 mm Amplatzer Septal Occluder",
  "15 mm Amplatzer Septal Occluder",
  "16 mm Amplatzer Septal Occluder",
  "17 mm Amplatzer Septal Occluder",
  "18 mm Amplatzer Septal Occluder",
  "19 mm Amplatzer Septal Occluder",
  "4 mm Amplatzer Septal Occluder",
  "5 mm Amplatzer Septal Occluder",
  "6 mm Amplatzer Septal Occluder",
  "6 mm Amplatzer Duct Occluder II",
  "Lariat RS Suture Delivery Device W 45 mm X H 20 mm X L 70 mm (30-06)",
  "20 mm CARDIOFORM Septal Occluder",
  "25 mm CARDIOFORM Septal Occluder",
  "30 mm CARDIOFORM Septal Occluder",
  "14 mm Amplatzer Vascular Plug II",
  "12 mm Amplatzer Vascular Plug II",
  "3 mm Amplatzer Duct Occluder II",
  "5 mm Amplatzer Duct Occluder II",
  "16 mm Amplatzer Vascular Plug II",
  "25 mm Amplatzer Multifenestrated Septal Occluder - Cribriform",
  "10 mm Amplatzer Vascular Plug II",
  "20 mm WATCHMAN FLX LAA Closure Device",
  "24 mm WATCHMAN FLX LAA Closure Device",
  "27 mm WATCHMAN FLX LAA Closure Device",
  "31 mm WATCHMAN FLX LAA Closure Device",
  "35 mm WATCHMAN FLX LAA Closure Device",
  "25 mm Amplatzer PFO Occluder",
  "4 mm Amplatzer Duct Occluder II",
  "Amplatzer Amulet 16 mm",
  "Amplatzer Amulet 18 mm",
  "Amplatzer Amulet 20 mm",
  "Amplatzer Amulet 22 mm",
  "Amplatzer Amulet 25 mm",
  "Amplatzer Amulet 28 mm",
  "Amplatzer Amulet 31 mm",
  "Amplatzer Amulet 34 mm",
  "35 mm Amplatzer PFO Occluder",
  "8 mm Amplatzer Vascular Plug 4",
  "18 mm Amplatzer PFO Occluder",
  "8mm Amplatzer Vascular Plug II",
  "27 mm Cardioform ASD Occluder",
  "32 mm Cardioform ASD Occluder",
  "37 mm Cardioform ASD Occluder",
  "44 mm Cardioform ASD Occluder",
  "48 mm Cardioform ASD Occluder",
  "6 mm Amplatzer Vascular Plug II",
  "30 mm Amplatzer Talisman PFO Occluder",
  "18 mm Amplatzer Talisman PFO Occluder",
  "25 mm Amplatzer Talisman PFO Occluder",
  "35 mm Amplatzer Talisman PFO Occluder",
  "3 mm Amplatzer Vascular Plug II",
  "4 mm Amplatzer Vascular Plug II",
  "18 mm Amplatzer Vascular Plug II",
  "20 mm Amplatzer Vascular Plug II",
  "22 mm Amplatzer Vascular Plug II",
  "4 mm Amplatzer Muscular VSD Occluder",
  "20 mm WATCHMAN FLX-PRO LAA Closure Device",
  "24 mm WATCHMAN FLX-PRO LAA Closure Device",
  "27 mm WATCHMAN FLX-PRO LAA Closure Device",
  "31 mm WATCHMAN FLX-PRO LAA Closure Device",
  "35 mm WATCHMAN FLX-PRO LAA Closure Device",
  "40 mm WATCHMAN FLX-PRO LAA Closure Device",
  "8 mm Amplatzer Muscular VSD Occluder",
  "6 mm Amplatzer Muscular VSD Occluder",
  "10 mm Amplatzer Muscular VSD Occluder",
  "12 mm Amplatzer Muscular VSD Occluder",
  "14 mm Amplatzer Muscular VSD Occluder",
  "16 mm Amplatzer Muscular VSD Occluder",
  "18 mm Amplatzer Muscular VSD Occluder",
  "7 mm Amplatzer Vascular Plug 4",
  "4 mm Amplatzer Vascular Plug 4",
  "5 mm Amplatzer Vascular Plug 4",
  "6 mm Amplatzer Vascular Plug 4"
];

const manufacturers = [
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "SentreHEART",
  "SentreHEART",
  "SentreHEART",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "St. Jude Medical",
  "SentreHEART",
  "GORE",
  "GORE",
  "GORE",
  "Abbott",
  "Abbott",
  "St. Jude Medical",
  "St. Jude Medical",
  "Abbott",
  "St. Jude Medical",
  "Abbott",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Abbott",
  "St. Jude Medical",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "GORE",
  "GORE",
  "GORE",
  "GORE",
  "GORE",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Boston Scientific",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott",
  "Abbott"
];


const options = values.map((value, index) => {
  return {
    "id": (index + 1).toString(),
    "value": value,
    "manufacturer": manufacturers[index]
  };
});

console.log(JSON.stringify(options, null, 2));
