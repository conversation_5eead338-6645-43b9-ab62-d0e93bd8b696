import {
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
  parseSSNInput,
} from "../utils.js";

export function renderPatientDemographics(patientData) {
  const container = document.getElementById("patientDemographics");
  container.innerHTML = ""; // Clear the container before rendering
  container.style.position = "relative"; // ensure relative position for absolute children

  // Helper function: Checks if all fields (except verified) are filled and updates the container border.
  function updateContainerHighlight() {
    // Iterate through all keys of demographics except "verified"
    for (const key in patientData.demographics) {
      if (key === "verified") continue;
      const data = patientData.demographics[key];
      if (typeof data === "object") {
        if (!data.value || data.value.toString().trim() === "") {
          // Not all fields are filled, but we don't need to track this
          // Just update the container style based on verification status
          break;
        }
      } else {
        if (!data || data.toString().trim() === "") {
          // Not all fields are filled, but we don't need to track this
          // Just update the container style based on verification status
          break;
        }
      }
    }
    // Highlight container based on verification status
    if (
      patientData.demographics.verified &&
      patientData.demographics.verified.value === "True"
    ) {
      // If verified is true, show green border
      container.style.border = "2px solid green";
      container.style.borderRadius = "8px";
    } else {
      // If not verified, show red border
      container.style.border = "2px solid red";
      container.style.borderRadius = "8px";
    }
  }
  // Create a set to track fields that should be skipped (fields with dependencies)
  const skipFields = new Set();

  // First pass: identify fields with dependencies
  Object.entries(patientData.demographics).forEach(([fieldKey, value]) => {
    if (fieldKey === "verified") return; // Skip the verified field

    if (value.dependency) {
      // This field has a dependency, find the field it depends on
      const dependentFieldKey = Object.keys(patientData.demographics).find(
        (k) => patientData.demographics[k].field_id === value.dependency
      );

      if (dependentFieldKey) {
        // Add the field with dependency to the skip list
        skipFields.add(fieldKey);
        // Also add the dependent field to the skip list
        skipFields.add(dependentFieldKey);
      }
    }
  });

  // Explicitly add SSN N/A and zipcode_na to the skip list
  if (patientData.demographics.ssn_na) skipFields.add("ssn_na");
  if (patientData.demographics.zipcode_na) skipFields.add("zipcode_na");

  // Create a container for the race field (we'll append it at the end)
  let raceContainer = null;
  if (
    patientData.demographics.race &&
    typeof patientData.demographics.race === "object"
  ) {

    raceContainer = document.createElement("div");
    raceContainer.classList.add("race-wrapper");
    raceContainer.style.position = "relative";
    raceContainer.style.marginBottom = "24px";
    raceContainer.style.width = "100%"; // Take full width
    raceContainer.style.textAlign = "left"; // Left-align all text content
    raceContainer.style.marginLeft = "0"; // No left margin
    raceContainer.style.paddingLeft = "0"; // No left padding
  }

  // Render regular fields (fields without dependencies)
  Object.entries(patientData.demographics).forEach(([fieldKey, fieldValue]) => {
    if (
      fieldKey === "verified" ||
      skipFields.has(fieldKey) ||
      fieldKey === "race"
    )
      return; // Skip verified field, fields with dependencies, and race field

    // Ensure fieldData is an object
    let fieldData = fieldValue;
    if (typeof fieldData !== "object" || fieldData === null) {
      fieldData = {
        value: fieldData,
        modified_by: "",
        label: fieldKey, // defaulting label to field name; adjust if needed
        field_id: fieldKey, // defaulting field_id to field name; adjust if needed
        input_type: "text", // defaulting input_type to text; adjust if needed
      };
      patientData.demographics[fieldKey] = fieldData;
    }

    // Create an outer wrapper for the tile
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px"; // margin between tiles

    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = `${fieldData.label} (${fieldData.field_id})`;
    if (fieldData.description) {
      label.setAttribute("title", fieldData.description);
    }
    fieldContainer.appendChild(label);

    // Depending on the input type, create the appropriate input element.
    let modifiedByDisplay = null;
    if (fieldData.input_type === "string" || fieldData.input_type === "text") {
      const input = document.createElement("input");
      input.type = "text";
      input.name = fieldKey;
      input.placeholder = `Enter ${fieldData.label}`;
      input.value = fieldData.value || "";

      updateTileStyle(fieldContainer, input.value);

      let previousValue = input.value;

      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use our existing validation utilities with a temporary callback
        // that tells us if the value was accepted
        let isValid = false;

        // Special handling for SSN field
        if (fieldKey === "ssn" && fieldData.field_id === "2030") {
          parseSSNInput(currentValue, (validatedValue) => {
            isValid = true;
            previousValue = validatedValue;

            // Update the model and UI only if the value is valid
            patientData.demographics[fieldKey].value = validatedValue;
            patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, validatedValue);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight();

            // Update the input value if the validation modified it
            if (validatedValue !== currentValue) {
              e.target.value = validatedValue;
            }
          });
        } else {
          validateStringInput(
            currentValue,
            fieldData.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update the model and UI only if the value is valid
              patientData.demographics[fieldKey].value = validatedValue;
              patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight();

              // Update the input value if the validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );
        }

        // If the validation didn't accept the value, revert to previous valid value
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      fieldContainer.appendChild(input);
    } else if (fieldData.input_type === "date") {
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${fieldKey}_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(fieldData.value);
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = fieldKey;
      dateInput.value = fieldData.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      updateTileStyle(fieldContainer, dateInput.value);

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        patientData.demographics[fieldKey].value = selectedDate;
        patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, selectedDate);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight();
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      fieldContainer.appendChild(dateWrapper);
    } else if (fieldData.input_type === "radio") {
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");

      let selectedValue = "";
      fieldData.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        const input = document.createElement("input");
        input.type = "radio";
        input.name = fieldKey;
        input.value = option;
        input.id = `${fieldKey}-${option}`;
        input.checked = fieldData.value === option;
        if (input.checked) {
          selectedValue = option;
        }

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            patientData.demographics[fieldKey].value = e.target.value;
            patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight();
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${fieldKey}-${option}`);
        optionLabel.innerText = option;
        if (fieldData.description) {
          optionLabel.setAttribute("title", fieldData.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      updateTileStyle(fieldContainer, selectedValue);
      fieldContainer.appendChild(radioContainer);
    } else if (fieldData.input_type === "select") {
      const select = document.createElement("select");
      select.name = fieldKey;

      if (!fieldData.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        select.appendChild(defaultOption);
      }

      fieldData.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id;
        optionElement.innerText = option.value;
        if (fieldData.value && fieldData.value === option.value) {
          optionElement.selected = true;
        }
        select.appendChild(optionElement);
      });

      const selectedOption = fieldData.options.find(
        (option) => option.value === fieldData.value
      );
      updateTileStyle(
        fieldContainer,
        selectedOption ? selectedOption.value : ""
      );

      select.addEventListener("change", (e) => {
        const selectedOption = fieldData.options.find(
          (option) => option.id === e.target.value
        );
        const newValue = selectedOption ? selectedOption.value : "";
        patientData.demographics[fieldKey].value = newValue;
        patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight();
      });

      fieldContainer.appendChild(select);
    } else if (fieldData.input_type === "checkbox") {
      const input = document.createElement("input");
      input.type = "checkbox";
      input.name = fieldKey;
      input.checked = fieldData.value === "True";

      updateTileStyle(fieldContainer, input.checked ? "True" : "");

      input.addEventListener("change", (e) => {
        const newValue = e.target.checked ? "True" : "";
        patientData.demographics[fieldKey].value = newValue;
        patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight();
      });

      fieldContainer.appendChild(input);
    } else if (fieldData.input_type === "multi_select") {
      // Initialize the value as an array if it's not already
      if (!Array.isArray(fieldData.value)) {
        fieldData.value = fieldData.value ? [fieldData.value] : [];
      }

      // Create a simpler dropdown implementation
      // Create dropdown container with relative positioning
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";

      // Make sure all parent containers allow overflow
      fieldContainer.style.overflow = "visible";
      const tileWrapper = fieldContainer.parentElement;
      if (tileWrapper && tileWrapper.classList.contains("tile-wrapper")) {
        tileWrapper.style.overflow = "visible";
      }
      // Also set the container's overflow to visible
      container.style.overflow = "visible";

      // Create dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.cursor = "pointer";
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";
      dropdownHeader.style.backgroundColor = "#fff";

      // Display selected values or placeholder
      const selectedText = document.createElement("span");
      selectedText.classList.add("selected-text");

      // Function to update the selected text display
      const updateSelectedText = () => {
        if (fieldData.value.length === 0) {
          selectedText.textContent = "Select options...";
        } else if (fieldData.value.length === 1) {
          const selectedOption = fieldData.options.find(
            (opt) => opt.value === fieldData.value[0]
          );
          selectedText.textContent = selectedOption
            ? selectedOption.value
            : fieldData.value[0];
        } else {
          selectedText.textContent = `${fieldData.value.length} options selected`;
        }
      };

      updateSelectedText();

      // Add dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create dropdown content (initially hidden)
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      // Position the dropdown directly under the header
      dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
      dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.overflowX = "hidden";
      dropdownContent.style.backgroundColor = "#fff";
      dropdownContent.style.border = "1px solid #ccc";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
      dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

      // Create a checkbox for each option
      fieldData.options.forEach((option) => {
        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.classList.add("checkbox-wrapper");
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";
        checkboxWrapper.style.padding = "8px 12px";
        checkboxWrapper.style.borderBottom = "1px solid #eee";
        checkboxWrapper.style.textAlign = "left";
        checkboxWrapper.style.cursor = "pointer";

        const input = document.createElement("input");
        input.type = "checkbox";
        input.name = `${field}-${option.id}`;
        input.value = option.id;
        input.id = `${field}-${option.id}`;
        input.style.marginRight = "4px"; // Add a small space between checkbox and text

        // Check if this option is in the selected values array
        input.checked = fieldData.value.includes(option.value);

        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(fieldData.value)
            ? [...fieldData.value]
            : [];

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(option.value)) {
              currentValues.push(option.value);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== option.value);
          }

          // Update the model
          patientData.demographics[field].value = currentValues;
          patientData.demographics[field].modified_by = "ABSTRACTOR";

          // Update selected text display
          updateSelectedText();

          // Update UI
          updateTileStyle(
            fieldContainer,
            currentValues.length > 0 ? "filled" : ""
          );
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight();
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${field}-${option.id}`);
        optionLabel.style.marginLeft = "0";
        optionLabel.style.display = "inline-block";
        optionLabel.style.whiteSpace = "nowrap";
        optionLabel.style.cursor = "pointer";
        optionLabel.style.flexGrow = "1";

        // Display field_id in parentheses if available
        if (option.field_id) {
          optionLabel.innerText = `${option.value} (${option.field_id})`;
        } else {
          optionLabel.innerText = option.value;
        }

        // Create a wrapper for the checkbox and label to ensure they're tightly aligned
        const inputLabelWrapper = document.createElement("div");
        inputLabelWrapper.style.display = "flex";
        inputLabelWrapper.style.alignItems = "center";
        inputLabelWrapper.style.gap = "0";

        inputLabelWrapper.appendChild(input);
        inputLabelWrapper.appendChild(optionLabel);
        checkboxWrapper.appendChild(inputLabelWrapper);
        dropdownContent.appendChild(checkboxWrapper);
      });

      // Function to position the dropdown content properly
      const positionDropdown = () => {
        const headerRect = dropdownHeader.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // Position dropdown below the header
        dropdownContent.style.top = `${headerRect.bottom}px`;

        // Ensure dropdown doesn't go off the right edge of the screen
        const rightEdge = headerRect.left + 250; // 250px is our dropdown width
        if (rightEdge > viewportWidth) {
          // Align to the right edge of the header instead
          dropdownContent.style.left = `${headerRect.right - 250}px`;
        } else {
          dropdownContent.style.left = `${headerRect.left}px`;
        }

        // Set max height based on available space
        const spaceBelow = viewportHeight - headerRect.bottom;
        const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
        dropdownContent.style.maxHeight = `${maxHeight}px`;
      };

      // We'll replace this event listener with our new implementation

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !dropdownContainer.contains(e.target) &&
          !dropdownContent.contains(e.target)
        ) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
        }
      });

      // Simple function to close the dropdown
      const closeDropdown = () => {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
        window.removeEventListener("scroll", closeDropdown);
      };

      // Clean up event listener when the component is removed
      const cleanupFunc = () => {
        if (document.body.contains(dropdownContent)) {
          document.body.removeChild(dropdownContent);
        }
        // Remove scroll event listener
        window.removeEventListener("scroll", closeDropdown);
      };

      // Store the cleanup function for potential future use
      dropdownContainer.cleanupFunc = cleanupFunc;

      // Add header to container, but add content to document body for better visibility
      dropdownContainer.appendChild(dropdownHeader);

      // Add the dropdown content to the document body when needed
      const showDropdown = () => {
        if (!document.body.contains(dropdownContent)) {
          document.body.appendChild(dropdownContent);
        }
        dropdownContent.style.display = "block";
        positionDropdown();
      };

      // Set up the click handler for the dropdown
      dropdownHeader.onclick = (e) => {
        e.stopPropagation();
        const isOpen = dropdownContent.style.display === "block";

        if (!isOpen) {
          showDropdown();
          dropdownArrow.innerHTML = "&#9652;"; // Up arrow
          window.addEventListener("scroll", closeDropdown);
        } else {
          closeDropdown();
        }
      };

      // Update the tile style based on whether any options are selected
      updateTileStyle(
        fieldContainer,
        fieldData.value.length > 0 ? "filled" : ""
      );

      fieldContainer.appendChild(dropdownContainer);
    } else if (fieldData.input_type === "multi_input_field") {
      const multiInputContainer = document.createElement("div");
      multiInputContainer.classList.add("multi-input-container");

      // Create radio buttons for options instead of text input and checkbox
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");

      // Create a container for conditional content that will be shown/hidden
      const conditionalContentContainer = document.createElement("div");
      conditionalContentContainer.classList.add(
        "conditional-content-container"
      );
      conditionalContentContainer.style.marginTop = "10px";
      conditionalContentContainer.style.padding = "10px";
      conditionalContentContainer.style.border = "1px solid #eee";
      conditionalContentContainer.style.borderRadius = "4px";
      conditionalContentContainer.style.display = "none"; // Initially hidden

      // Function to render conditional content based on selected option
      const renderConditionalContent = (selectedOption) => {
        // Clear previous content
        conditionalContentContainer.innerHTML = "";
        conditionalContentContainer.style.display = "none";

        let conditionalKey = null;

        // Determine which conditional key to use based on selected option
        if (selectedOption === "Yes" && fieldData.if_yes) {
          conditionalKey = "if_yes";
        } else if (selectedOption === "No" && fieldData.if_no) {
          conditionalKey = "if_no";
        } else if (selectedOption === "Alive" && fieldData.if_alive) {
          conditionalKey = "if_alive";
        } else if (selectedOption === "Deceased" && fieldData.if_deceased) {
          conditionalKey = "if_deceased";
        }

        // If we have a conditional key, render its content
        if (conditionalKey && fieldData[conditionalKey]) {
          conditionalContentContainer.style.display = "block";

          // Iterate through each field in the conditional content
          Object.entries(fieldData[conditionalKey]).forEach(
            ([subKey, subField]) => {
              // Create a container for this sub-field
              const subFieldContainer = document.createElement("div");
              subFieldContainer.classList.add("sub-field-container");
              subFieldContainer.style.marginBottom = "15px";

              // Create and append the label
              const subLabel = document.createElement("label");
              subLabel.classList.add("label", "cursor-pointer");
              subLabel.textContent = `${subField.label} ${
                subField.field_id ? `(${subField.field_id})` : ""
              }`;
              if (subField.description) {
                subLabel.setAttribute("title", subField.description);
              }
              subFieldContainer.appendChild(subLabel);

              // Initialize the sub-field value if not set
              if (!subField.value) {
                subField.value = "";
              }

              // Create the appropriate input based on sub-field type
              if (subField.input_type === "radio") {
                const subRadioContainer = document.createElement("div");
                subRadioContainer.classList.add("radio-container");

                subField.options.forEach((option) => {
                  const radioWrapper = document.createElement("div");
                  const input = document.createElement("input");
                  input.type = "radio";
                  input.name = `${field}-${subKey}`;
                  input.value = option;
                  input.id = `${field}-${subKey}-${option}`;
                  input.checked = subField.value === option;

                  input.addEventListener("change", (e) => {
                    if (e.target.checked) {
                      // Update the sub-field value
                      fieldData[conditionalKey][subKey].value = e.target.value;
                      fieldData[conditionalKey][subKey].modified_by =
                        "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight();
                    }
                  });

                  const optionLabel = document.createElement("label");
                  optionLabel.setAttribute(
                    "for",
                    `${field}-${subKey}-${option}`
                  );
                  optionLabel.innerText = option;
                  if (subField.description) {
                    optionLabel.setAttribute("title", subField.description);
                  }

                  radioWrapper.appendChild(input);
                  radioWrapper.appendChild(optionLabel);
                  subRadioContainer.appendChild(radioWrapper);
                });

                subFieldContainer.appendChild(subRadioContainer);
              } else if (subField.input_type === "select") {
                const select = document.createElement("select");
                select.name = `${field}-${subKey}`;

                // Add default option if no value is set
                if (!subField.value) {
                  const defaultOption = document.createElement("option");
                  defaultOption.value = "";
                  defaultOption.innerText = "Select an option";
                  defaultOption.disabled = true;
                  defaultOption.selected = true;
                  select.appendChild(defaultOption);
                }

                subField.options.forEach((option) => {
                  const optionElement = document.createElement("option");
                  optionElement.value = option.id;
                  optionElement.innerText = option.value;
                  if (subField.value && subField.value === option.value) {
                    optionElement.selected = true;
                  }
                  select.appendChild(optionElement);
                });

                select.addEventListener("change", (e) => {
                  const selOption = subField.options.find(
                    (option) => option.id === e.target.value
                  );
                  const newValue = selOption ? selOption.value : "";
                  fieldData[conditionalKey][subKey].value = newValue;
                  fieldData[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight();
                });

                subFieldContainer.appendChild(select);
              } else if (
                subField.input_type === "string" ||
                subField.input_type === "text"
              ) {
                // Create text input for string/text fields
                const input = document.createElement("input");
                input.type = "text";
                input.name = `${field}-${subKey}`;
                input.placeholder = `Enter ${subField.label}`;
                input.value = subField.value || "";

                let previousValue = input.value;

                input.addEventListener("input", (e) => {
                  const currentValue = e.target.value;

                  // Use validation utilities with temporary callback
                  let isValid = false;
                  validateStringInput(
                    currentValue,
                    subField.field_id,
                    (validatedValue) => {
                      isValid = true;
                      previousValue = validatedValue;

                      // Update model and UI only if value is valid
                      fieldData[conditionalKey][subKey].value = validatedValue;
                      fieldData[conditionalKey][subKey].modified_by =
                        "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight();

                      // Update input value if validation modified it
                      if (validatedValue !== currentValue) {
                        e.target.value = validatedValue;
                      }
                    }
                  );

                  // Revert to previous value if validation failed
                  if (!isValid) {
                    e.target.value = previousValue;
                  }
                });

                subFieldContainer.appendChild(input);
              } else if (subField.input_type === "date") {
                // Create date input
                const dateWrapper = document.createElement("div");
                dateWrapper.style.position = "relative";

                // Create display input
                const displayInput = document.createElement("input");
                displayInput.type = "text";
                displayInput.name = `${field}-${subKey}-display`;
                displayInput.readOnly = true;
                displayInput.value = formatDisplayDate(subField.value);
                displayInput.placeholder = "MM/DD/YYYY";
                displayInput.style.cursor = "pointer";

                // Hidden date input
                const dateInput = document.createElement("input");
                dateInput.type = "date";
                dateInput.name = `${field}-${subKey}`;
                dateInput.value = subField.value || "";
                dateInput.style.position = "absolute";
                dateInput.style.opacity = "0";
                dateInput.style.cursor = "pointer";

                // Set max date to today
                const today = new Date().toISOString().split("T")[0];
                dateInput.max = today;

                dateInput.addEventListener("change", (e) => {
                  const selectedDate = e.target.value;
                  displayInput.value = formatDisplayDate(selectedDate);
                  fieldData[conditionalKey][subKey].value = selectedDate;
                  fieldData[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight();
                });

                // Trigger date picker when clicking display input
                displayInput.addEventListener("click", () => {
                  dateInput.showPicker();
                });

                dateWrapper.appendChild(displayInput);
                dateWrapper.appendChild(dateInput);
                subFieldContainer.appendChild(dateWrapper);
              } else if (subField.input_type === "multi_select") {
                // Initialize the value as an array if it's not already
                if (!Array.isArray(subField.value)) {
                  subField.value = subField.value ? [subField.value] : [];
                }

                // Create dropdown container with relative positioning
                const dropdownContainer = document.createElement("div");
                dropdownContainer.classList.add("dropdown-container");
                dropdownContainer.style.position = "relative";
                dropdownContainer.style.width = "100%";

                // Make sure all parent containers allow overflow
                subFieldContainer.style.overflow = "visible";

                // Create dropdown header/button
                const dropdownHeader = document.createElement("div");
                dropdownHeader.classList.add("dropdown-header");
                dropdownHeader.style.padding = "8px 12px";
                dropdownHeader.style.border = "1px solid #ccc";
                dropdownHeader.style.borderRadius = "4px";
                dropdownHeader.style.cursor = "pointer";
                dropdownHeader.style.display = "flex";
                dropdownHeader.style.justifyContent = "space-between";
                dropdownHeader.style.alignItems = "center";
                dropdownHeader.style.backgroundColor = "#fff";

                // Display selected values or placeholder
                const selectedText = document.createElement("span");
                selectedText.classList.add("selected-text");

                // Function to update the selected text display
                const updateSelectedText = () => {
                  if (subField.value.length === 0) {
                    selectedText.textContent = "Select options...";
                  } else if (subField.value.length === 1) {
                    const selectedOption = subField.options.find(
                      (opt) => opt.value === subField.value[0]
                    );
                    selectedText.textContent = selectedOption
                      ? selectedOption.value
                      : subField.value[0];
                  } else {
                    selectedText.textContent = `${subField.value.length} options selected`;
                  }
                };

                updateSelectedText();

                // Add dropdown arrow
                const dropdownArrow = document.createElement("span");
                dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

                dropdownHeader.appendChild(selectedText);
                dropdownHeader.appendChild(dropdownArrow);

                // Create dropdown content (initially hidden)
                const dropdownContent = document.createElement("div");
                dropdownContent.classList.add("dropdown-content");
                dropdownContent.style.display = "none";
                // Position the dropdown directly under the header
                dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
                dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
                dropdownContent.style.maxHeight = "200px";
                dropdownContent.style.overflowY = "auto";
                dropdownContent.style.overflowX = "hidden";
                dropdownContent.style.backgroundColor = "#fff";
                dropdownContent.style.border = "1px solid #ccc";
                dropdownContent.style.borderRadius = "4px";
                dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
                dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

                // Create a checkbox for each option
                subField.options.forEach((option) => {
                  const checkboxWrapper = document.createElement("div");
                  checkboxWrapper.classList.add("checkbox-wrapper");
                  checkboxWrapper.style.display = "flex";
                  checkboxWrapper.style.alignItems = "center";
                  checkboxWrapper.style.padding = "8px 12px";
                  checkboxWrapper.style.borderBottom = "1px solid #eee";
                  checkboxWrapper.style.textAlign = "left";
                  checkboxWrapper.style.cursor = "pointer";

                  const input = document.createElement("input");
                  input.type = "checkbox";
                  input.name = `${field}-${subKey}-${option.id}`;
                  input.value = option.id;
                  input.id = `${field}-${subKey}-${option.id}`;
                  input.style.marginRight = "4px"; // Add a small space between checkbox and text

                  // Check if this option is in the selected values array
                  input.checked = subField.value.includes(option.value);

                  input.addEventListener("change", (e) => {
                    // Get the current values array
                    let currentValues = Array.isArray(subField.value)
                      ? [...subField.value]
                      : [];

                    if (e.target.checked) {
                      // Add the value if it's not already in the array
                      if (!currentValues.includes(option.value)) {
                        currentValues.push(option.value);
                      }
                    } else {
                      // Remove the value if it's in the array
                      currentValues = currentValues.filter(
                        (val) => val !== option.value
                      );
                    }

                    // Update the model
                    fieldData[conditionalKey][subKey].value = currentValues;
                    fieldData[conditionalKey][subKey].modified_by =
                      "ABSTRACTOR";

                    // Update selected text display
                    updateSelectedText();

                    // Update UI
                    modifiedByDisplay.textContent = "ABSTRACTOR";
                    updateContainerHighlight();
                  });

                  const optionLabel = document.createElement("label");
                  optionLabel.setAttribute(
                    "for",
                    `${field}-${subKey}-${option.id}`
                  );
                  optionLabel.style.marginLeft = "0";
                  optionLabel.style.display = "inline-block";
                  optionLabel.style.whiteSpace = "nowrap";
                  optionLabel.style.cursor = "pointer";
                  optionLabel.style.flexGrow = "1";

                  // Display field_id in parentheses if available
                  if (option.field_id) {
                    optionLabel.innerText = `${option.value} (${option.field_id})`;
                  } else {
                    optionLabel.innerText = option.value;
                  }

                  // Create a wrapper for the checkbox and label to ensure they're tightly aligned
                  const inputLabelWrapper = document.createElement("div");
                  inputLabelWrapper.style.display = "flex";
                  inputLabelWrapper.style.alignItems = "center";
                  inputLabelWrapper.style.gap = "0";

                  inputLabelWrapper.appendChild(input);
                  inputLabelWrapper.appendChild(optionLabel);
                  checkboxWrapper.appendChild(inputLabelWrapper);
                  dropdownContent.appendChild(checkboxWrapper);
                });

                // Function to position the dropdown content properly
                const positionDropdown = () => {
                  const headerRect = dropdownHeader.getBoundingClientRect();
                  const viewportHeight = window.innerHeight;
                  const viewportWidth = window.innerWidth;

                  // Position dropdown below the header
                  dropdownContent.style.top = `${headerRect.bottom}px`;

                  // Ensure dropdown doesn't go off the right edge of the screen
                  const rightEdge = headerRect.left + 250; // 250px is our dropdown width
                  if (rightEdge > viewportWidth) {
                    // Align to the right edge of the header instead
                    dropdownContent.style.left = `${headerRect.right - 250}px`;
                  } else {
                    dropdownContent.style.left = `${headerRect.left}px`;
                  }

                  // Set max height based on available space
                  const spaceBelow = viewportHeight - headerRect.bottom;
                  const maxHeight = Math.max(
                    100,
                    Math.min(200, spaceBelow - 20)
                  );
                  dropdownContent.style.maxHeight = `${maxHeight}px`;
                };

                // Close dropdown when clicking outside
                document.addEventListener("click", (e) => {
                  if (
                    !dropdownContainer.contains(e.target) &&
                    !dropdownContent.contains(e.target)
                  ) {
                    dropdownContent.style.display = "none";
                    dropdownArrow.innerHTML = "&#9662;";
                  }
                });

                // Simple function to close the dropdown
                const closeDropdown = () => {
                  dropdownContent.style.display = "none";
                  dropdownArrow.innerHTML = "&#9662;";
                  window.removeEventListener("scroll", closeDropdown);
                };

                // Clean up event listener when the component is removed
                const cleanupFunc = () => {
                  if (document.body.contains(dropdownContent)) {
                    document.body.removeChild(dropdownContent);
                  }
                  // Remove scroll event listener
                  window.removeEventListener("scroll", closeDropdown);
                };

                // Store the cleanup function for potential future use
                dropdownContainer.cleanupFunc = cleanupFunc;

                // Add header to container, but add content to document body for better visibility
                dropdownContainer.appendChild(dropdownHeader);

                // Add the dropdown content to the document body when needed
                const showDropdown = () => {
                  if (!document.body.contains(dropdownContent)) {
                    document.body.appendChild(dropdownContent);
                  }
                  dropdownContent.style.display = "block";
                  positionDropdown();
                };

                // Set up the click handler for the dropdown
                dropdownHeader.onclick = (e) => {
                  e.stopPropagation();
                  const isOpen = dropdownContent.style.display === "block";

                  if (!isOpen) {
                    showDropdown();
                    dropdownArrow.innerHTML = "&#9652;"; // Up arrow
                    window.addEventListener("scroll", closeDropdown);
                  } else {
                    closeDropdown();
                  }
                };

                subFieldContainer.appendChild(dropdownContainer);
              }

              conditionalContentContainer.appendChild(subFieldContainer);
            }
          );
        }
      };

      // Create radio buttons for each option
      if (fieldData.options && Array.isArray(fieldData.options)) {
        fieldData.options.forEach((option) => {
          const radioWrapper = document.createElement("div");
          const input = document.createElement("input");
          input.type = "radio";
          input.name = field;
          input.value = option;
          input.id = `${field}-${option}`;
          input.checked = fieldData.value === option;

          // Update patientData and render conditional content when a radio is selected
          input.addEventListener("change", (e) => {
            if (e.target.checked) {
              patientData.demographics[field].value = e.target.value;
              patientData.demographics[field].modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, e.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight();

              // Render conditional content based on selected option
              renderConditionalContent(e.target.value);
            }
          });

          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${field}-${option}`);
          optionLabel.innerText = option;
          if (fieldData.description) {
            optionLabel.setAttribute("title", fieldData.description);
          }

          radioWrapper.appendChild(input);
          radioWrapper.appendChild(optionLabel);
          radioContainer.appendChild(radioWrapper);
        });
      }

      // Apply initial style based on the selected option value
      updateTileStyle(fieldContainer, fieldData.value || "");

      // Render initial conditional content if a value is already selected
      if (fieldData.value) {
        renderConditionalContent(fieldData.value);
      }

      // Add elements to container
      multiInputContainer.appendChild(radioContainer);
      multiInputContainer.appendChild(conditionalContentContainer);
      fieldContainer.appendChild(multiInputContainer);
    }

    // Create the modified_by display element.
    modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    if (!fieldData.modified_by) {
      fieldData.modified_by = "";
    }
    modifiedByDisplay.textContent = fieldData.modified_by;

    // Append field container and modified_by display
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);

    // Append the complete tile to the container.
    container.appendChild(tileWrapper);
  });

  // Render the "verified" checkbox separately at the bottom right of the section.
  // If the verified field is not an object, convert it so that we can update it.
  if (
    typeof patientData.demographics.verified !== "object" ||
    patientData.demographics.verified === null
  ) {
    patientData.demographics.verified = {
      value: patientData.demographics.verified || "False",
      modified_by: "",
    };
  }
  const verifiedData = patientData.demographics.verified;

  // Create a container for the verified checkbox with absolute positioning.
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";

  // Create the checkbox and set its initial checked state.
  const verifiedCheckbox = document.createElement("input");
  verifiedCheckbox.type = "checkbox";
  verifiedCheckbox.id = "verified-checkbox";
  verifiedCheckbox.checked = verifiedData.value === "True";
  // Make the checkbox bigger.
  verifiedCheckbox.style.width = "24px";
  verifiedCheckbox.style.height = "24px";

  verifiedCheckbox.addEventListener("change", (e) => {
    verifiedData.value = e.target.checked ? "True" : "False";
    updateContainerHighlight();
  });

  // Create label for the verified checkbox.
  const verifiedLabel = document.createElement("label");
  verifiedLabel.setAttribute("for", "verified-checkbox");
  verifiedLabel.classList.add("mt-2", "ml-2");
  verifiedLabel.innerText = "Verified";
  // Make the text bigger
  verifiedLabel.style.fontSize = "18px";
  verifiedLabel.style.fontWeight = "bold";

  // Append the checkbox first then the label.
  verifiedContainer.appendChild(verifiedCheckbox);
  verifiedContainer.appendChild(verifiedLabel);

  container.appendChild(verifiedContainer);

  // Render dependency fields as combined fields
  Object.entries(patientData.demographics).forEach(([fieldKey, value]) => {
    // Skip fields that don't have dependencies or are already rendered
    if (!value.dependency || !skipFields.has(fieldKey)) return;

    // Find the dependent field (N/A checkbox)
    const dependentFieldKey = Object.keys(patientData.demographics).find(
      (k) => patientData.demographics[k].field_id === value.dependency
    );

    if (!dependentFieldKey) return;

    const dependentField = patientData.demographics[dependentFieldKey];
    if (dependentField.input_type !== "checkbox") return;

    // Create an outer wrapper to hold the field tile and the modified_by display
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px";

    // Create the field container
    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    // Create and append the label element
    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = value.metric
      ? `${value.label} (${value.metric}) (${value.field_id})`
      : `${value.label} (${value.field_id})`;
    if (value.description) {
      label.setAttribute("title", value.description);
    }
    fieldContainer.appendChild(label);

    // Create the modified_by display element
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    if (!value.modified_by) {
      value.modified_by = "";
    }
    modifiedByDisplay.textContent = value.modified_by;

    // Create a wrapper for the input and checkbox to display them side by side
    const inputWrapper = document.createElement("div");
    inputWrapper.style.display = "flex";
    inputWrapper.style.alignItems = "center";
    inputWrapper.style.gap = "5px"; // Reduced gap from 10px to 5px

    // Create the appropriate input based on input_type
    let input;

    if (value.input_type === "select" && value.options) {
      // Create a select dropdown
      input = document.createElement("select");
      input.name = fieldKey;
      input.style.flexGrow = "1";

      // Add a default option
      if (!value.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        input.appendChild(defaultOption);
      }

      // Add options from the data
      value.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id;
        optionElement.innerText = option.value;
        if (value.value && value.value === option.value) {
          optionElement.selected = true;
        }
        input.appendChild(optionElement);
      });
    } else {
      // Create a text input for other types
      input = document.createElement("input");
      input.type = "text";
      input.name = fieldKey;
      input.placeholder = `Enter ${value.label}`;
      input.value = value.value || "";
      input.style.flexGrow = "1";
      input.style.minWidth = "200px"; // Ensure minimum width for placeholder visibility
    }

    // Create the N/A checkbox
    const checkboxWrapper = document.createElement("div");
    checkboxWrapper.style.display = "flex";
    checkboxWrapper.style.alignItems = "center";

    const checkbox = document.createElement("input");
    checkbox.type = "checkbox"; // Use checkbox to allow toggling
    checkbox.name = dependentFieldKey;
    checkbox.id = `${dependentFieldKey}-checkbox`;
    checkbox.checked = dependentField.value === "True";

    const checkboxLabel = document.createElement("label");
    checkboxLabel.setAttribute("for", `${dependentFieldKey}-checkbox`);
    checkboxLabel.innerText =
      `${dependentField.label} (${dependentField.field_id})` || "N/A";
    checkboxLabel.style.marginLeft = "5px";

    checkboxWrapper.appendChild(checkbox);
    checkboxWrapper.appendChild(checkboxLabel);

    // Create a wrapper for the input field
    const inputFieldWrapper = document.createElement("div");
    inputFieldWrapper.style.display = "flex";
    inputFieldWrapper.style.alignItems = "center";
    inputFieldWrapper.style.flexGrow = "1";
    inputFieldWrapper.style.minWidth = "250px"; // Ensure minimum width for the input field
    inputFieldWrapper.style.marginRight = "5px"; // Add some margin to the right
    inputFieldWrapper.appendChild(input);

    // Set initial state based on current values
    if (dependentField.value === "True") {
      input.disabled = true;
      checkbox.checked = true;
      if (value.input_type === "select") {
        // For select, reset to first option
        if (input.options.length > 0) {
          input.selectedIndex = 0;
        }
      } else {
        // For text input, clear the value
        input.value = "";
      }
      patientData.demographics[fieldKey].value = "";
    } else {
      input.disabled = false;
      checkbox.checked = false;
    }

    // Make sure the checkbox is not disabled
    checkbox.disabled = false;

    updateTileStyle(
      fieldContainer,
      value.value || (dependentField.value === "True" ? "True" : "")
    );

    // Add appropriate event listeners based on input type
    if (value.input_type === "select") {
      // For select fields
      input.addEventListener("change", (e) => {
        const selectedOption = value.options.find(
          (option) => option.id.toString() === e.target.value
        );
        const newValue = selectedOption ? selectedOption.value : "";

        // Update model and UI
        patientData.demographics[fieldKey].value = newValue;
        patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";

        // If select has value, disable the checkbox
        if (newValue) {
          checkbox.checked = false;
          patientData.demographics[dependentFieldKey].value = "";
        }

        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight();
      });
    } else {
      // For text input fields
      let previousValue = input.value;

      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use validation utilities with temporary callback
        let isValid = false;

        // Special handling for SSN field
        if (fieldKey === "ssn" && value.field_id === "2030") {
          parseSSNInput(currentValue, (validatedValue) => {
            isValid = true;
            previousValue = validatedValue;

            // Update model and UI only if value is valid
            patientData.demographics[fieldKey].value = validatedValue;
            patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";

            // If input has value, disable the checkbox
            if (validatedValue) {
              checkbox.checked = false;
              patientData.demographics[dependentFieldKey].value = "";
            }

            updateTileStyle(fieldContainer, validatedValue);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight();

            // Update input value if validation modified it
            if (validatedValue !== currentValue) {
              e.target.value = validatedValue;
            }
          });
        } else {
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update model and UI only if value is valid
              patientData.demographics[fieldKey].value = validatedValue;
              patientData.demographics[fieldKey].modified_by = "ABSTRACTOR";

              // If input has value, disable the checkbox
              if (validatedValue) {
                checkbox.checked = false;
                patientData.demographics[dependentFieldKey].value = "";
              }

              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight();

              // Update input value if validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );
        }

        // Revert to previous value if validation failed
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      // Also focus the input when clicking on it
      input.addEventListener("click", () => {
        checkbox.checked = false;
        patientData.demographics[dependentFieldKey].value = "";
      });
    }

    checkbox.addEventListener("change", (e) => {
      if (e.target.checked) {
        // Update the dependent field value
        patientData.demographics[dependentFieldKey].value = "True";
        patientData.demographics[dependentFieldKey].modified_by = "ABSTRACTOR";

        // Disable and clear the input
        input.disabled = true;
        if (value.input_type === "select") {
          // For select, reset to first option
          if (input.options.length > 0) {
            input.selectedIndex = 0;
          }
        } else {
          // For text input, clear the value
          input.value = "";
        }
        patientData.demographics[fieldKey].value = "";

        updateTileStyle(fieldContainer, "True");
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight();
      } else {
        // If checkbox is unchecked, enable the input
        input.disabled = false;
        patientData.demographics[dependentFieldKey].value = "";
        patientData.demographics[dependentFieldKey].modified_by = "ABSTRACTOR";
        updateTileStyle(
          fieldContainer,
          patientData.demographics[fieldKey].value || ""
        );
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight();
      }
    });

    // Append elements to the wrapper
    inputWrapper.appendChild(inputFieldWrapper);
    inputWrapper.appendChild(checkboxWrapper);
    fieldContainer.appendChild(inputWrapper);

    // Append field container and modified_by display to the tile wrapper
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);
    container.appendChild(tileWrapper);
  });

  // Render race field at the end if it exists
  if (raceContainer && patientData.demographics.race) {
  

    // Create an outer wrapper to hold the field tile and the modified_by display
    const raceTileWrapper = document.createElement("div");
    raceTileWrapper.classList.add("tile-wrapper");
    raceTileWrapper.style.position = "relative";
    raceTileWrapper.style.marginBottom = "24px";

    // Create the field container with highlight feature
    const raceFieldContainer = document.createElement("div");
    raceFieldContainer.classList.add("field-container");

    // Create and append the label element
    const raceLabel = document.createElement("label");
    raceLabel.classList.add("label");
    raceLabel.textContent = "Race (Select all that apply)";
    raceLabel.style.fontWeight = "bold";
    raceLabel.style.display = "block";
    raceLabel.style.marginBottom = "10px";
    raceFieldContainer.appendChild(raceLabel);

    // Create the modified_by display element
    const raceModifiedByDisplay = document.createElement("span");
    raceModifiedByDisplay.style.display = "block";
    raceModifiedByDisplay.style.textAlign = "right";
    raceModifiedByDisplay.style.marginTop = "-10px";
    raceModifiedByDisplay.style.color = "#8143d9";
    raceModifiedByDisplay.style.fontSize = "12px";

    // Check if any race option has been modified
    let raceModifiedBy = "";
    Object.values(patientData.demographics.race).forEach((raceValue) => {
      if (typeof raceValue === "object" && raceValue.modified_by) {
        raceModifiedBy = raceValue.modified_by;
      }
    });
    raceModifiedByDisplay.textContent = raceModifiedBy;

    // Create a container for the race checkboxes using grid layout
    const raceCheckboxesContainer = document.createElement("div");
    raceCheckboxesContainer.classList.add("race-checkboxes-container");
    raceCheckboxesContainer.style.display = "grid";
    raceCheckboxesContainer.style.gridTemplateColumns = "40px 1fr"; // Fixed width for checkbox column
    raceCheckboxesContainer.style.gap = "10px 10px"; // Row gap 10px, column gap 10px
    raceCheckboxesContainer.style.alignItems = "center"; // Center items vertically
    raceCheckboxesContainer.style.justifyItems = "start"; // Start-align items horizontally
    raceCheckboxesContainer.style.width = "100%"; // Take full width
    raceCheckboxesContainer.style.marginLeft = "0"; // No left margin
    raceCheckboxesContainer.style.paddingLeft = "0"; // No left padding

    // Get all race keys
    const raceKeys = Object.keys(patientData.demographics.race);
  

    // Check if race is empty or has invalid structure
    if (raceKeys.length === 0) {
     
      const noRaceMessage = document.createElement("p");
      noRaceMessage.textContent = "No race options available";
      raceCheckboxesContainer.appendChild(noRaceMessage);
    } else {
      // Render each race checkbox
      raceKeys.forEach((raceKey) => {
        const raceValue = patientData.demographics.race[raceKey];

     

       
      

        // Create the checkbox
        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.name = `race-${raceKey}`;
        checkbox.id = `race-${raceKey}`;
        checkbox.checked = raceValue.value === "True";
        checkbox.style.margin = "0"; // No margin
        checkbox.style.justifySelf = "center"; // Center in its grid cell
        checkbox.style.width = "20px"; // Fixed width for checkbox
        checkbox.style.height = "20px"; // Fixed height for checkbox

        // Create the label
        const label = document.createElement("label");
        label.setAttribute("for", `race-${raceKey}`);
        label.textContent = `${raceValue.label} (${raceValue.field_id})`;
        label.style.margin = "0"; // No margin
        label.style.textAlign = "left"; // Left-align text
        if (raceValue.description) {
          label.setAttribute("title", raceValue.description);
        }

        // Add event listener to update the data model
        checkbox.addEventListener("change", (e) => {
          patientData.demographics.race[raceKey].value = e.target.checked
            ? "True"
            : "";
          patientData.demographics.race[raceKey].modified_by = "ABSTRACTOR";

          // Update the modified_by display
          raceModifiedByDisplay.textContent = "ABSTRACTOR";

          // Update the highlight for the race field container
          updateTileStyle(raceFieldContainer, "True"); // Always highlight when modified

          updateContainerHighlight();
        });

        // Append checkbox and label directly to the grid container
        // Checkbox goes in the first column, label in the second
        raceCheckboxesContainer.appendChild(checkbox);
        raceCheckboxesContainer.appendChild(label);
      });
    }

    // Append the race checkboxes container to the race field container
    raceFieldContainer.appendChild(raceCheckboxesContainer);

    // Set initial highlight based on whether any race option is selected
    let anyRaceSelected = false;
    Object.values(patientData.demographics.race).forEach((raceValue) => {
      if (typeof raceValue === "object" && raceValue.value === "True") {
        anyRaceSelected = true;
      }
    });
    updateTileStyle(raceFieldContainer, anyRaceSelected ? "True" : "");

    // Append field container and modified_by display to the race tile wrapper
    raceTileWrapper.appendChild(raceFieldContainer);
    raceTileWrapper.appendChild(raceModifiedByDisplay);

    // Append the race tile wrapper to the main container
    container.appendChild(raceTileWrapper);
  }

  // Initial highlight update call in case conditions are already met on load.
  updateContainerHighlight();
}
