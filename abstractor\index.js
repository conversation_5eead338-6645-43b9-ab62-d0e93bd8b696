const urlParams = new URLSearchParams(window.location.search);
const caseId = urlParams.get("case_id");
const accessToken = localStorage.getItem("access_token");
let patientData = null;

// Smoothly scrolls to the specified section and applies a temporary highlight
function scrollToSection(sectionId) {
  const sectionElement = document.getElementById(sectionId);
  if (!sectionElement) {
    console.error("Section not found:", sectionId);
    return;
  }

  // Smooth scroll the section into view with alignment to center of the viewport
  sectionElement.scrollIntoView({
    behavior: "smooth",
    block: "center",
  });

  // Apply a temporary highlight effect
  sectionElement.style.transition = "background-color 0.5s ease-in-out";
  sectionElement.style.backgroundColor = "#f3f4f6";

  // Remove the highlight after a delay
  setTimeout(() => {
    sectionElement.style.backgroundColor = "";
  }, 1500);
}

// Toggles the accordion for a given form name and returns a Promise that resolves after UI updates have completed
function toggleAccordion(formName, forceClose = false, forceOpen = false) {
  return new Promise((resolve, reject) => {
    const content = document.getElementById(formName);
    if (!content) {
      console.error("Accordion section not found:", formName);
      reject();
      return;
    }

    // Get header element (assumed to be previous sibling)
    const header = content.previousElementSibling;
    if (!header) {
      console.error("Header not found for accordion section:", formName);
      reject();
      return;
    }

    // Get arrow element - we only need to update the arrow direction
    const arrow = header.querySelector(".arrow");
    if (!arrow) {
      console.warn("Arrow element not found in header for section:", formName);
    }

    // Toggle accordion open/close state
    const isOpen =
      content.style.display !== "none" && content.style.display !== "";

    // Check if this section should be kept open
    const keepOpen = content.dataset.keepOpen === "true";

    if (forceOpen) {
      // Force open the accordion
      console.log(`Forcing open accordion for section: ${formName}`);

      // Close all other accordions first
      document.querySelectorAll(".accordion-content").forEach((item) => {
        if (item.id !== formName) {
          item.style.display = "none";
          // Find the arrow in the header
          const itemHeader = item.previousElementSibling;
          const itemArrow = itemHeader?.querySelector(".arrow");
          if (itemArrow) itemArrow.textContent = "▼";

          // Remove keepOpen flag from other accordions
          if (item.dataset.keepOpen) {
            delete item.dataset.keepOpen;
          }
        }
      });

      // Open this accordion
      content.style.display = content.dataset.gridDisplay || "grid";
      if (arrow) arrow.textContent = "▲";
      setTimeout(resolve, 300);
    } else if ((isOpen && !keepOpen) || forceClose) {
      // Close the accordion only if it's not marked to be kept open
      content.style.display = "none";
      if (arrow) arrow.textContent = "▼";
      setTimeout(resolve, 300);
    } else {
      // Close all other accordions
      document.querySelectorAll(".accordion-content").forEach((item) => {
        if (item.id !== formName) {
          // Only close other accordions, not the current one
          item.style.display = "none";
          // Find the arrow in the header, which might be inside a wrapper div
          const itemHeader = item.previousElementSibling;
          const itemArrow = itemHeader.querySelector(".arrow");
          if (itemArrow) itemArrow.textContent = "▼";

          // Remove keepOpen flag from other accordions
          if (item.dataset.keepOpen) {
            delete item.dataset.keepOpen;
          }
        }
      });

      // Open the target accordion
      // Use the stored grid display value if available, otherwise default to "grid"
      content.style.display = content.dataset.gridDisplay || "grid";
      if (arrow) arrow.textContent = "▲";
      setTimeout(resolve, 300);
    }
  });
}

// Initialize accordions and add event listeners
document.addEventListener("DOMContentLoaded", () => {
  // Close all accordions by default
  document.querySelectorAll(".accordion-content").forEach((content) => {
    content.style.display = "none";
    const header = content.previousElementSibling;
    const arrow = header?.querySelector(".arrow");
    if (arrow) arrow.textContent = "▼";
  });

  // Add click event listeners to accordion headers
  document.querySelectorAll(".accordion-header").forEach((header) => {
    header.addEventListener("click", () => {
      const formName = header.getAttribute("data-form");
      toggleAccordion(formName);
    });
  });
});

// Recursively checks for the section element and performs the accordion toggle and smooth scroll
function waitForSectionAndScroll(sectionId, retries = 10) {
  const sectionElement = document.getElementById(sectionId);

  if (sectionElement) {
    // Mark this section to be kept open
    sectionElement.dataset.keepOpen = "true";

    // Force the section to be displayed
    sectionElement.style.display = sectionElement.dataset.gridDisplay || "grid";

    // Update the arrow in the header
    const header = sectionElement.previousElementSibling;

    const arrow = header?.querySelector(".arrow");
    if (arrow) {
      arrow.textContent = "▲";
    } else {
      console.warn(`Arrow element not found for section: ${sectionId}`);
    }

    // Toggle the accordion (which will now respect the keepOpen flag)

    toggleAccordion(sectionId, false, true) // Added forceOpen parameter
      .then(() => {
        // Scroll to the section after a short delay to ensure it's fully visible
        setTimeout(() => {
          scrollToSection(sectionId);

          // Double-check that the section is still open after scrolling
          setTimeout(() => {
            const element = document.getElementById(sectionId);
            if (element && element.style.display === "none") {
              element.style.display = element.dataset.gridDisplay || "grid";

              // Update the arrow again
              const header = element.previousElementSibling;
              const arrow = header?.querySelector(".arrow");
              if (arrow) arrow.textContent = "▲";
            }
          }, 500);
        }, 300);
      })
      .catch((error) => {
        console.error(
          `Failed to toggle accordion for section: ${sectionId}`,
          error
        );
      });
    return;
  } else {
    console.warn(`Section element not found: ${sectionId}`);
  }

  if (retries > 0) {
    setTimeout(() => waitForSectionAndScroll(sectionId, retries - 1), 500);
  } else {
    console.warn("Section not found after multiple attempts:", sectionId);
  }
}

// Back button creation with SVG icon for navigation
const backButton = document.createElement("button");
backButton.classList.add(
  "mt-4",
  "ml-4",
  "flex",
  "items-center",
  "gap-2",
  "text-[#8143d9]",
  "p-2",
  "rounded",
  "px-2.5",
  "py-1",
  "mb-5"
);
backButton.style.position = "absolute";
backButton.style.top = "10px";
backButton.style.left = "10px";
backButton.style.zIndex = "9999";

// Create SVG icon container
const svgIcon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
svgIcon.setAttribute("xmlns", "http://www.w3.org/2000/svg");
svgIcon.setAttribute("fill", "none");
svgIcon.setAttribute("viewBox", "0 0 24 24");
svgIcon.setAttribute("stroke-width", "2");
svgIcon.classList.add("w-6", "h-6");

// Create circle element in the SVG
const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
circle.setAttribute("cx", "12");
circle.setAttribute("cy", "12");
circle.setAttribute("r", "10");
circle.setAttribute("stroke", "#8143d9");
circle.setAttribute("stroke-width", "2");
circle.setAttribute("fill", "white");

// Create the arrow element in the SVG
const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
path.setAttribute("stroke", "#8143d9");
path.setAttribute("stroke-linecap", "round");
path.setAttribute("stroke-linejoin", "round");
path.setAttribute("d", "M14 16l-4-4m0 0l4-4");

// Assemble SVG icon
svgIcon.appendChild(circle);
svgIcon.appendChild(path);

// Create the "Back" label
const backText = document.createElement("span");
backText.textContent = "Back";

// Append icon and text to the button
backButton.appendChild(svgIcon);
backButton.appendChild(backText);
backButton.onclick = () => window.history.back();

function ensureVerificationStatus(maxAttempts = 10, interval = 500) {
  let attempts = 0;

  function tryAddingStatus() {
    const headers = document.querySelectorAll(".accordion-title");

    if (headers.length > 0) {
      addVerificationStatusToAllHeaders();
      return true;
    }

    attempts++;
    if (attempts < maxAttempts) {
      setTimeout(tryAddingStatus, interval);
      return false;
    }

    console.warn(
      `Failed to find accordion headers after ${maxAttempts} attempts`
    );
    return false;
  }

  return tryAddingStatus();
}

// Show/hide the HTML back to top button based on scroll position
window.addEventListener("scroll", () => {
  const backToTopBtn = document.getElementById("back-to-top-container");
  if (backToTopBtn) {
    if (window.scrollY > 300) {
      backToTopBtn.style.display = "flex";
    } else {
      backToTopBtn.style.display = "none";
    }
  }
});

// Append the back button to the body when DOM is fully loaded
document.addEventListener("DOMContentLoaded", () => {
  document.body.appendChild(backButton);

  // Initialize all accordions to be closed
  const allContents = document.querySelectorAll(".accordion-content");
  allContents.forEach((item) => {
    // Force close all accordions, including preProcedureMedications
    item.style.display = "none";
    const arrow = item.previousElementSibling.querySelector(".arrow");
    if (arrow) arrow.textContent = "▼";
  });

  // Specifically check and close preProcedureMedications if it's open
  const preProcedureMedications = document.getElementById(
    "preProcedureMedications"
  );
  if (preProcedureMedications) {
    preProcedureMedications.style.display = "none";
    const header = preProcedureMedications.previousElementSibling;
    const arrow = header?.querySelector(".arrow");
    if (arrow) arrow.textContent = "▼";
  }

  // Extract section_id from URL params only
  const sectionId = urlParams.get("section_id");
  if (sectionId) {
    // Add a slight delay to ensure DOM is fully processed
    setTimeout(() => {
      // First, try to directly open the section
      const sectionElement = document.getElementById(sectionId);
      if (sectionElement) {
        sectionElement.style.display =
          sectionElement.dataset.gridDisplay || "grid";
        sectionElement.dataset.keepOpen = "true";

        // Update the arrow
        const header = sectionElement.previousElementSibling;
        const arrow = header?.querySelector(".arrow");
        if (arrow) arrow.textContent = "▲";
      }

      // Then use the waitForSectionAndScroll function for complete handling
      waitForSectionAndScroll(sectionId);

      // Add a final check after everything else has run
      setTimeout(() => {
        const finalCheck = document.getElementById(sectionId);
        if (finalCheck && finalCheck.style.display === "none") {
          finalCheck.style.display = finalCheck.dataset.gridDisplay || "grid";

          // Update the arrow again
          const header = finalCheck.previousElementSibling;
          const arrow = header?.querySelector(".arrow");
          if (arrow) arrow.textContent = "▲";

          // Scroll to the section
          scrollToSection(sectionId);
        }

        // Set up a MutationObserver to keep the section open
        if (finalCheck) {
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (
                mutation.type === "attributes" &&
                mutation.attributeName === "style"
              ) {
                if (finalCheck.style.display === "none") {
                  finalCheck.style.display =
                    finalCheck.dataset.gridDisplay || "grid";

                  // Update the arrow
                  const header = finalCheck.previousElementSibling;
                  const arrow = header?.querySelector(".arrow");
                  if (arrow) arrow.textContent = "▲";
                }
              }
            });
          });

          // Start observing the section for style changes
          observer.observe(finalCheck, {
            attributes: true,
            attributeFilter: ["style"],
          });
        }
      }, 1000);
    }, 500);
  }

  // Add verification status to all headers once DOM is loaded and patient data is available
  if (patientData) {
    ensureVerificationStatus(20, 300);
  } else {
    // If patient data isn't available yet, set up an interval to check
    const verificationStatusInterval = setInterval(() => {
      if (patientData) {
        ensureVerificationStatus(20, 300);
        clearInterval(verificationStatusInterval);
      }
    }, 200);

    // Clear interval after 10 seconds to prevent infinite checking
    setTimeout(() => clearInterval(verificationStatusInterval), 10000);
  }
});

// Fetch patient data from API and handle dynamic module imports
function fetchPatientData() {
  const apiUrl = `${config.apiUrl}/abstractor/patients/${caseId}`;
  const headers = {
    Accept: "application/json",
    Authorization: `Bearer ${accessToken}`,
  };

  return fetch(apiUrl, { headers })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then((data) => {
      if (data && data.result && data.result.length > 0) {
        patientData = data.result[0];
        console.log("[FETCH] Patient data:", patientData);

        // Log specific sections to verify structure
        if (
          patientData.follow_up_medications &&
          patientData.follow_up_medications.medications
        ) {
        } else {
          console.warn(
            "[FETCH] Follow-up medications structure is not as expected:",
            patientData.follow_up_medications
          );
        }
      } else {
        console.warn("[FETCH] Unexpected data structure:", data);
      }

      return data;
    })
    .catch((error) => {
      console.error("Error fetching patient data:", error);
    });
}

// Function to add verification status to all accordion headers
function addVerificationStatusToAllHeaders() {
  if (!patientData) {
    console.error("No patient data available for verification status");
    return;
  }

  // Define mapping from formName to API response keys
  const apiMapping = {
    additionalHistoryRiskFactors: "additional_history_and_risk_factors",
    additionalStrokeAndBleedingRiskFactors:
      "additional_stroke_and_bleeding_risk_factors",
    diagnosticStudies: "diagnostic_studies",
    discharge: "discharge",
    dischargeMedications: "discharge_medications",
    epicardialAccessAssessment: "epicardial_access_assessment",
    episodeOfCare: "episode_of_care_admission",
    followUp: "follow_up",
    followUpDiagnosticStudies: "follow_up_diagnostic_studies",
    followUpPhysicalExamAndLabs: "follow_up_physical_exam_and_labs",
    followUpAdjudication: "follow_up_adjudication",
    followUpBarthelIndexEvaluation: "follow_up_barthel_index_evaluation",
    followUpAnticoagulationTherapy: "follow_up_anticoagulation_therapy",
    followUpEvents: "follow_up_events",
    followUpMedications: "follow_up_medications",
    historyAndRiskFactors: "history_and_risk_factors",
    historyInterventions: "history_interventions",
    historyRhythmHistory: "history_rhythm_history",
    inHospitalAdjudication: "in_hospital_adjudication",
    intraPostProcedureEvents: "intra_or_post_procedure_events",
    radiationExposure: "radiation_exposure",
    intraProcedureAnticoagulationStrategy:
      "intraprocedure_anticoagulation_strategy",
    patientDemographics: "demographics",
    physicalExamAndLabs: "physical_exam_and_labs",
    postProcedureLabs: "post_procedure_labs",
    preProcedureDiagnostics: "pre_procedure_diagnostics",
    preProcedureMedications: "pre_procedure_medications",
    procedureInfo: "procedureInfo",
    systemicThromboembolism: "systemic_thromboembolism",
  };

  // Get all accordion headers - they have class "accordion-title" in the HTML
  const headers = document.querySelectorAll(".accordion-title");

  headers.forEach((header) => {
    // Extract form name from the onclick attribute
    const onclickAttr = header.getAttribute("onclick") || "";
    const formNameMatch = onclickAttr.match(/toggleAccordion\(['"](.+)['"]\)/);
    const formName = formNameMatch ? formNameMatch[1] : null;

    // Get API key from mapping
    const apiKey = apiMapping[formName] || formName;

    // Check if the API key exists in patientData
    if (!patientData[apiKey]) {
      console.error(`API key ${apiKey} not found in patientData`);
      return;
    }

    // Check if the verified field exists
    if (apiKey === "intra_or_post_procedure_events") {
      if (!patientData[apiKey].events?.verified) {
        console.warn(`Verified field not found for ${apiKey}.events`);
      } else {
      }
    } else if (!patientData[apiKey].verified) {
      console.warn(`Verified field not found for ${apiKey}`);
    }

    // Get or create wrapper div for status and arrow
    let wrapperDiv = header.querySelector(".status-arrow-wrapper");
    if (!wrapperDiv) {
      wrapperDiv = document.createElement("div");
      wrapperDiv.classList.add(
        "status-arrow-wrapper",
        "flex",
        "items-center",
        "gap-4"
      );
      wrapperDiv.style.marginLeft = "auto"; // Push to right side
      wrapperDiv.style.display = "flex";
      wrapperDiv.style.alignItems = "center";
      wrapperDiv.style.justifyContent = "space-between"; // Changed from flex-end to space-between
      wrapperDiv.style.width = "190px";
    }

    // Remove any existing verification status
    const existingStatus = wrapperDiv.querySelector(".verification-status");
    if (existingStatus) existingStatus.remove();

    // Create a new verification status span
    const verifiedSpan = document.createElement("span");
    verifiedSpan.classList.add("verification-status");
    verifiedSpan.style.fontWeight = "bold";
    verifiedSpan.style.padding = "4px 8px";
    verifiedSpan.style.borderRadius = "4px";
    verifiedSpan.style.fontSize = "0.85rem";
    verifiedSpan.style.display = "inline-block";
    verifiedSpan.style.minWidth = "90px"; // Set a minimum width for consistent alignment
    verifiedSpan.style.marginRight = "0"; // Remove right margin

    // Determine verification status
    let isVerified = false;

    // Special case for intra_or_post_procedure_events where verified is nested under events
    if (apiKey === "intra_or_post_procedure_events") {
      isVerified = patientData[apiKey]?.events?.verified?.value === "True";
    } else {
      isVerified = patientData[apiKey]?.verified?.value === "True";
    }

    // Set styles and text based on verification status
    verifiedSpan.textContent = isVerified ? "Verified" : "Not Verified";
    verifiedSpan.style.color = isVerified ? "#006600" : "#cc0000";
    verifiedSpan.style.backgroundColor = isVerified ? "#ccffcc" : "#ffcccc";
    verifiedSpan.style.boxShadow = "0 1px 2px rgba(0, 0, 0, 0.1)";
    verifiedSpan.style.textAlign = "center";

    // Get existing arrow or create a new one
    let arrow = header.querySelector(".arrow");

    // If we're restructuring, remove the arrow from its current position
    if (arrow && !wrapperDiv.contains(arrow)) {
      arrow.remove();
    }

    // If arrow doesn't exist or was removed, create a new one
    if (!arrow || !wrapperDiv.contains(arrow)) {
      arrow = document.createElement("span");
      arrow.classList.add("arrow");
      arrow.textContent = "▼";
      arrow.style.fontSize = "1rem";
      arrow.style.fontWeight = "bold";
      arrow.style.padding = "4px";
      arrow.style.display = "flex";
      arrow.style.alignItems = "center";
      arrow.style.justifyContent = "center";
      arrow.style.width = "24px";
      arrow.style.height = "24px";
      arrow.style.borderRadius = "50%";
      arrow.style.backgroundColor = "#f0f0f0";
    }

    // Clear the wrapper div to ensure proper order
    wrapperDiv.innerHTML = "";

    // Add verification status first (on the left)
    wrapperDiv.appendChild(verifiedSpan);

    // Add arrow second (on the right)
    arrow.style.marginLeft = "auto"; // Push arrow to the far right
    wrapperDiv.appendChild(arrow);

    // Add wrapper to header if needed
    if (!header.contains(wrapperDiv)) {
      header.appendChild(wrapperDiv);
    }
  });
}

// This function has been removed and its functionality integrated into loadAllModules

// Add a loading indicator to the page
function showLoadingIndicator() {
  // Check if loading indicator already exists
  if (document.getElementById("loading-indicator")) {
    return;
  }

  // Create loading indicator container
  const loadingContainer = document.createElement("div");
  loadingContainer.id = "loading-indicator";
  loadingContainer.style.position = "fixed";
  loadingContainer.style.top = "0";
  loadingContainer.style.left = "0";
  loadingContainer.style.width = "100%";
  loadingContainer.style.height = "100%";
  loadingContainer.style.backgroundColor = "rgba(255, 255, 255, 0.7)";
  loadingContainer.style.display = "flex";
  loadingContainer.style.justifyContent = "center";
  loadingContainer.style.alignItems = "center";
  loadingContainer.style.zIndex = "10000"; // Higher z-index to appear in front of forms
  loadingContainer.style.opacity = "0"; // Start with opacity 0 for fade-in effect
  loadingContainer.style.transition = "opacity 0.3s ease"; // Add transition for smooth fade-in

  // Create spinner
  const spinner = document.createElement("div");
  spinner.style.border = "5px solid #f3f3f3";
  spinner.style.borderTop = "5px solid #8143d9";
  spinner.style.borderRadius = "50%";
  spinner.style.width = "60px"; // Slightly larger spinner
  spinner.style.height = "60px";
  spinner.style.animation = "spin 1s linear infinite";
  spinner.style.boxShadow = "0 0 10px rgba(0, 0, 0, 0.1)";

  // Add animation keyframes
  const style = document.createElement("style");
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
  // Assemble and add to document
  loadingContainer.appendChild(spinner);
  document.body.appendChild(loadingContainer);

  // Trigger fade-in effect after a small delay to ensure the transition works
  setTimeout(() => {
    loadingContainer.style.opacity = "1";
  }, 10);
}

// Remove the loading indicator
function hideLoadingIndicator() {
  const loadingIndicator = document.getElementById("loading-indicator");
  if (loadingIndicator) {
    // Add transition property if not already set
    if (!loadingIndicator.style.transition) {
      loadingIndicator.style.transition = "opacity 0.5s ease";
    }

    // Fade out
    loadingIndicator.style.opacity = "0";

    // Remove after transition completes
    setTimeout(() => {
      loadingIndicator.remove();
    }, 500);
  }
}

// Function to load all modules and ensure verification status is displayed
function loadAllModules(data) {
  if (!data) {
    console.warn("[MODULES] No data provided to loadAllModules");
    return;
  }

  // Show loading indicator
  showLoadingIndicator();

  // Create an array of all module import promises
  const modulePromises = [
    import("./js/patientDemographics.js").then((module) =>
      module.renderPatientDemographics(data.result[0])
    ),
    import("./js/episodeOfCare.js").then((module) =>
      module.renderEpisodeOfCare(data.result[0])
    ),
    import("./js/additionalHistoryRiskFactors.js").then((module) =>
      module.renderAdditionalHistoryRiskFactors(data.result[0])
    ),
    import("./js/additionalStrokeAndBleedingRiskFactors.js").then((module) =>
      module.renderAdditionalStrokeAndBleedingRiskFactors(data.result[0])
    ),
    import("./js/diagnosticStudies.js").then((module) =>
      module.renderDiagnosticStudies(data.result[0])
    ),
    import("./js/discharge.js").then((module) =>
      module.renderDischarge(data.result[0])
    ),
    import("./js/dischargeMedication.js").then((module) =>
      module.renderDischargeMedication(data.result[0])
    ),
    import("./js/epicardialAccessAssessment.js").then((module) =>
      module.renderEpicardialAccessAssessment(data.result[0])
    ),
    import("./js/followUp.js").then((module) =>
      module.renderFollowUp(data.result[0])
    ),
    import("./js/followUpAdjudication.js").then((module) =>
      module.renderFollowUpAdjudication(data.result[0])
    ),
    import("./js/followUpAnticoagulationTherapy.js").then((module) =>
      module.renderFollowUpAnticoagulationTherapy(data.result[0])
    ),
    import("./js/followUpDiagnosticStudies.js").then((module) =>
      module.renderFollowUpDiagnosticStudies(data.result[0])
    ),
    import("./js/followUpBarthelIndexEvaluation.js").then((module) =>
      module.renderFollowUpBarthelIndexEvaluation(data.result[0])
    ),
    import("./js/followUpPhysicalExamAndLabs.js").then((module) =>
      module.renderFollowUpPhysicalExamAndLabs(data.result[0])
    ),
    import("./js/followUpEvents.js").then((module) =>
      module.renderFollowUpEvents(data.result[0])
    ),
    import("./js/followUpMedication.js").then((module) =>
      module.renderFollowUpMedication(data.result[0])
    ),
    import("./js/historyAndRiskFactors.js").then((module) =>
      module.renderHistoryAndRiskFactors(data.result[0])
    ),
    import("./js/historyInterventions.js").then((module) =>
      module.renderHistoryInterventions(data.result[0])
    ),
    import("./js/historyRhythmHistory.js").then((module) =>
      module.renderHistoryRhythmHistory(data.result[0])
    ),
    import("./js/inHospitalAdjudication.js").then((module) =>
      module.renderInHospitalAdjudication(data.result[0])
    ),
    import("./js/intraPostProcedureEvents.js").then((module) =>
      module.renderIntraPostProcedureEvents(data.result[0])
    ),
    import("./js/radiationExposure.js").then((module) =>
      module.renderRadiationExposure(data.result[0])
    ),
    import("./js/intraProcedureAnticoagulationStrategy.js").then((module) =>
      module.renderIntraProcedureAnticoagulationStrategy(data.result[0])
    ),
    import("./js/physicalExamAndLabs.js").then((module) =>
      module.renderPhysicalExamAndLabs(data.result[0])
    ),
    import("./js/postProcedureLabs.js").then((module) =>
      module.renderPostProcedureLabs(data.result[0])
    ),
    import("./js/preProcedureMedications.js").then((module) =>
      module.renderPreProcedureMedications(data.result[0])
    ),
    import("./js/procedureInfo.js").then((module) =>
      module.renderProcedureInfo(data.result[0])
    ),
  ];

  // Wait for all modules to load, then add verification status
  Promise.all(modulePromises)
    .then(() => {
      console.log("All modules loaded successfully");

      // Ensure all accordions are closed after modules are loaded
      const allContents = document.querySelectorAll(".accordion-content");
      allContents.forEach((item) => {
        // Force close all accordions, including preProcedureMedications
        item.style.display = "none";
        const arrow = item.previousElementSibling.querySelector(".arrow");
        if (arrow) arrow.textContent = "▼";
      });

      // Specifically check and close preProcedureMedications if it's open
      const preProcedureMedications = document.getElementById(
        "preProcedureMedications"
      );
      if (preProcedureMedications) {
        preProcedureMedications.style.display = "none";
        const header = preProcedureMedications.previousElementSibling;
        const arrow = header?.querySelector(".arrow");
        if (arrow) arrow.textContent = "▼";
      }

      // Set up a MutationObserver to detect when DOM elements are added
      const observer = new MutationObserver(() => {
        const headers = document.querySelectorAll(".accordion-title");
        if (headers.length > 0) {
          // Add verification status to all headers
          addVerificationStatusToAllHeaders();

          // Hide loading indicator after verification status is added
          hideLoadingIndicator();

          // Disconnect observer once verification status is added
          observer.disconnect();
        }
      });

      // Start observing the document
      observer.observe(document.body, { childList: true, subtree: true });

      // Try to add verification status immediately in case headers are already available
      const headers = document.querySelectorAll(".accordion-title");
      if (headers.length > 0) {
        addVerificationStatusToAllHeaders();
        hideLoadingIndicator();
        observer.disconnect();
      }

      // Set a timeout to hide the loading indicator after 3 seconds even if something goes wrong
      setTimeout(() => {
        hideLoadingIndicator();
        observer.disconnect();
      }, 3000);
    })
    .catch((error) => {
      console.error("Error loading modules:", error);
      hideLoadingIndicator();
    });
}

// Create modal elements if they don't exist
function createModalIfNeeded() {
  if (!document.getElementById("modal")) {
    // Create modal container
    const modal = document.createElement("div");
    modal.id = "modal";
    modal.className =
      "fixed inset-0 z-50 flex items-center justify-center hidden";
    modal.style.backgroundColor = "rgba(0, 0, 0, 0.5)";

    // Create modal content
    const modalContent = document.createElement("div");
    modalContent.id = "modal-content";
    modalContent.className =
      "bg-white rounded-lg p-6 max-w-md mx-auto shadow-xl transform transition-all";

    // Create modal message
    const modalMessage = document.createElement("p");
    modalMessage.id = "modal-message";
    modalMessage.className = "text-lg text-center mb-4";

    // Create close button
    const closeButton = document.createElement("button");
    closeButton.id = "modal-close";
    closeButton.className =
      "mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 mx-auto block";
    closeButton.textContent = "Close";

    // Assemble modal
    modalContent.appendChild(modalMessage);
    modalContent.appendChild(closeButton);
    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Add modal styles
    const modalStyles = document.createElement("style");
    modalStyles.textContent = `
      #modal.show {
        display: flex;
        opacity: 1;
      }
      #modal {
        transition: opacity 0.3s ease;
      }
      #modal-content {
        transition: transform 0.3s ease;
      }
      #modal-content.show {
        transform: scale(1);
      }
      #modal-content.scale-90 {
        transform: scale(0.9);
      }
    `;
    document.head.appendChild(modalStyles);
  }
}

// Show loading indicator immediately when the script runs
showLoadingIndicator();

// Create modal elements
createModalIfNeeded();

fetchPatientData()
  .then((data) => {
    if (data) {
      loadAllModules(data);
    } else {
      console.warn("[MAIN] No data returned from fetchPatientData");
      // Hide loading indicator if no data was returned
      hideLoadingIndicator();
    }
  })
  .catch((error) => {
    console.error("Error fetching patient data:", error);
    // Hide loading indicator if there was an error
    hideLoadingIndicator();
  });

// Event listener for saving patient data
document
  .getElementById("saveButton")
  .addEventListener("click", savePatientData);

// Event listener for cancelling patient data modifications
document
  .getElementById("cancelButton")
  .addEventListener("click", cancelPatientData);

// Reload the page to cancel edits
function cancelPatientData() {
  location.reload();
}

function showModal(message, duration = null) {
  const modal = document.getElementById("modal");
  const modalContent = document.getElementById("modal-content");
  const modalMessage = document.getElementById("modal-message");
  const closeButton = document.getElementById("modal-close");

  if (!modal || !modalContent || !modalMessage) {
    console.error("Modal elements not found in the DOM");
    return;
  }

  // Remove hidden class first
  modal.classList.remove("hidden");

  // Set the message
  modalMessage.textContent = message;

  // Force a reflow to ensure transitions work properly
  void modal.offsetWidth;

  // Add show classes for animation
  modal.classList.add("show", "opacity-100");
  modalContent.classList.add("show", "scale-100");

  // Remove any lingering animation classes
  modal.classList.remove("opacity-0");
  modalContent.classList.remove("scale-90");

  // Set up event handlers
  closeButton.onclick = hideModal;
  modal.onclick = (event) => {
    if (event.target === modal) {
      hideModal();
    }
  };

  // Auto-hide after duration if specified
  if (duration) {
    setTimeout(() => {
      hideModal();
    }, duration);
  }
}

// Hide the modal with animation effects
function hideModal() {
  const modal = document.getElementById("modal");
  const modalContent = document.getElementById("modal-content");

  // First remove the show classes
  modal.classList.remove("show");
  modalContent.classList.remove("show");

  // Add animation classes
  modal.classList.add("opacity-0");
  modalContent.classList.add("scale-90");

  // Wait for the transition to complete before hiding the modal
  setTimeout(() => {
    modal.classList.add("hidden");
    modal.classList.remove("opacity-0", "opacity-100", "scale-100");
  }, 500); // Match the duration of the CSS transition
}

// Save patient data to the backend API
function savePatientData() {
  console.log("[SAVE] Preparing to save patient data...");

  if (!patientData) {
    console.error("[SAVE] No patient data available to save!");
    showModal("No data available to save.");
    return;
  }

  console.dir(patientData, { depth: null });

  // Show loading indicator immediately
  showLoadingIndicator();

  // Get the save button and change its text to "Saving..."
  const saveButton = document.getElementById("saveButton");
  const originalButtonText = saveButton.textContent;
  saveButton.textContent = "Saving...";
  saveButton.disabled = true; // Disable the button while saving

  const apiUrl = `${config.apiUrl}/abstractor/patients/${caseId}`;
  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`,
  };

  fetch(apiUrl, {
    method: "PUT",
    headers,
    body: JSON.stringify(patientData),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then((_) => {
      // Use underscore to indicate unused parameter
      // Hide loading indicator
      hideLoadingIndicator();

      // Restore the save button text and enable it
      saveButton.textContent = originalButtonText;
      saveButton.disabled = false;

      showModal("Patient data saved successfully.", 3000);

      // Just show the success message and reload the page after a short delay
      // No immediate verification status update
      setTimeout(() => {
        // Show loading indicator before reload
        showLoadingIndicator();
        location.reload();
      }, 2000);
    })
    .catch((error) => {
      console.error("Error saving patient data:", error);

      // Hide loading indicator
      hideLoadingIndicator();

      // Restore the save button text and enable it even if there's an error
      saveButton.textContent = originalButtonText;
      saveButton.disabled = false;

      showModal("Failed to save patient data. Please try again.");
    });
}

// Add CSS styles for chatbot transitions
const chatbotStyles = document.createElement("style");
chatbotStyles.textContent = `
  .chatbot-transition {
    transition: bottom 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }
`;
document.head.appendChild(chatbotStyles);

function openChatbot() {
  const chatbotModal = document.getElementById("chatbot-modal");
  const chatbotIcon = document.getElementById("chatbot-icon");

  // Add transition class and remove hidden class
  chatbotModal.classList.add("chatbot-transition");
  chatbotModal.classList.remove("hidden");

  // Set initial position before transition
  chatbotModal.style.opacity = "0";
  chatbotModal.style.bottom = "-400px";

  // Force a reflow to ensure the initial state is applied
  void chatbotModal.offsetWidth;

  // Trigger the transition
  chatbotModal.style.opacity = "1";
  chatbotModal.style.bottom = "100px";

  // Change icon
  chatbotIcon.classList.remove("fa-robot");
  chatbotIcon.classList.add("fa-times");
}

function closeChatbotOnIcon() {
  const chatbotModal = document.getElementById("chatbot-modal");
  const chatbotIcon = document.getElementById("chatbot-icon");

  // Make sure transition class is applied
  chatbotModal.classList.add("chatbot-transition");

  // Trigger the transition
  chatbotModal.style.opacity = "0";
  chatbotModal.style.bottom = "-400px";

  // Hide after transition completes
  setTimeout(() => {
    chatbotModal.classList.add("hidden");
  }, 300);

  // Change icon
  chatbotIcon.classList.remove("fa-times");
  chatbotIcon.classList.add("fa-robot");
}

function toggleChatbot() {
  const chatbotModal = document.getElementById("chatbot-modal");

  if (chatbotModal.classList.contains("hidden")) {
    openChatbot();
  } else {
    closeChatbotOnIcon();
  }
}
