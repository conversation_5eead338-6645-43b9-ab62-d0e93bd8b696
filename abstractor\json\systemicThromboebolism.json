{"systemic_thromboembolism": {"adjudication_status": {"field_id": "14932", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": ""}, "adjudication_date_of_death": {"field_id": "14933", "label": "Adjudication Date of Death", "format": "mm/dd/yyyy", "input_type": "string", "value": ""}, "death_cause": {"field_id": "14934", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "focal_end_organ_hypoperfusion_present": {"field_id": "14935", "label": "Focal End-Organ Hypoperfusion Present", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "systemic_thromboembolization_imaging_evidence": {"field_id": "14939", "label": "Systemic Thromboembolization Imaging Evidence", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "imaging_method": {"field_id": "14936", "label": "Imaging Method", "options": [{"id": "", "value": "Angiography"}, {"id": "", "value": "Computed Tomography"}, {"id": "", "value": "Magnetic Resonance Imaging"}, {"id": "", "value": "Ultrasound"}, {"id": "", "value": "Other Imaging"}], "input_type": "select", "value": "id"}, "therapeutic_intervention_performed": {"field_id": "14937", "label": "Therapeutic Intervention Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "intervention_type": {"field_id": "14938", "label": "Intervention Type", "options": [{"id": "", "value": "Catheter"}, {"id": "", "value": "Pharmacological"}, {"id": "", "value": "Surgical"}, {"id": "", "value": "Other"}], "input_type": "select", "value": "id"}, "current_medications_at_time_of_event": {"field_id": "14941", "label": "Current Medications at Time of Event", "medications": {"fondaparinux": {"field_id": "14940", "options": ["Yes", "No"], "input_type": "radio", "value": ""}, "heparin_derivative": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "low_molecular_weight_heparin": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "unfractionated_heparin": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "warfarin": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_81_100_mg": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_101_324_mg": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_325_mg": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_dipyridamole": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "vorapaxar": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "apixaban": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "dabigatran": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "edoxaban": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "rivaroxaban": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "cangrelor": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "clopidogrel": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "other_p2y12": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "prasugrel": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "ticagrelor": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "ticlopidine": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}}}}}