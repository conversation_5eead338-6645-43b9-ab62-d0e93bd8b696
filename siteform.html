<style>
  .container {
    --uib-size: 40px;
    --uib-color: black;
    --uib-speed: 2s;
    --uib-bg-opacity: 0;
    height: var(--uib-size);
    width: var(--uib-size);
    transform-origin: center;
    animation: rotate var(--uib-speed) linear infinite;
    will-change: transform;
    overflow: visible;
  }

  .car {
    fill: none;
    stroke: var(--uib-color);
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: stretch calc(var(--uib-speed) * 0.75) ease-in-out infinite;
    will-change: stroke-dasharray, stroke-dashoffset;
    transition: stroke 0.5s ease;
  }

  .track {
    fill: none;
    stroke: var(--uib-color);
    opacity: var(--uib-bg-opacity);
    transition: stroke 0.5s ease;
  }

  /* Ensure loader appears in front of forms */
  #loader,
  .loader-holder,
  #content-loader {
    z-index: 9999 !important;
    position: absolute !important;
    background-color: rgba(255, 255, 255, 0.7) !important;
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes stretch {
    0% {
      stroke-dasharray: 0, 150;
      stroke-dashoffset: 0;
    }

    50% {
      stroke-dasharray: 75, 150;
      stroke-dashoffset: -25;
    }

    100% {
      stroke-dashoffset: -100;
    }
  }
</style>

<script type="module">
  import {
    parseCharInput,
    parseIntInput,
    parseEmailInput,
  } from "/abstractor/utils.js";
  window.parseCharInput = parseCharInput;
  window.parseIntInput = parseIntInput;
  window.parseEmailInput = parseEmailInput;
  window.utilsLoaded = true;

  // Function to validate site form fields
  window.validateSiteField = function (input, type) {
    if (!window.utilsLoaded) {
      console.warn("Utils not loaded yet, validation skipped");
      return;
    }

    try {
      if (type === "char") {
        // Use parseCharInput for character fields with a callback
        window.parseCharInput(input.value, function (validValue) {
          input.value = validValue;
        });
      } else if (type === "int") {
        // Use parseIntInput for integer fields with a callback
        window.parseIntInput(input.value, function (validValue) {
          input.value = validValue;
        });
      } else if (type === "email") {
        // Use parseEmailInput for email fields with a callback
        window.parseEmailInput(input.value, function (validValue) {
          input.value = validValue;
        });
      }
    } catch (error) {
      console.error("Error validating field:", error);
    }
  };
</script>

<div class="flex w-full h-full mb-9 mt-3 justify-center">
  <!-- Using main loader instead of form loader -->

  <div
    id="SiteSuccessModal"
    class="fixed w-full h-full bg-black bg-opacity-50 items-center justify-center"
    style="display: none; top: 0; left: 0; z-index: 99999"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-sm w-full z-10">
      <div
        id="header"
        class="flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg"
      >
        <span id="headertitle"></span>
      </div>
      <div class="p-4 text-center">
        <p class="text-gray-600" id="desc"></p>
      </div>
      <div class="p-4 flex justify-center">
        <button
          id="siteModalOkBtn"
          class="px-4 py-2 bg-green-500 text-white font-medium rounded"
        >
          OK
        </button>
      </div>
    </div>
  </div>

  <form
    id="siteFormElement"
    class="space-y-4 w-[80%] mb-4"
    onsubmit="createNewSite(event)"
  >
    <div>
      <h1 class="font-bold text-2xl mb-2">Site Info</h1>
      <label for="name" class="block text-sm font-medium text-gray-700"
        >Hospital Name <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="name"
        name="name"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
        oninput="validateSiteField(this, 'char')"
      />
    </div>
    <div>
      <label for="account_id" class="block text-sm font-medium text-gray-700"
        >Account ID</label
      >
      <input
        type="text"
        id="account_id"
        name="account_id"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        oninput="validateSiteField(this, 'int')"
      />
    </div>
    <div>
      <label for="address" class="block text-sm font-medium text-gray-700"
        >Address</label
      >
      <input
        type="text"
        id="address"
        name="address"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
      />
    </div>
    <div>
      <label for="city" class="block text-sm font-medium text-gray-700"
        >City <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="city"
        name="city"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
        oninput="validateSiteField(this, 'char')"
      />
    </div>
    <div>
      <label for="state" class="block text-sm font-medium text-gray-700"
        >State <span class="text-red-500">*</span></label
      >
      <input
        type="text"
        id="state"
        name="state"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        required
        oninput="validateSiteField(this, 'char')"
      />
    </div>
    <div>
      <label for="zip_code" class="block text-sm font-medium text-gray-700"
        >Zip Code</label
      >
      <input
        type="text"
        id="zip_code"
        name="zip_code"
        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
        oninput="validateSiteField(this, 'int')"
      />
    </div>
    <button
      type="submit"
      style="margin-bottom: 1rem"
      class="w-full mb-4 bg-[#8F58DD] text-white py-2 px-4 rounded-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
    >
      Submit
    </button>
  </form>
</div>

<script src="js/settings.js"></script>
