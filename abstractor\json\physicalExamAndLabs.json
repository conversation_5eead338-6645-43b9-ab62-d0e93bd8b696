{"physical_exam_and_labs": {"height": {"field_id": "6000", "label": "Height", "metric": "cm", "input_type": "string", "value": ""}, "weight": {"field_id": "6005", "label": "Weight", "metric": "kg", "input_type": "string", "value": ""}, "pulse": {"field_id": "6010", "label": "Pulse", "metric": "bpm", "input_type": "string", "value": ""}, "blood_pressure": {"field_id": "6015/6020", "label": "Blood Pressure", "metric": "mmHg", "input_type": "string", "value": ""}, "hemoglobin": {"field_id": "6030", "label": "Hemoglobin", "options": [{"id": "g_dl", "value": "g/dL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "prothrombin_time": {"field_id": "6040", "label": "Prothrombin Time (PT)", "options": [{"id": "sec", "value": "sec"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "inr": {"field_id": "6045", "label": "INR", "options": [{"id": "not_drawn", "value": "Not Drawn"}], "input_type": "string", "value": ""}, "creatinine": {"field_id": "6050", "label": "Creatinine", "options": [{"id": "mg_dl", "value": "mg/dL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "albumin": {"field_id": "14210", "label": "Albumin", "options": [{"id": "g_dl", "value": "g/dL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "platelet_count": {"field_id": "13213", "label": "Platelet Count", "options": [{"id": "ul", "value": "µL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "modified_rankin_scale": {"field_id": "14805", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "0", "value": "0: No symptoms at all"}, {"id": "1", "value": "1: No significant disability despite symptoms"}, {"id": "2", "value": "2: Slight disability"}, {"id": "3", "value": "3: Moderate disability"}, {"id": "4", "value": "4: Moderately severe disability"}, {"id": "5", "value": "5: Severe disability"}, {"id": "not_administered", "value": "Not Administered"}], "input_type": "select", "value": ""}}}