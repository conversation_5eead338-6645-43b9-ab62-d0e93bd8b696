{"radiation_exposure": {"verified": "", "cumulative_air_kerma": {"field_id": "7210", "label": "Cumulative Air Kerma", "input_type": "string", "value": "", "options": [{"id": 1, "value": "mGy"}, {"id": 2, "value": "Gy"}], "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the total radiation dose (Cumulative Air Kerma or Reference Air Kerma) recorded to the nearest milligray (mGy) or gray (Gy) for the lab visit, including all contributions from fluoroscopic and radiographic irradiation, from the start to the end of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the total radiation dose (Cumulative Air Kerma, or Reference Air Kerma) recorded to the nearest milligray (mGy) or gray (Gy). The value recorded should include the total dose for the lab visit. Cumulative air kerma is the total air kerma accrued from the beginning of an examination or procedure and includes all contributions from fluoroscopic and radiographic irradiation.\n**Target Value:**\nThe total between start of current procedure and end of current procedure"}, "contrast_volume": {"field_id": "7215", "label": "Contrast Volume", "input_type": "string", "value": "", "metric": "mL", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the total volume of contrast (ionic and non-ionic) used in milliliters (ml) for the lab visit, from the start to the end of the current TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate the volume of contrast (ionic and non-ionic) used in milliliters (ml). The volume recorded should be the total volume for the lab visit.\n**Target Value:**\nThe total between start of current procedure and end of current procedure"}, "dose_area_product": {"field_id": "14278", "label": "Dose Area Product", "input_type": "string", "value": "", "options": [{"id": 1, "value": "Gy·cm2"}, {"id": 2, "value": "dGy·cm2"}, {"id": 3, "value": "cGy·cm2"}, {"id": 4, "value": "mGy·cm2"}, {"id": 5, "value": "μGy·M2"}], "unit": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the total fluoroscopy dose, measured as Dose Area Product (DAP), for the lab visit, including the entire duration from the start to the end of the current TAVR procedure? Please provide the value to the nearest integer.", "description": "\n**Coding Instruction:**\nIndicate the total fluoroscopy dose to the nearest integer. The value recorded should include the total dose for the lab visit.\n**Target Value:**\nThe total between start of current procedure and end of current procedure"}}}