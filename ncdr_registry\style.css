* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #e6e2ec, #edebf0);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #8143d9;
  }
  
  .container {
    width: 100%;
    max-width: 600px;
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: 0.3s ease-in-out;
    overflow-wrap: break-word;
  }
  
  h2 {
    text-align: center;
    font-size: 26px;
    margin-bottom: 25px;
    color: #8143d9;
  }
  
  label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    color: #444;
  }
  
  select,
  button {
    width: 100%;
    padding: 12px 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 16px;
    border: 1px solid #ccc;
    transition: 0.2s ease;
  }
  
  select:focus {
    border-color: #5e1cba;
    outline: none;
  }
  
  button {
    background-color: #8143d9;
    color: white;
    border: none;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
  }
  
  button:hover {
    background-color: #5e1cba;
    transform: translateY(-1px);
  }
  
  #result {
    margin-top: 20px;
    padding: 15px;
    background: #f3f1f7;
    border-left: 6px solid #8143d9;
    border-radius: 8px;
    display: none;
    white-space: pre-wrap;
    font-size: 14px;
    color: #00332b;
    overflow: auto;
    word-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
  }
  
 
  @media (max-width: 600px) {
    .container {
      padding: 25px 20px;
    }
  
    h2 {
      font-size: 22px;
    }
  
    button,
    select {
      font-size: 15px;
    }
  }
  #clearResultBtn {
    margin-top: 10px;
  }
  
  .date-range {
    display: flex;
    align-items: center;
    gap: 2em; 
    flex-wrap: wrap; 
  }
  