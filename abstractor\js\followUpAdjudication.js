import {
  formatHeading,
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

/**
 * Recursive renderer for any input type, supporting multi_input_field nesting.
 * This matches the recursive approach in historyInterventions.js for all nested fields.
 */
function renderInputField({
  fieldKey,
  value,
  fieldContainer,
  patientData,
  catKey,
  updateTileStyleFn,
  updateContainerHighlightFn,
}) {
  // Add data-field-id attribute for easier identification
  if (value.field_id) {
    fieldContainer.setAttribute("data-field-id", value.field_id);
  }

  // Label
  const label = document.createElement("label");
  label.classList.add("label", "cursor-pointer");
  label.textContent = `${value.label}${
    value.metric ? ` (${value.metric})` : ""
  } (${value.field_id})`;
  if (value.description) label.setAttribute("title", value.description);
  fieldContainer.appendChild(label);

  // Modified by display
  const modifiedByDisplay = document.createElement("span");
  modifiedByDisplay.style.display = "block";
  modifiedByDisplay.style.textAlign = "right";
  modifiedByDisplay.style.marginTop = "-10px";
  modifiedByDisplay.style.color = "#8143d9";
  modifiedByDisplay.style.fontSize = "12px";
  if (!value.modified_by) value.modified_by = "";
  modifiedByDisplay.textContent = value.modified_by;

  // Recursive handlers
  if (value.input_type === "string" || value.input_type === "text") {
    const input = document.createElement("input");
    input.type = "text";
    input.name = fieldKey;
    input.placeholder = `Enter ${value.label}`;
    input.value = value.value || "";
    updateTileStyleFn(fieldContainer, input.value);

    let previousValue = input.value;
    input.addEventListener("input", (e) => {
      const currentValue = e.target.value;
      let isValid = false;
      validateStringInput(currentValue, value.field_id, (validatedValue) => {
        isValid = true;
        previousValue = validatedValue;
        value.value = validatedValue;
        value.modified_by = "ABSTRACTOR";
        updateTileStyleFn(fieldContainer, validatedValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlightFn(
          document.getElementById("followUpAdjudication"),
          patientData.follow_up_adjudication
        );
        if (validatedValue !== currentValue) e.target.value = validatedValue;
      });
      if (!isValid) e.target.value = previousValue;
    });
    fieldContainer.appendChild(input);
  } else if (value.input_type === "date" || value.input_type === "date_time") {
    if (value.input_type === "date") {
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${fieldKey}_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(value.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = fieldKey;
      dateInput.value = value.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";
      dateInput.max = new Date().toISOString().split("T")[0];

      updateTileStyleFn(fieldContainer, dateInput.value);

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        value.value = selectedDate;
        value.modified_by = "ABSTRACTOR";
        updateTileStyleFn(fieldContainer, selectedDate);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlightFn(
          document.getElementById("followUpAdjudication"),
          patientData.follow_up_adjudication
        );
      });
      displayInput.addEventListener("click", () => dateInput.showPicker());
      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      fieldContainer.appendChild(dateWrapper);
    } else if (value.input_type === "date_time") {
      // Create container for date and time inputs
      const dateTimeContainer = document.createElement("div");
      dateTimeContainer.style.display = "flex";
      dateTimeContainer.style.gap = "10px";
      dateTimeContainer.style.alignItems = "center";

      // Parse existing value if any
      let dateValue = "";
      let timeValue = "";
      if (value.value) {
        const dateTimeParts = value.value.split("T");
        if (dateTimeParts.length > 0) {
          dateValue = dateTimeParts[0];
          if (dateTimeParts.length > 1) {
            timeValue = dateTimeParts[1].substring(0, 5); // HH:MM format
          }
        }
      }

      // Create date wrapper similar to date input type
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";
      dateWrapper.style.flex = "1";

      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${fieldKey}_date_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(dateValue);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = `${fieldKey}_date`;
      dateInput.value = dateValue;
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Create time input
      const timeInput = document.createElement("input");
      timeInput.type = "time";
      timeInput.name = `${fieldKey}_time`;
      timeInput.value = timeValue;
      timeInput.style.flex = "1";

      // Function to update the combined value
      const updateDateTime = () => {
        if (dateInput.value) {
          const combinedValue = timeInput.value
            ? `${dateInput.value}T${timeInput.value}:00`
            : `${dateInput.value}T00:00:00`;
          value.value = combinedValue;
          value.modified_by = "ABSTRACTOR";
          updateTileStyleFn(fieldContainer, combinedValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlightFn(
            document.getElementById("followUpAdjudication"),
            patientData.follow_up_adjudication
          );
          displayInput.value = formatDisplayDate(dateInput.value);
        }
      };

      // Add event listeners
      dateInput.addEventListener("change", updateDateTime);
      timeInput.addEventListener("change", updateDateTime);
      displayInput.addEventListener("click", () => dateInput.showPicker());

      // Add inputs to containers
      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      dateTimeContainer.appendChild(dateWrapper);
      dateTimeContainer.appendChild(timeInput);

      fieldContainer.appendChild(dateTimeContainer);
      updateTileStyleFn(fieldContainer, value.value || "");
    }
  } else if (value.input_type === "radio") {
    const radioContainer = document.createElement("div");
    radioContainer.classList.add("radio-container");
    radioContainer.style.display = "flex";
    radioContainer.style.flexDirection = "column";
    radioContainer.style.gap = "10px";
    value.options.forEach((option) => {
      const radioWrapper = document.createElement("div");
      radioWrapper.style.display = "flex";
      radioWrapper.style.alignItems = "center";
      radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing
      const input = document.createElement("input");
      input.type = "radio";
      input.name = fieldKey;
      input.value = option;
      input.id = `${fieldKey}-${option}`;
      input.checked = value.value === option;
      input.addEventListener("change", (e) => {
        if (e.target.checked) {
          value.value = e.target.value;
          value.modified_by = "ABSTRACTOR";
          updateTileStyleFn(fieldContainer, e.target.value);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlightFn(
            document.getElementById("followUpAdjudication"),
            patientData.follow_up_adjudication
          );
        }
      });
      const optionLabel = document.createElement("label");
      optionLabel.setAttribute("for", `${fieldKey}-${option}`);
      optionLabel.innerText = option;
      if (value.description)
        optionLabel.setAttribute("title", value.description);
      radioWrapper.appendChild(input);
      radioWrapper.appendChild(optionLabel);
      radioContainer.appendChild(radioWrapper);
    });
    fieldContainer.appendChild(radioContainer);
    updateTileStyleFn(fieldContainer, value.value || "");
  } else if (value.input_type === "select") {
    const select = document.createElement("select");
    select.name = fieldKey;
    if (!value.value) {
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.innerText = "Select an option";
      defaultOption.disabled = true;
      defaultOption.selected = true;
      select.appendChild(defaultOption);
    }
    value.options.forEach((option) => {
      const o = document.createElement("option");
      o.value = option.id;
      o.innerText = option.value;
      if (value.value && value.value === option.value) o.selected = true;
      if (value.description) o.setAttribute("title", value.description);
      select.appendChild(o);
    });
    select.addEventListener("change", (e) => {
      const selOpt = value.options.find((o) => o.id === e.target.value);
      const newValue = selOpt ? selOpt.value : "";
      value.value = newValue;
      value.modified_by = "ABSTRACTOR";
      updateTileStyleFn(fieldContainer, newValue);
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlightFn(
        document.getElementById("followUpAdjudication"),
        patientData.follow_up_adjudication
      );
    });
    fieldContainer.appendChild(select);
    updateTileStyleFn(fieldContainer, value.value || "");
  } else if (value.input_type === "checkbox") {
    const checkboxWrapper = document.createElement("div");
    checkboxWrapper.style.display = "flex";
    checkboxWrapper.style.alignItems = "center";
    checkboxWrapper.style.marginTop = "10px";

    const checkbox = document.createElement("input");
    checkbox.type = "checkbox";
    checkbox.id = `${fieldKey}-checkbox`;
    checkbox.checked = value.value === "True";

    checkbox.addEventListener("change", (e) => {
      value.value = e.target.checked ? "True" : "";
      value.modified_by = "ABSTRACTOR";
      updateTileStyleFn(fieldContainer, value.value);
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlightFn(
        document.getElementById("followUpAdjudication"),
        patientData.follow_up_adjudication
      );
    });

    const checkboxLabel = document.createElement("label");
    checkboxLabel.setAttribute("for", `${fieldKey}-checkbox`);
    checkboxLabel.innerText = value.label;
    checkboxLabel.style.marginLeft = "10px";

    checkboxWrapper.appendChild(checkbox);
    checkboxWrapper.appendChild(checkboxLabel);
    fieldContainer.appendChild(checkboxWrapper);
    updateTileStyleFn(fieldContainer, value.value || "");
  } else if (value.input_type === "multi_select") {
    if (!Array.isArray(value.value))
      value.value = value.value ? [value.value] : [];
    const dropdownContainer = document.createElement("div");
    dropdownContainer.classList.add("dropdown-container");
    dropdownContainer.style.position = "relative";
    dropdownContainer.style.width = "100%";
    fieldContainer.style.overflow = "visible";
    if (fieldContainer.parentElement)
      fieldContainer.parentElement.style.overflow = "visible";
    document.getElementById("followUpAdjudication").style.overflow = "visible";

    const dropdownHeader = document.createElement("div");
    dropdownHeader.classList.add("dropdown-header");
    dropdownHeader.style.padding = "8px 12px";
    dropdownHeader.style.border = "1px solid #ccc";
    dropdownHeader.style.borderRadius = "4px";
    dropdownHeader.style.cursor = "pointer";
    dropdownHeader.style.display = "flex";
    dropdownHeader.style.justifyContent = "space-between";
    dropdownHeader.style.alignItems = "center";
    dropdownHeader.style.backgroundColor = "#fff";
    const selectedText = document.createElement("span");
    selectedText.classList.add("selected-text");
    function updateSelectedText() {
      if (value.value.length === 0) {
        selectedText.textContent = "Select options...";
      } else if (value.value.length === 1) {
        const selectedOption = value.options.find((opt) => {
          const optValue = typeof opt === "object" ? opt.value : opt;
          return optValue === value.value[0];
        });
        selectedText.textContent = selectedOption
          ? typeof selectedOption === "object"
            ? selectedOption.value
            : selectedOption
          : value.value[0];
      } else {
        selectedText.textContent = `${value.value.length} options selected`;
      }
    }
    updateSelectedText();
    const dropdownArrow = document.createElement("span");
    dropdownArrow.innerHTML = "&#9662;";
    dropdownHeader.appendChild(selectedText);
    dropdownHeader.appendChild(dropdownArrow);

    const dropdownContent = document.createElement("div");
    dropdownContent.classList.add("dropdown-content");
    dropdownContent.style.display = "none";
    dropdownContent.style.position = "fixed";
    dropdownContent.style.width = "350px";
    dropdownContent.style.maxHeight = "200px";
    dropdownContent.style.overflowY = "auto";
    dropdownContent.style.overflowX = "hidden";
    dropdownContent.style.backgroundColor = "#fff";
    dropdownContent.style.border = "1px solid #ccc";
    dropdownContent.style.borderRadius = "4px";
    dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
    dropdownContent.style.zIndex = "9999";
    value.options.forEach((option) => {
      const checkboxWrapper = document.createElement("div");
      checkboxWrapper.classList.add("checkbox-wrapper");
      checkboxWrapper.style.display = "flex";
      checkboxWrapper.style.alignItems = "center";
      checkboxWrapper.style.padding = "8px 12px";
      checkboxWrapper.style.borderBottom = "1px solid #eee";
      checkboxWrapper.style.textAlign = "left";
      checkboxWrapper.style.cursor = "pointer";
      // Handle both object options and string options
      const optionValue = typeof option === "object" ? option.value : option;
      const optionId = typeof option === "object" ? option.id : option;

      const input = document.createElement("input");
      input.type = "checkbox";
      input.name = `${fieldKey}-${optionId}`;
      input.value = optionId;
      input.id = `${fieldKey}-${optionId}`;
      input.style.marginRight = "4px";
      input.checked = value.value.includes(optionValue);
      input.addEventListener("change", (e) => {
        let currentValues = Array.isArray(value.value) ? [...value.value] : [];
        if (e.target.checked) {
          if (!currentValues.includes(optionValue))
            currentValues.push(optionValue);
        } else {
          currentValues = currentValues.filter((val) => val !== optionValue);
        }
        value.value = currentValues;
        value.modified_by = "ABSTRACTOR";
        updateSelectedText();
        updateTileStyleFn(
          fieldContainer,
          currentValues.length > 0 ? "filled" : ""
        );
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlightFn(
          document.getElementById("followUpAdjudication"),
          patientData.follow_up_adjudication
        );
      });
      const optionLabel = document.createElement("label");
      optionLabel.setAttribute("for", `${fieldKey}-${option.id}`);
      optionLabel.style.marginLeft = "0";
      optionLabel.style.display = "inline-block";
      optionLabel.style.whiteSpace = "nowrap";
      optionLabel.style.cursor = "pointer";
      optionLabel.style.flexGrow = "1";
      if (typeof option === "object" && option.field_id) {
        optionLabel.innerText = `${optionValue} (${option.field_id})`;
      } else {
        optionLabel.innerText = optionValue;
      }
      const inputLabelWrapper = document.createElement("div");
      inputLabelWrapper.style.display = "flex";
      inputLabelWrapper.style.alignItems = "center";
      inputLabelWrapper.style.gap = "0";
      inputLabelWrapper.appendChild(input);
      inputLabelWrapper.appendChild(optionLabel);
      checkboxWrapper.appendChild(inputLabelWrapper);
      dropdownContent.appendChild(checkboxWrapper);
    });
    dropdownContainer.appendChild(dropdownHeader);

    function positionDropdown() {
      const headerRect = dropdownHeader.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      dropdownContent.style.top = `${headerRect.bottom}px`;
      const rightEdge = headerRect.left + 250;
      if (rightEdge > viewportWidth) {
        dropdownContent.style.left = `${headerRect.right - 250}px`;
      } else {
        dropdownContent.style.left = `${headerRect.left}px`;
      }
      const spaceBelow = viewportHeight - headerRect.bottom;
      const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
      dropdownContent.style.maxHeight = `${maxHeight}px`;
    }
    document.addEventListener("click", (e) => {
      if (
        !dropdownContainer.contains(e.target) &&
        !dropdownContent.contains(e.target)
      ) {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
      }
    });
    dropdownHeader.onclick = (e) => {
      e.stopPropagation();
      const isOpen = dropdownContent.style.display === "block";
      if (!isOpen) {
        if (!document.body.contains(dropdownContent)) {
          document.body.appendChild(dropdownContent);
        }
        dropdownContent.style.display = "block";
        positionDropdown();
        dropdownArrow.innerHTML = "&#9652;";
      } else {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
      }
    };
    fieldContainer.appendChild(dropdownContainer);
    updateTileStyleFn(fieldContainer, value.value.length > 0 ? "filled" : "");
  } else if (value.input_type === "multi_input_field") {
    // Recursive multi_input_field (core logic)
    const multiInputContainer = document.createElement("div");
    multiInputContainer.classList.add("multi-input-container");
    const radioContainer = document.createElement("div");
    radioContainer.classList.add("radio-container");
    radioContainer.style.display = "flex";
    radioContainer.style.flexDirection = "column";
    radioContainer.style.gap = "10px";
    radioContainer.style.width = "100%";
    const conditionalContentContainer = document.createElement("div");
    conditionalContentContainer.classList.add("conditional-content-container");
    conditionalContentContainer.style.marginTop = "10px";
    conditionalContentContainer.style.padding = "10px";
    conditionalContentContainer.style.border = "1px solid #eee";
    conditionalContentContainer.style.borderRadius = "4px";
    conditionalContentContainer.style.display = "none";
    conditionalContentContainer.style.width = "100%";

    // Radio options
    if (value.options && Array.isArray(value.options)) {
      // Debug the options to see their structure

      value.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginBottom = "10px"; // Added margin for better spacing

        // Handle both string options and object options
        const optionValue = typeof option === "object" ? option.value : option;
        const optionId = typeof option === "object" ? option.id : option;

        const input = document.createElement("input");
        input.type = "radio";
        input.name = fieldKey;
        input.value = optionValue;
        input.id = `${fieldKey}-${optionId}`;
        input.checked = value.value === optionValue;

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            // Store the selected value
            value.value = e.target.value;
            value.modified_by = "ABSTRACTOR";

            updateTileStyleFn(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlightFn(
              document.getElementById("followUpAdjudication"),
              patientData.follow_up_adjudication
            );

            // Clear previous content
            conditionalContentContainer.innerHTML = "";
            conditionalContentContainer.style.display = "none";

            // Determine which conditional key to use based on selected option
            let conditionalKey = null;
            if (e.target.value.startsWith("Yes") && value.if_yes) {
              conditionalKey = "if_yes";
            } else if (e.target.value === "No" && value.if_no) {
              conditionalKey = "if_no";
            } else if (e.target.value === "Alive" && value.if_alive) {
              conditionalKey = "if_alive";
            } else if (e.target.value === "Deceased" && value.if_deceased) {
              conditionalKey = "if_deceased";
            } else if (e.target.value === "Infarction" && value.if_infarction) {
              conditionalKey = "if_infarction";
            } else if (e.target.value === "Hemorrhage" && value.if_hemorrhage) {
              conditionalKey = "if_hemorrhage";
            } else if (e.target.value === "Both" && value.if_both) {
              conditionalKey = "if_both";
            }

            // If we have a conditional key, render its content
            if (conditionalKey && value[conditionalKey]) {
              conditionalContentContainer.style.display = "block";

              // Iterate through each field in the conditional content
              Object.entries(value[conditionalKey]).forEach(
                ([subKey, subField]) => {
                  // Create a container for this sub-field
                  const subFieldContainer = document.createElement("div");
                  subFieldContainer.classList.add("sub-field-container");
                  subFieldContainer.style.marginBottom = "15px";

                  // Recursively render the subfield using the same rendering function
                  renderInputField({
                    fieldKey: subKey,
                    value: subField,

                    fieldContainer: subFieldContainer,
                    patientData,
                    catKey,
                    updateTileStyleFn,
                    updateContainerHighlightFn,
                  });

                  conditionalContentContainer.appendChild(subFieldContainer);
                }
              );
            }
          }
        });
        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${fieldKey}-${optionId}`);
        optionLabel.innerText = optionValue;
        if (value.description)
          optionLabel.setAttribute("title", value.description);
        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });
    }
    multiInputContainer.appendChild(radioContainer);
    multiInputContainer.appendChild(conditionalContentContainer);

    // Initial render for conditional if already selected
    if (value.value) {
      // Determine which conditional key to use based on selected option
      let conditionalKey = null;
      if (typeof value.value === "string") {
        if (value.value.startsWith("Yes") && value.if_yes) {
          conditionalKey = "if_yes";
        } else if (value.value === "No" && value.if_no) {
          conditionalKey = "if_no";
        } else if (value.value === "Alive" && value.if_alive) {
          conditionalKey = "if_alive";
        } else if (value.value === "Deceased" && value.if_deceased) {
          conditionalKey = "if_deceased";
        } else if (value.value === "Infarction" && value.if_infarction) {
          conditionalKey = "if_infarction";
        } else if (value.value === "Hemorrhage" && value.if_hemorrhage) {
          conditionalKey = "if_hemorrhage";
        } else if (value.value === "Both" && value.if_both) {
          conditionalKey = "if_both";
        }
      }

      // If we have a conditional key, render its content
      if (conditionalKey && value[conditionalKey]) {
        conditionalContentContainer.style.display = "block";

        // Iterate through each field in the conditional content
        Object.entries(value[conditionalKey]).forEach(([subKey, subField]) => {
          // Create a container for this sub-field
          const subFieldContainer = document.createElement("div");
          subFieldContainer.classList.add("sub-field-container");
          subFieldContainer.style.marginBottom = "15px";

          // Recursively render the subfield using the same rendering function
          renderInputField({
            fieldKey: subKey,
            value: subField,

            fieldContainer: subFieldContainer,
            patientData,
            catKey,
            updateTileStyleFn,
            updateContainerHighlightFn,
          });

          conditionalContentContainer.appendChild(subFieldContainer);
        });
      }
    }
    fieldContainer.appendChild(multiInputContainer);

    // Set initial tile style based on whether the field is filled or not
    updateTileStyleFn(fieldContainer, value.value ? value.value : "");
  }

  // Only add modifiedBy to top-level fields, not nested fields
  if (!fieldContainer.classList.contains("sub-field-container")) {
    fieldContainer.appendChild(modifiedByDisplay);
  }
}

export function renderFollowUpAdjudication(patientData) {
  const container = document.getElementById("followUpAdjudication");
  container.innerHTML = "";
  container.style.position = "relative";

  if (
    typeof patientData.follow_up_adjudication.verified !== "object" ||
    patientData.follow_up_adjudication.verified === null
  ) {
    patientData.follow_up_adjudication.verified = {
      value: patientData.follow_up_adjudication.verified || "False",
      modified_by: "",
    };
  }

  // Find dependency skip fields for combined input+checkbox rendering (not the multi_input_field logic)
  const skipFields = new Map(); // Using Map to store category and field key pairs

  // Helper function to process fields in a category or subcategory
  const processFields = (fields, category) => {
    Object.entries(fields).forEach(([fieldKey, value]) => {
      // Skip non-object values or verified field
      if (typeof value !== "object" || fieldKey === "verified") return;

      // If this is a subcategory (contains nested fields)
      if (!value.input_type && !value.field_id) {
        // Process the subcategory fields
        processFields(value, category + "." + fieldKey);
        return;
      }

      // Check if this field has a dependency
      if (value.dependency) {
        // Find the dependent field in the same category
        let dependentFieldKey = null;
        let dependentCategory = category;

        // Search in the current category/subcategory
        const categoryParts = category.split(".");
        let currentObj = patientData.follow_up_adjudication;

        // Navigate to the correct nested object
        for (const part of categoryParts) {
          if (part === "follow_up_adjudication") continue;
          currentObj = currentObj[part];
        }

        // Special case for medications
        if (category.includes("current_medications_at_time_of_event")) {
          currentObj = currentObj.medications;
        }

        // Find the field with matching field_id
        dependentFieldKey = Object.keys(currentObj).find(
          (k) => currentObj[k].field_id === value.dependency
        );

        if (dependentFieldKey) {
          // Store both fields with their categories
          skipFields.set(`${dependentCategory}.${dependentFieldKey}`, true);
          skipFields.set(`${category}.${fieldKey}`, true);
        }
      }
    });
  };

  // Process all categories in the patient data
  Object.entries(patientData.follow_up_adjudication).forEach(
    ([category, fields]) => {
      if (category === "verified") return;
      processFields(fields, "follow_up_adjudication." + category);
    }
  );

  Object.entries(patientData.follow_up_adjudication).forEach(
    ([category, fields]) => {
      if (category === "verified") return;
      let isMeds = category === "current_medications_at_time_of_event";
      let fieldsToRender = isMeds ? fields.medications : fields;

      const catContainer = document.createElement("div");
      catContainer.classList.add("category-container");
      catContainer.setAttribute("data-category", category); // Add data-category attribute
      catContainer.style.display = "contents";

      // Create and append the category label
      // For current_medications_at_time_of_event, use its label instead of formatting the category name
      if (formatHeading(category).toLowerCase() !== "medications") {
        const catLabel = document.createElement("h4");
        catLabel.style.fontWeight = "bold";
        catLabel.style.gridColumn = "1 / -1";

        if (category === "current_medications_at_time_of_event") {
          // Use the label from the data instead of formatting the category name
          catLabel.innerText =
            fields.label || "Current Medications at Time of Event";
        } else {
          catLabel.innerText = formatHeading(category);
        }

        catContainer.appendChild(catLabel);
      }

      Object.entries(fieldsToRender).forEach(([key, value]) => {
        const fullKey = `follow_up_adjudication.${category}.${key}`;
        const specialKey =
          category === "current_medications_at_time_of_event"
            ? `follow_up_adjudication.${category}.medications.${key}`
            : fullKey;

        // multi_input_field FULL LOGIC:
        if (value.input_type === "multi_input_field") {
          const tileWrapper = document.createElement("div");
          tileWrapper.classList.add("tile-wrapper");
          tileWrapper.style.position = "relative";
          tileWrapper.style.marginBottom = "24px";
          const fieldContainer = document.createElement("div");
          fieldContainer.classList.add("field-container");
          renderInputField({
            fieldKey: key,
            value: value,

            fieldContainer: fieldContainer,
            patientData,
            catKey: category,
            updateTileStyleFn: updateTileStyle,
            updateContainerHighlightFn: updateContainerHighlight,
          });
          // Make sure the tile is properly highlighted
          updateTileStyle(fieldContainer, value.value ? value.value : "");

          tileWrapper.appendChild(fieldContainer);
          catContainer.appendChild(tileWrapper);
          return;
        }

        // Otherwise, render all other field types normally (using the recursive function for parity and extensibility)
        const tileWrapper = document.createElement("div");
        tileWrapper.classList.add("tile-wrapper");
        tileWrapper.style.position = "relative";
        tileWrapper.style.marginBottom = "24px";
        const fieldContainer = document.createElement("div");
        fieldContainer.classList.add("field-container");
        renderInputField({
          fieldKey: key,
          value: value,

          fieldContainer: fieldContainer,
          patientData,
          catKey: category,
          updateTileStyleFn: updateTileStyle,
          updateContainerHighlightFn: updateContainerHighlight,
        });
        tileWrapper.appendChild(fieldContainer);
        catContainer.appendChild(tileWrapper);
      });

      container.appendChild(catContainer);
    }
  );

  // Helper function to render dependency fields in a category or subcategory
  const renderDependencyFields = (fields, category, parentContainer) => {
    // Get the category container (should have been created earlier)
    let catContainer = parentContainer;
    const categoryName = category.split(".").pop();

    if (!catContainer) {
      // Find the existing category container
      catContainer = document.querySelector(
        `#followUpAdjudication .category-container[data-category="${categoryName}"]`
      );

      if (!catContainer) {
        console.error(
          `Category container for ${categoryName} not found - skipping dependency fields`
        );
        return;
      }
    }

    Object.entries(fields).forEach(([fieldKey, value]) => {
      // Skip non-object values or verified field
      if (typeof value !== "object" || fieldKey === "verified") return;

      // If this is a subcategory (contains nested fields)
      if (!value.input_type && !value.field_id) {
        // Create a subcategory container
        const subCatContainer = document.createElement("div");
        subCatContainer.classList.add("subcategory-container");
        subCatContainer.setAttribute("data-subcategory", fieldKey);
        subCatContainer.style.display = "contents";

        // Add subcategory header if it doesn't exist
        if (
          !catContainer.querySelector(`[data-subcategory-header="${fieldKey}"]`)
        ) {
          // Skip creating subcategory headers for medications
          if (formatHeading(fieldKey).toLowerCase() !== "medications") {
            const subCatHeader = document.createElement("h5");
            subCatHeader.style.fontWeight = "bold";
            subCatHeader.style.gridColumn = "1 / -1";
            subCatHeader.style.marginTop = "10px";
            subCatHeader.innerText = formatHeading(fieldKey);
            subCatHeader.setAttribute("data-subcategory-header", fieldKey);
            catContainer.appendChild(subCatHeader);
          }
        }

        catContainer.appendChild(subCatContainer);

        // Process the subcategory fields
        renderDependencyFields(
          value,
          `${category}.${fieldKey}`,
          subCatContainer
        );
        return;
      }

      const fullKey = `${category}.${fieldKey}`;

      // Skip fields that don't have dependencies or aren't in the skip list
      if (!value.dependency || !skipFields.has(fullKey)) return;

      // Find the dependent field (N/A checkbox) in the same category
      const categoryParts = category.split(".");
      let currentObj = patientData.follow_up_adjudication;

      // Navigate to the correct nested object
      for (const part of categoryParts) {
        if (part === "follow_up_adjudication") continue;
        currentObj = currentObj[part];
      }

      // Special case for medications
      if (category.includes("current_medications_at_time_of_event")) {
        currentObj = currentObj.medications;
      }

      // Find the field with matching field_id
      const dependentFieldKey = Object.keys(currentObj).find(
        (k) => currentObj[k].field_id === value.dependency
      );

      const dependentField = currentObj[dependentFieldKey];

      // Create an outer wrapper to hold the field tile and the modified_by display
      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";

      // Create the field container
      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");

      // Create and append the label element
      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.textContent = value.metric
        ? `${value.label} (${value.metric}) (${value.field_id})`
        : `${value.label} (${value.field_id})`;
      if (value.description) {
        label.setAttribute("title", value.description);
      }
      fieldContainer.appendChild(label);

      // Create the modified_by display element
      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.marginTop = "-10px";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";
      if (!value.modified_by) {
        value.modified_by = "";
      }
      modifiedByDisplay.textContent = value.modified_by;

      // Create a wrapper for the input and checkbox to display them side by side
      const inputWrapper = document.createElement("div");
      inputWrapper.style.display = "flex";
      inputWrapper.style.alignItems = "center";
      inputWrapper.style.gap = "10px";

      // Create the appropriate input based on input_type
      let input;

      if (value.input_type === "select" && value.options) {
        // Create a select dropdown
        input = document.createElement("select");
        input.name = fieldKey;
        input.style.flexGrow = "1";

        // Add a default option
        if (!value.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          input.appendChild(defaultOption);
        }

        // Add options from the data
        value.options.forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          if (value.value && value.value === option.value) {
            optionElement.selected = true;
          }
          input.appendChild(optionElement);
        });
      } else {
        // Create a text input for other types
        input = document.createElement("input");
        input.type = "text";
        input.name = fieldKey;
        input.placeholder = `Enter ${value.label}`;
        input.value = value.value || "";
        input.style.flexGrow = "1";
      }

      // Create the N/A checkbox
      const checkboxWrapper = document.createElement("div");
      checkboxWrapper.style.display = "flex";
      checkboxWrapper.style.alignItems = "center";

      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.name = dependentFieldKey;
      checkbox.id = `${dependentFieldKey}-checkbox`;
      checkbox.checked = dependentField.value === "True";

      const checkboxLabel = document.createElement("label");
      checkboxLabel.setAttribute("for", `${dependentFieldKey}-checkbox`);
      checkboxLabel.innerText =
        `${dependentField.label} (${dependentField.field_id})` || "N/A";
      checkboxLabel.style.marginLeft = "5px";

      checkboxWrapper.appendChild(checkbox);
      checkboxWrapper.appendChild(checkboxLabel);

      // Set initial state based on current values
      if (dependentField.value === "True") {
        input.disabled = true;
        if (value.input_type === "select") {
          // For select, reset to first option
          if (input.options.length > 0) {
            input.selectedIndex = 0;
          }
        } else {
          // For text input, clear the value
          input.value = "";
        }
        fieldsToRender[fieldKey].value = "";
      }

      updateTileStyle(
        fieldContainer,
        value.value || (dependentField.value === "True" ? "True" : "")
      );

      // Add appropriate event listeners based on input type
      if (value.input_type === "select") {
        // For select fields
        input.addEventListener("change", (e) => {
          const selectedOption = value.options.find(
            (option) => option.id.toString() === e.target.value
          );
          const newValue = selectedOption ? selectedOption.value : "";

          // Update model and UI
          currentObj[fieldKey].value = newValue;
          currentObj[fieldKey].modified_by = "ABSTRACTOR";

          // If select has value, disable the checkbox
          if (newValue) {
            checkbox.checked = false;
            currentObj[dependentFieldKey].value = "";
          }

          updateTileStyle(fieldContainer, newValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_adjudication
          );
        });
      } else {
        // For text input fields
        let previousValue = input.value;

        input.addEventListener("input", (e) => {
          const currentValue = e.target.value;

          // Use validation utilities with temporary callback
          let isValid = false;
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update model and UI only if value is valid
              currentObj[fieldKey].value = validatedValue;
              currentObj[fieldKey].modified_by = "ABSTRACTOR";

              // If input has value, disable the checkbox
              if (validatedValue) {
                checkbox.checked = false;
                currentObj[dependentFieldKey].value = "";
              }

              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.follow_up_adjudication
              );

              // Update input value if validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );

          // Revert to previous value if validation failed
          if (!isValid) {
            e.target.value = previousValue;
          }
        });
      }

      checkbox.addEventListener("change", (e) => {
        const isChecked = e.target.checked;

        // Update the dependent field value
        currentObj[dependentFieldKey].value = isChecked ? "True" : "";
        currentObj[dependentFieldKey].modified_by = "ABSTRACTOR";

        // If checkbox is checked, disable and clear the input
        if (isChecked) {
          input.disabled = true;
          if (value.input_type === "select") {
            // For select, reset to first option
            if (input.options.length > 0) {
              input.selectedIndex = 0;
            }
          } else {
            // For text input, clear the value
            input.value = "";
          }
          currentObj[fieldKey].value = "";
        } else {
          input.disabled = false;
        }

        updateTileStyle(fieldContainer, isChecked ? "True" : "");
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.follow_up_adjudication);
      });

      // Append elements to the wrapper
      inputWrapper.appendChild(input);
      inputWrapper.appendChild(checkboxWrapper);
      fieldContainer.appendChild(inputWrapper);

      // Append field container and modified_by display to the tile wrapper
      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);
      catContainer.appendChild(tileWrapper);
    });
  };

  // Call the renderDependencyFields function to process all categories
  Object.entries(patientData.follow_up_adjudication).forEach(
    ([category, fields]) => {
      if (category === "verified") return;

      renderDependencyFields(fields, "follow_up_adjudication." + category);
    }
  );

  // We don't need to create category containers here as they're already created
  // during the initial rendering of fields in the previous loop

  // We no longer need special handling for the neurologic category
  // as we've modified the main rendering logic to include the modified_rankin_scale_mrs field

  // Verified checkbox area
  const verifiedData = patientData.follow_up_adjudication.verified;
  const verifiedContainer = document.createElement("div");
  verifiedContainer.classList.add("verified-container"); // Add class for easier selection
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";

  const verifiedCheckbox = document.createElement("input");
  verifiedCheckbox.type = "checkbox";
  verifiedCheckbox.id = "followUpAdjudication-verified-checkbox";
  verifiedCheckbox.checked = verifiedData.value === "True";
  verifiedCheckbox.style.width = "24px";
  verifiedCheckbox.style.height = "24px";

  verifiedCheckbox.addEventListener("change", (e) => {
    verifiedData.value = e.target.checked ? "True" : "False";
    updateContainerHighlight(container, patientData.follow_up_adjudication);
  });

  const verifiedLabel = document.createElement("label");
  verifiedLabel.setAttribute("for", "followUpAdjudication-verified-checkbox");
  verifiedLabel.classList.add("mt-2", "ml-2");
  verifiedLabel.innerText = "Verified";
  verifiedLabel.style.fontSize = "18px";
  verifiedLabel.style.fontWeight = "bold";

  verifiedContainer.appendChild(verifiedCheckbox);
  verifiedContainer.appendChild(verifiedLabel);
  container.appendChild(verifiedContainer);

  updateContainerHighlight(container, patientData.follow_up_adjudication);
}
