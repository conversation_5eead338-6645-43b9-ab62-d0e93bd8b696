const caseSelect = document.getElementById('caseSelect');
const resultDiv = document.getElementById('result');
const clearResultBtn = document.getElementById('clearResultBtn');

async function loadCases() {
    try {


        const startDate = document.getElementById("startDate").value;
        const endDate = document.getElementById("endDate").value;
  
        if (!startDate || !endDate) {
            alert("Please select both start and end dates.");
            return;
        }

        console.log("start_date :",startDate,"end_date :",endDate)
    
        access_token = localStorage.getItem("access_token");
        if (!access_token) {
            alert("No access token found. Please login again.");
            return;
        }
    
        const apiUrl = `${config.apiUrl}/clinical/schedules?type=completed&completed_start_date=${startDate}&completed_end_date=${endDate}`;
        console.log("Fetching patients from:", apiUrl);
    
        const res = await fetch(apiUrl, {
            method: "GET",
            headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${access_token}`,
            },
        });
    
        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }
    
        const json = await res.json();
        const cases = json.result;
    
        if (!Array.isArray(cases)) {
            throw new Error("Expected result to be an array");
        }
    
        caseSelect.innerHTML = '<option value="">-- Select Patient --</option>';
        cases.forEach((item) => {
            const option = document.createElement("option");
            option.value = item.case_id;
            option.text = item.patient?.name || "Unnamed Patient";
            caseSelect.appendChild(option);
        });
        } catch (err) {
      console.error("Fetch error:", err);
      alert(`Fetching patient list failed. Error: ${err.message}`);
    }
  }
  

document.getElementById('submitBtn').addEventListener('click', async () => {
  const caseId = caseSelect.value;
  console.log("case_id : ", caseId)
  if (!caseId) {
    alert("Please select a Case ID first.");
    return;
  }
  try {
    const apiUrl = `${config.apiUrl}/ncdr/cases/${caseId}/submit`;
    const access_token = localStorage.getItem("access_token")
    const res =await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization : `Bearer ${access_token}`,
        },
      });
    const data = await res.json();
    if(res.ok){
      const status = data.result.status;
      resultDiv.style.display = 'block';
      resultDiv.textContent = `Submit Response:\n${status}`;
      clearResultBtn.style.display = 'inline-block';
    }else{
      const error = data.error_description;
      resultDiv.style.display = 'block';
      resultDiv.textContent = `Submit Response:\n${error}`;
      clearResultBtn.style.display = 'inline-block';
    }

  } catch (err) {
    alert('Error submitting case.');
    console.error(err);
  }
});

document.getElementById('checkStatusBtn').addEventListener('click', async () => {
  const caseId = caseSelect.value;
  if (!caseId) {
    alert("Please select a Case ID first.");
    return;
  }
  try {
    const apiUrl =`${config.apiUrl}/ncdr/cases/${caseId}/status`;
    const access_token = localStorage.getItem("access_token")
    const res = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization : `Bearer ${access_token}`,
        },
      });
    const data = await res.json();
    const result = data.result.result;
    const formattedXml = result
  .replace(/\\r\\n/g, "\n").replace(/\t/g, "    ").replace(/&/g, "&amp;")          
  .replace(/</g, "&lt;").replace(/>/g, "&gt;");  
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `<pre>${formattedXml}</pre>`;
    clearResultBtn.style.display = 'inline-block';

  } catch (err) {
    alert('Error checking status.');
    console.error(err);
  }
});

clearResultBtn.addEventListener('click', () => {
    resultDiv.style.display = 'none';
    resultDiv.textContent = '';
    clearResultBtn.style.display = 'none';
  });
  
  caseSelect.addEventListener('change', () => {
    resultDiv.style.display = 'none';
    clearResultBtn.style.display = 'none';
    resultDiv.textContent = '';
  });
window.onload = loadCases();
