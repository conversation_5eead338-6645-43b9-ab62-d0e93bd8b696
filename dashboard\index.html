<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <title>Dashboard</title>
    <script src="../config.js"></script>
</head>

<body>
    <nav class="bg-gray-600 shadow">
        <div class="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex items-center">
                        <h1 class="text-xl font-bold">Superset</h1>
                    </div>
                    <div class="hidden sm:flex sm:ml-6 ">
                        <a href="#"
                            class="flex items-center text-white hover:bg-gray-200 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Home</a>
                        <a href="#"
                            class="flex items-center text-white hover:bg-gray-200 hover:text-white px-3 py-2 rounded-md text-sm font-medium">About</a>
                        <a href="#"
                            class="flex items-center text-white hover:bg-gray-200 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Services</a>
                        <a href="#"
                            class="flex items-center text-white hover:bg-gray-200 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Contact</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <a id="logout"
                        class="text-red-600 hover:bg-red-200 hover:text-red-900 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </nav>
    <script src="https://unpkg.com/@superset-ui/embedded-sdk"></script>
    <div id="dashboard" style="height: 100vh; width: 100%;"></div>
    <script>
        document.getElementById("logout").addEventListener('click', () => {
            fetch(`${config.apiUrl}/api/v2/logout`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ refresh_token: localStorage.getItem("dash_refresh_token") })
            })
                .then(res => {
                    if (!res.ok) {
                        throw new Error(`Logout failed: ${res.status}`);
                    }
                    return res.json(); // Parse the response as JSON
                })
                .then(data => {
                    console.log("Logout successful:", data);
                    // Clear tokens from localStorage
                    localStorage.clear();

                    window.location.href = "/dashboard/login.html";
                })
                .catch(error => {
                    console.error("Error during logout:", error);
                    alert("Failed to logout. Please try again.");
                });

        });
        async function embedDashboard() {
            try {
                const guest_token = localStorage.getItem("guest_token");
                if (guest_token == null || guest_token == "") {
                    window.location.href = "login.html";
                }
                supersetEmbeddedSdk.embedDashboard({
                    id: "a46e65dc-310b-4373-8b16-ae2ee9c26729",
                    supersetDomain: config.super_set,
                    mountPoint: document.getElementById("dashboard"),
                    fetchGuestToken: () => guest_token,
                    dashboardUiConfig: {
                        hideTitle: true,
                        filters: {
                            expanded: true
                        },
                        urlParams: {
                            standalone: 1
                        }
                    },
                    iframeSandboxExtras: ['allow-top-navigation', 'allow-popups-to-escape-sandbox']
                });
            } catch (error) {
                console.error("Error embedding dashboard:", error);
            }
            const iframe = document.querySelector("#dashboard iframe");
            iframe.style.width = "100%";
            iframe.style.height = "100%";
        }
        async function fetchGuestTokenFromBackend() {
            try {

                let dash_access_token = localStorage.getItem("dash_access_token");
                if (dash_access_token == null || dash_access_token == "") {
                    window.location.href = "login.html";
                }

                const response = await fetch(`${config.apiUrl}/api/v2/dashboard/token`, {
                    method: 'POST',
                    headers: { "Content-Type": "application/json", "Authorization": `Bearer ${dash_access_token}` },
                });

                if (!response.ok) {
                    throw new Error(`Error fetching guest token: ${response.status}`);
                }

                const guestTokenData = await response.json();
                localStorage.setItem("guest_token", guestTokenData.result.guest_token);

                embedDashboard();

            } catch (error) {
                console.error("Error fetching guest token:", error);
                alert("An error occurred. Please try again.");
            } finally {
                hideLoader(); // Hide the loader after completing the API call
            }
        }
        fetchGuestTokenFromBackend();
    </script>
</body>

</html>