<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>Launch URL</title>
  <script src="https://cdn.jsdelivr.net/npm/fhirclient/build/fhir-client.min.js"></script>
  <script src="../config.js"></script>
</head>

<body>
  <script>

    const auth = () => {
      FHIR.oauth2.authorize({
        clientId: config.epic_clientid,
        iss: config.epic_iss_url,
        // lThe scopes that you request from the EHR
        scope: [
          "openid fhirUser",  // Get the current user
          `${config.epic_scope_base}/Patient.read`,   // Read patient data
          `${config.epic_scope_base}/Encounter.read`,
          `${config.epic_scope_base}/DocumentReference.write`,
          `${config.epic_scope_base}/DocumentReference.read`,
          `${config.epic_scope_base}/Binary.read`,
          `${config.epic_scope_base}/Appointment.read`,
          "launch/patient",
          "launch/encounter",
          "launch/clinicalnotes"
        ].join(" "),
        redirectUri: "callback.html"
      });
    }

  </script>
  <h3>EPIC Standalone App.</h3>
  <button onclick=auth()>
    Launch
  </button>
</body>

</html>