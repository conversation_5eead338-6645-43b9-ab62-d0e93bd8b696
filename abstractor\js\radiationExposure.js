import {
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  // Check if all fields are filled (not used currently but kept for future use)
  Object.entries(data).forEach(([key, field]) => {
    if (key === "verified") return;
    if (!field.value || field.value.toString().trim() === "") {
      // Field is empty
    }
  });

  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

export function renderRadiationExposure(patientData) {
  const container = document.getElementById("radiationExposure");
  container.innerHTML = ""; // Clear container before rendering new tiles
  container.style.position = "relative"; // Ensure proper positioning for absolute elements

  // Render each history intervention field except "verified"
  Object.entries(patientData.radiation_exposure).forEach(([key, value]) => {
    if (key === "verified") return; // Verified will be handled separately

    // Create an outer wrapper to hold the field tile and the modified_by display.
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px";

    // Create the field container for the tile.
    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    // Create and append the label element.
    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = `${value.label}${
        value.metric ? ` (${value.metric})` : ""
      } (${value.field_id})`;
    // Add tooltip with description if available.
    if (value.description) {
      label.setAttribute("title", value.description);
    }
    fieldContainer.appendChild(label);

    // Create the modified_by display element.
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    // Initialize modified_by if not set.
    if (!value.modified_by) {
      value.modified_by = "";
    }
    modifiedByDisplay.textContent = value.modified_by;

    // Process the input based on its type.
    if (value.input_type === "string" || value.input_type === "text") {
      const input = document.createElement("input");
      input.type = "text";
      input.name = key;
      input.placeholder = `Enter ${value.label}`;
      input.value = value.value || "";

      updateTileStyle(fieldContainer, input.value);

      let previousValue = input.value;

      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use validation utilities with temporary callback
        let isValid = false;
        validateStringInput(currentValue, value.field_id, (validatedValue) => {
          isValid = true;
          previousValue = validatedValue;

          // Update model and UI only if value is valid
          patientData.radiation_exposure[key].value = validatedValue;
          patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, validatedValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.radiation_exposure);

          // Update input value if validation modified it
          if (validatedValue !== currentValue) {
            e.target.value = validatedValue;
          }
        });

        // Revert to previous value if validation failed
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      // Check if this field has options (like units)
      if (
        value.options &&
        Array.isArray(value.options) &&
        value.options.length > 0
      ) {
        // Create a container for the input and unit selector
        const inputContainer = document.createElement("div");
        inputContainer.style.display = "flex";
        inputContainer.style.alignItems = "center";
        inputContainer.style.gap = "10px";

        // Add the input to the container
        inputContainer.appendChild(input);

        // Create a unit selector
        const unitSelect = document.createElement("select");
        unitSelect.name = `${key}-unit`;
        unitSelect.style.width = "auto";
        unitSelect.style.minWidth = "80px";

        // Add options to the unit selector
        value.options.forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          // Set selected if unit is already set
          if (value.unit && value.unit === option.value) {
            optionElement.selected = true;
          }
          unitSelect.appendChild(optionElement);
        });

        // Add event listener to update the unit
        unitSelect.addEventListener("change", (e) => {
          const selectedOption = value.options.find(
            (option) => option.id == e.target.value
          );
          patientData.radiation_exposure[key].unit = selectedOption
            ? selectedOption.value
            : "";
          patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.radiation_exposure);
        });

        // Add the unit selector to the container
        inputContainer.appendChild(unitSelect);

        // Add the container to the field container
        fieldContainer.appendChild(inputContainer);
      } else if (value.metric) {
        // If there's a metric but no options, display it next to the input
        const inputContainer = document.createElement("div");
        inputContainer.style.display = "flex";
        inputContainer.style.alignItems = "center";
        inputContainer.style.gap = "10px";

        // Add the input to the container
        inputContainer.appendChild(input);

        // Create a metric display
        const metricDisplay = document.createElement("span");
        metricDisplay.innerText = value.metric;
        metricDisplay.style.color = "#666";

        // Add the metric display to the container
        inputContainer.appendChild(metricDisplay);

        // Add the container to the field container
        fieldContainer.appendChild(inputContainer);
      } else {
        // If no options or metric, just add the input
        fieldContainer.appendChild(input);
      }
    } else if (value.input_type === "date") {
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${key}_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(value.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = key;
      dateInput.value = value.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      updateTileStyle(fieldContainer, dateInput.value);

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        patientData.radiation_exposure[key].value = selectedDate;
        patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, selectedDate);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.radiation_exposure);
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      fieldContainer.appendChild(dateWrapper);
    } else if (value.input_type === "radio") {
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");

      value.options.forEach((option) => {
        const radioWrapper = document.createElement("div");

        const input = document.createElement("input");
        input.type = "radio";
        input.name = key;
        input.value = option;
        input.id = `${key}-${option}`;
        input.checked = value.value === option; // set checked if matches

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            patientData.radiation_exposure[key].value = e.target.value;
            patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(container, patientData.radiation_exposure);
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${option}`);
        optionLabel.innerText = option;
        // Add tooltip if available.
        if (value.description) {
          optionLabel.setAttribute("title", value.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      // Apply initial border styling using the selected radio value.
      updateTileStyle(fieldContainer, value.value || "");
      fieldContainer.appendChild(radioContainer);
    } else if (value.input_type === "select") {
      const select = document.createElement("select");
      select.name = key;

      // Add a default option if no value is set.
      if (!value.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        if (value.description) {
          defaultOption.setAttribute("title", value.description);
        }
        select.appendChild(defaultOption);
      }

      value.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id; // assuming each option has an id
        optionElement.innerText = option.value;
        // Mark as selected if it matches the value.
        if (value.value && value.value === option.value) {
          optionElement.selected = true;
        }
        if (value.description) {
          optionElement.setAttribute("title", value.description);
        }
        select.appendChild(optionElement);
      });

      const selectedOption = value.options.find(
        (option) => option.value === value.value
      );
      updateTileStyle(
        fieldContainer,
        selectedOption ? selectedOption.value : ""
      );

      select.addEventListener("change", (e) => {
        const selectedOpt = value.options.find(
          (option) => option.id == e.target.value
        );
        const newValue = selectedOpt ? selectedOpt.value : "";
        patientData.radiation_exposure[key].value = newValue;
        patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.radiation_exposure);
      });

      fieldContainer.appendChild(select);
    } else if (value.input_type === "multi_select") {
      // Initialize the value as an array if it's not already
      if (!Array.isArray(value.value)) {
        value.value = value.value ? [value.value] : [];
      }

      // Create a simpler dropdown implementation
      // Create dropdown container with relative positioning
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";

      // Make sure all parent containers allow overflow
      fieldContainer.style.overflow = "visible";
      const tileWrapper = fieldContainer.parentElement;
      if (tileWrapper && tileWrapper.classList.contains("tile-wrapper")) {
        tileWrapper.style.overflow = "visible";
      }
      // Also set the container's overflow to visible
      container.style.overflow = "visible";

      // Create dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.cursor = "pointer";
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";
      dropdownHeader.style.backgroundColor = "#fff";

      // Display selected values or placeholder
      const selectedText = document.createElement("span");
      selectedText.classList.add("selected-text");

      // Function to update the selected text display
      const updateSelectedText = () => {
        if (value.value.length === 0) {
          selectedText.textContent = "Select options...";
        } else if (value.value.length === 1) {
          const selectedOption = value.options.find(
            (opt) => opt.value === value.value[0]
          );
          selectedText.textContent = selectedOption
            ? selectedOption.value
            : value.value[0];
        } else {
          selectedText.textContent = `${value.value.length} options selected`;
        }
      };

      updateSelectedText();

      // Add dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create dropdown content (initially hidden)
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      // Position the dropdown directly under the header
      dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
      dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.overflowX = "hidden";
      dropdownContent.style.backgroundColor = "#fff";
      dropdownContent.style.border = "1px solid #ccc";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
      dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

      // Create a checkbox for each option
      value.options.forEach((option) => {
        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.classList.add("checkbox-wrapper");
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";
        checkboxWrapper.style.padding = "8px 12px";
        checkboxWrapper.style.borderBottom = "1px solid #eee";
        checkboxWrapper.style.textAlign = "left";
        checkboxWrapper.style.cursor = "pointer";

        const input = document.createElement("input");
        input.type = "checkbox";
        input.name = `${key}-${option.id}`;
        input.value = option.id;
        input.id = `${key}-${option.id}`;
        input.style.marginRight = "4px"; // Add a small space between checkbox and text

        // Check if this option is in the selected values array
        input.checked = value.value.includes(option.value);

        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(value.value)
            ? [...value.value]
            : [];

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(option.value)) {
              currentValues.push(option.value);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== option.value);
          }

          // Update the model
          patientData.radiation_exposure[key].value = currentValues;
          patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";

          // Update selected text display
          updateSelectedText();

          // Update UI
          updateTileStyle(
            fieldContainer,
            currentValues.length > 0 ? "filled" : ""
          );
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(container, patientData.radiation_exposure);
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${option.id}`);
        optionLabel.style.marginLeft = "0";
        optionLabel.style.display = "inline-block";
        optionLabel.style.whiteSpace = "nowrap";
        optionLabel.style.cursor = "pointer";
        optionLabel.style.flexGrow = "1";

        // Display field_id in parentheses if available
        if (option.field_id) {
          optionLabel.innerText = `${option.value} (${option.field_id})`;
        } else {
          optionLabel.innerText = option.value;
        }

        // Create a wrapper for the checkbox and label to ensure they're tightly aligned
        const inputLabelWrapper = document.createElement("div");
        inputLabelWrapper.style.display = "flex";
        inputLabelWrapper.style.alignItems = "center";
        inputLabelWrapper.style.gap = "0";

        inputLabelWrapper.appendChild(input);
        inputLabelWrapper.appendChild(optionLabel);
        checkboxWrapper.appendChild(inputLabelWrapper);
        dropdownContent.appendChild(checkboxWrapper);
      });

      // Function to position the dropdown content properly
      const positionDropdown = () => {
        const headerRect = dropdownHeader.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // Position dropdown below the header
        dropdownContent.style.top = `${headerRect.bottom}px`;

        // Ensure dropdown doesn't go off the right edge of the screen
        const rightEdge = headerRect.left + 250; // 250px is our dropdown width
        if (rightEdge > viewportWidth) {
          // Align to the right edge of the header instead
          dropdownContent.style.left = `${headerRect.right - 250}px`;
        } else {
          dropdownContent.style.left = `${headerRect.left}px`;
        }

        // Set max height based on available space
        const spaceBelow = viewportHeight - headerRect.bottom;
        const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
        dropdownContent.style.maxHeight = `${maxHeight}px`;
      };

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !dropdownContainer.contains(e.target) &&
          !dropdownContent.contains(e.target)
        ) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
        }
      });

      // Simple function to close the dropdown
      const closeDropdown = () => {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
        window.removeEventListener("scroll", closeDropdown);
      };

      // Clean up event listener when the component is removed
      const cleanupFunc = () => {
        if (document.body.contains(dropdownContent)) {
          document.body.removeChild(dropdownContent);
        }
        // Remove scroll event listener
        window.removeEventListener("scroll", closeDropdown);
      };

      // Store the cleanup function for potential future use
      dropdownContainer.cleanupFunc = cleanupFunc;

      // Add header to container, but add content to document body for better visibility
      dropdownContainer.appendChild(dropdownHeader);

      // Add the dropdown content to the document body when needed
      const showDropdown = () => {
        if (!document.body.contains(dropdownContent)) {
          document.body.appendChild(dropdownContent);
        }
        dropdownContent.style.display = "block";
        positionDropdown();
      };

      // Set up the click handler for the dropdown
      dropdownHeader.onclick = (e) => {
        e.stopPropagation();
        const isOpen = dropdownContent.style.display === "block";

        if (!isOpen) {
          showDropdown();
          dropdownArrow.innerHTML = "&#9652;"; // Up arrow
          window.addEventListener("scroll", closeDropdown);
        } else {
          closeDropdown();
        }
      };

      // Update the tile style based on whether any options are selected
      updateTileStyle(fieldContainer, value.value.length > 0 ? "filled" : "");

      fieldContainer.appendChild(dropdownContainer);
    } else if (value.input_type === "multi_input_field") {
      const multiInputContainer = document.createElement("div");
      multiInputContainer.classList.add("multi-input-container");

      // Create radio buttons for options instead of text input and checkbox
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "row";
      radioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
      radioContainer.style.gap = "15px";
      radioContainer.style.width = "100%";

      // Create a container for conditional content that will be shown/hidden
      const conditionalContentContainer = document.createElement("div");
      conditionalContentContainer.classList.add(
        "conditional-content-container"
      );
      conditionalContentContainer.style.marginTop = "10px";
      conditionalContentContainer.style.padding = "10px";
      conditionalContentContainer.style.border = "1px solid #eee";
      conditionalContentContainer.style.borderRadius = "4px";
      conditionalContentContainer.style.display = "none"; // Initially hidden
      conditionalContentContainer.style.width = "100%"; // Full width

      // Function to render conditional content based on selected option
      const renderConditionalContent = (selectedOption) => {
        // Clear previous content
        conditionalContentContainer.innerHTML = "";
        conditionalContentContainer.style.display = "none";

        let conditionalKey = null;

        // Determine which conditional key to use based on selected option
        if (selectedOption === "Yes" && value.if_yes) {
          conditionalKey = "if_yes";
        } else if (selectedOption === "No" && value.if_no) {
          conditionalKey = "if_no";
        } else if (selectedOption === "Alive" && value.if_alive) {
          conditionalKey = "if_alive";
        } else if (selectedOption === "Deceased" && value.if_deceased) {
          conditionalKey = "if_deceased";
        }

        // If we have a conditional key, render its content
        if (conditionalKey && value[conditionalKey]) {
          conditionalContentContainer.style.display = "block";

          // Iterate through each field in the conditional content
          Object.entries(value[conditionalKey]).forEach(
            ([subKey, subField]) => {
              // Create a container for this sub-field
              const subFieldContainer = document.createElement("div");
              subFieldContainer.classList.add("sub-field-container");
              subFieldContainer.style.marginBottom = "15px";

              // Create and append the label
              const subLabel = document.createElement("label");
              subLabel.classList.add("label", "cursor-pointer");
              subLabel.textContent = `${subField.label}${
                subField.metric ? ` (${subField.metric})` : ""
              } (${subField.field_id})`;
              if (subField.description) {
                subLabel.setAttribute("title", subField.description);
              }
              subFieldContainer.appendChild(subLabel);

              // Initialize the sub-field value if not set
              if (!subField.value) {
                subField.value = "";
              }

              // Create the appropriate input based on sub-field type
              if (subField.input_type === "radio") {
                const subRadioContainer = document.createElement("div");
                subRadioContainer.classList.add("radio-container");
                subRadioContainer.style.display = "flex";
                subRadioContainer.style.flexDirection = "row";
                subRadioContainer.style.flexWrap = "nowrap";
                subRadioContainer.style.gap = "15px";

                subField.options.forEach((option) => {
                  const radioWrapper = document.createElement("div");
                  radioWrapper.style.display = "inline-flex";
                  radioWrapper.style.alignItems = "center";
                  radioWrapper.style.marginRight = "20px";
                  radioWrapper.style.whiteSpace = "nowrap";

                  const input = document.createElement("input");
                  input.type = "radio";
                  input.name = `${key}-${subKey}`;
                  input.value = option;
                  input.id = `${key}-${subKey}-${option}`;
                  input.checked = subField.value === option;

                  input.addEventListener("change", (e) => {
                    if (e.target.checked) {
                      // Update the sub-field value
                      value[conditionalKey][subKey].value = e.target.value;
                      value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight(
                        container,
                        patientData.radiation_exposure
                      );
                    }
                  });

                  const optionLabel = document.createElement("label");
                  optionLabel.setAttribute("for", `${key}-${subKey}-${option}`);
                  optionLabel.innerText = option;
                  if (subField.description) {
                    optionLabel.setAttribute("title", subField.description);
                  }

                  radioWrapper.appendChild(input);
                  radioWrapper.appendChild(optionLabel);
                  subRadioContainer.appendChild(radioWrapper);
                });

                subFieldContainer.appendChild(subRadioContainer);
              } else if (subField.input_type === "select") {
                const select = document.createElement("select");
                select.name = `${key}-${subKey}`;

                // Add default option if no value is set
                if (!subField.value) {
                  const defaultOption = document.createElement("option");
                  defaultOption.value = "";
                  defaultOption.innerText = "Select an option";
                  defaultOption.disabled = true;
                  defaultOption.selected = true;
                  select.appendChild(defaultOption);
                }

                subField.options.forEach((option) => {
                  const optionElement = document.createElement("option");
                  optionElement.value = option.id;
                  optionElement.innerText = option.value;
                  if (subField.value && subField.value === option.value) {
                    optionElement.selected = true;
                  }
                  select.appendChild(optionElement);
                });

                select.addEventListener("change", (e) => {
                  const selOption = subField.options.find(
                    (option) => option.id === e.target.value
                  );
                  const newValue = selOption ? selOption.value : "";
                  value[conditionalKey][subKey].value = newValue;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.radiation_exposure
                  );
                });

                subFieldContainer.appendChild(select);
              } else if (
                subField.input_type === "string" ||
                subField.input_type === "text"
              ) {
                // Create text input for string/text fields
                const input = document.createElement("input");
                input.type = "text";
                input.name = `${key}-${subKey}`;
                input.placeholder = `Enter ${subField.label}`;
                input.value = subField.value || "";

                let previousValue = input.value;

                input.addEventListener("input", (e) => {
                  const currentValue = e.target.value;

                  // Use validation utilities with temporary callback
                  let isValid = false;
                  validateStringInput(
                    currentValue,
                    subField.field_id,
                    (validatedValue) => {
                      isValid = true;
                      previousValue = validatedValue;

                      // Update model and UI only if value is valid
                      value[conditionalKey][subKey].value = validatedValue;
                      value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight(
                        container,
                        patientData.radiation_exposure
                      );

                      // Update input value if validation modified it
                      if (validatedValue !== currentValue) {
                        e.target.value = validatedValue;
                      }
                    }
                  );

                  // Revert to previous value if validation failed
                  if (!isValid) {
                    e.target.value = previousValue;
                  }
                });

                subFieldContainer.appendChild(input);
              } else if (subField.input_type === "date") {
                // Create date input
                const dateWrapper = document.createElement("div");
                dateWrapper.style.position = "relative";

                // Create display input
                const displayInput = document.createElement("input");
                displayInput.type = "text";
                displayInput.name = `${key}-${subKey}-display`;
                displayInput.readOnly = true;
                displayInput.value = formatDisplayDate(subField.value);
                displayInput.placeholder = "MM/DD/YYYY";
                displayInput.style.cursor = "pointer";

                // Hidden date input
                const dateInput = document.createElement("input");
                dateInput.type = "date";
                dateInput.name = `${key}-${subKey}`;
                dateInput.value = subField.value || "";
                dateInput.style.position = "absolute";
                dateInput.style.opacity = "0";
                dateInput.style.cursor = "pointer";

                // Set max date to today
                const today = new Date().toISOString().split("T")[0];
                dateInput.max = today;

                dateInput.addEventListener("change", (e) => {
                  const selectedDate = e.target.value;
                  displayInput.value = formatDisplayDate(selectedDate);
                  value[conditionalKey][subKey].value = selectedDate;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.radiation_exposure
                  );
                });

                // Trigger date picker when clicking display input
                displayInput.addEventListener("click", () => {
                  dateInput.showPicker();
                });

                dateWrapper.appendChild(displayInput);
                dateWrapper.appendChild(dateInput);
                subFieldContainer.appendChild(dateWrapper);
              } else if (subField.input_type === "multi_input_field") {
                // Handle nested multi_input_field
                const nestedMultiInputContainer = document.createElement("div");
                nestedMultiInputContainer.classList.add(
                  "multi-input-container"
                );
                nestedMultiInputContainer.style.marginTop = "10px";

                // Create radio buttons for the nested multi_input_field
                const nestedRadioContainer = document.createElement("div");
                nestedRadioContainer.classList.add("radio-container");
                nestedRadioContainer.style.display = "flex";
                nestedRadioContainer.style.flexDirection = "row";
                nestedRadioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
                nestedRadioContainer.style.gap = "15px";
                nestedRadioContainer.style.width = "100%";

                // Create a single container for nested conditional content
                const nestedConditionalContainer =
                  document.createElement("div");
                nestedConditionalContainer.classList.add(
                  "conditional-content-container"
                );
                nestedConditionalContainer.style.marginTop = "10px";
                nestedConditionalContainer.style.padding = "10px";
                nestedConditionalContainer.style.border = "1px solid #eee";
                nestedConditionalContainer.style.borderRadius = "4px";
                nestedConditionalContainer.style.display = "none";
                nestedConditionalContainer.style.width = "100%";

                // Function to update nested value
                const updateNestedValue = (newValue) => {
                  value[conditionalKey][subKey].value = newValue;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.radiation_exposure
                  );
                };

                // Create radio buttons for each option in the nested field
                if (subField.options && Array.isArray(subField.options)) {
                  subField.options.forEach((option) => {
                    // Create a wrapper for each radio option
                    const optionWrapper = document.createElement("div");
                    optionWrapper.classList.add("option-wrapper");
                    optionWrapper.style.display = "inline-flex"; // Use inline-flex to keep in one line
                    optionWrapper.style.marginRight = "20px";
                    optionWrapper.style.minWidth = "fit-content";

                    // Create the radio button wrapper
                    const radioWrapper = document.createElement("div");
                    radioWrapper.style.display = "flex";
                    radioWrapper.style.alignItems = "center";
                    radioWrapper.style.whiteSpace = "nowrap";

                    // Create the radio input
                    const input = document.createElement("input");
                    input.type = "radio";
                    input.name = `${key}-${subKey}`;
                    input.value = option;
                    input.id = `${key}-${subKey}-${option}`;
                    input.checked = subField.value === option;

                    // Create the label
                    const optionLabel = document.createElement("label");
                    optionLabel.setAttribute(
                      "for",
                      `${key}-${subKey}-${option}`
                    );
                    optionLabel.innerText = option;
                    if (subField.description) {
                      optionLabel.setAttribute("title", subField.description);
                    }

                    // Add radio and label to the radio wrapper
                    radioWrapper.appendChild(input);
                    radioWrapper.appendChild(optionLabel);

                    // Update data model and render nested conditional content
                    input.addEventListener("change", (e) => {
                      if (e.target.checked) {
                        // Update the nested field value
                        updateNestedValue(e.target.value);

                        // Always clear any previously rendered content first
                        nestedConditionalContainer.innerHTML = "";
                        nestedConditionalContainer.style.display = "none";

                        // Check if this option has conditional content to display
                        let hasConditionalContent = false;
                        if (e.target.value === "Yes" && subField.if_yes) {
                          hasConditionalContent = true;
                        } else if (e.target.value === "No" && subField.if_no) {
                          hasConditionalContent = true;
                        } else if (
                          e.target.value === "Alive" &&
                          subField.if_alive
                        ) {
                          hasConditionalContent = true;
                        } else if (
                          e.target.value === "Deceased" &&
                          subField.if_deceased
                        ) {
                          hasConditionalContent = true;
                        }

                        // Only render if there's conditional content to show
                        if (hasConditionalContent) {
                          // Render nested conditional content based on the selected option
                          // This will show if_yes content for Yes, if_no for No, etc.
                          renderConditionalContent(
                            e.target.value,
                            subField,
                            nestedConditionalContainer,
                            subKey,
                            [{ key: key, conditionalKey, subKey }]
                          );
                        }
                      }
                    });

                    // Add radio wrapper to the option wrapper
                    optionWrapper.appendChild(radioWrapper);

                    // Add the option wrapper to the radio container
                    nestedRadioContainer.appendChild(optionWrapper);

                    // Render initial conditional content if this option is selected
                    if (subField.value === option) {
                      // Check if this option has conditional content to display
                      let hasConditionalContent = false;
                      if (option === "Yes" && subField.if_yes) {
                        hasConditionalContent = true;
                      } else if (option === "No" && subField.if_no) {
                        hasConditionalContent = true;
                      } else if (option === "Alive" && subField.if_alive) {
                        hasConditionalContent = true;
                      } else if (
                        option === "Deceased" &&
                        subField.if_deceased
                      ) {
                        hasConditionalContent = true;
                      }

                      // Only render if there's conditional content to show
                      if (hasConditionalContent) {
                        renderConditionalContent(
                          option,
                          subField,
                          nestedConditionalContainer,
                          subKey,
                          [{ key: key, conditionalKey, subKey }]
                        );
                      }
                    }
                  });
                }

                // Add radio container and conditional container to the multi-input container
                nestedMultiInputContainer.appendChild(nestedRadioContainer);
                nestedMultiInputContainer.appendChild(
                  nestedConditionalContainer
                );
                subFieldContainer.appendChild(nestedMultiInputContainer);
              } else if (subField.input_type === "multi_select") {
                // Initialize the value as an array if it's not already
                if (!Array.isArray(subField.value)) {
                  subField.value = subField.value ? [subField.value] : [];
                }

                // Create dropdown container with relative positioning
                const dropdownContainer = document.createElement("div");
                dropdownContainer.classList.add("dropdown-container");
                dropdownContainer.style.position = "relative";
                dropdownContainer.style.width = "100%";

                // Make sure all parent containers allow overflow
                subFieldContainer.style.overflow = "visible";

                // Create dropdown header/button
                const dropdownHeader = document.createElement("div");
                dropdownHeader.classList.add("dropdown-header");
                dropdownHeader.style.padding = "8px 12px";
                dropdownHeader.style.border = "1px solid #ccc";
                dropdownHeader.style.borderRadius = "4px";
                dropdownHeader.style.cursor = "pointer";
                dropdownHeader.style.display = "flex";
                dropdownHeader.style.justifyContent = "space-between";
                dropdownHeader.style.alignItems = "center";
                dropdownHeader.style.backgroundColor = "#fff";

                // Display selected values or placeholder
                const selectedText = document.createElement("span");
                selectedText.classList.add("selected-text");

                // Function to update the selected text display
                const updateSelectedText = () => {
                  if (subField.value.length === 0) {
                    selectedText.textContent = "Select options...";
                  } else if (subField.value.length === 1) {
                    const selectedOption = subField.options.find(
                      (opt) => opt.value === subField.value[0]
                    );
                    selectedText.textContent = selectedOption
                      ? selectedOption.value
                      : subField.value[0];
                  } else {
                    selectedText.textContent = `${subField.value.length} options selected`;
                  }
                };

                updateSelectedText();

                // Add dropdown arrow
                const dropdownArrow = document.createElement("span");
                dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

                dropdownHeader.appendChild(selectedText);
                dropdownHeader.appendChild(dropdownArrow);

                // Create dropdown content (initially hidden)
                const dropdownContent = document.createElement("div");
                dropdownContent.classList.add("dropdown-content");
                dropdownContent.style.display = "none";
                // Position the dropdown directly under the header
                dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
                dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
                dropdownContent.style.maxHeight = "200px";
                dropdownContent.style.overflowY = "auto";
                dropdownContent.style.overflowX = "hidden";
                dropdownContent.style.backgroundColor = "#fff";
                dropdownContent.style.border = "1px solid #ccc";
                dropdownContent.style.borderRadius = "4px";
                dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
                dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

                // Create a checkbox for each option
                subField.options.forEach((option) => {
                  const checkboxWrapper = document.createElement("div");
                  checkboxWrapper.classList.add("checkbox-wrapper");
                  checkboxWrapper.style.display = "flex";
                  checkboxWrapper.style.alignItems = "center";
                  checkboxWrapper.style.padding = "8px 12px";
                  checkboxWrapper.style.borderBottom = "1px solid #eee";
                  checkboxWrapper.style.textAlign = "left";
                  checkboxWrapper.style.cursor = "pointer";

                  const input = document.createElement("input");
                  input.type = "checkbox";
                  input.name = `${key}-${subKey}-${option.id}`;
                  input.value = option.id;
                  input.id = `${key}-${subKey}-${option.id}`;
                  input.style.marginRight = "4px"; // Add a small space between checkbox and text

                  // Check if this option is in the selected values array
                  input.checked = subField.value.includes(option.value);

                  input.addEventListener("change", (e) => {
                    // Get the current values array
                    let currentValues = Array.isArray(subField.value)
                      ? [...subField.value]
                      : [];

                    if (e.target.checked) {
                      // Add the value if it's not already in the array
                      if (!currentValues.includes(option.value)) {
                        currentValues.push(option.value);
                      }
                    } else {
                      // Remove the value if it's in the array
                      currentValues = currentValues.filter(
                        (val) => val !== option.value
                      );
                    }

                    // Update the model
                    value[conditionalKey][subKey].value = currentValues;
                    value[conditionalKey][subKey].modified_by = "ABSTRACTOR";

                    // Update selected text display
                    updateSelectedText();

                    // Update UI
                    modifiedByDisplay.textContent = "ABSTRACTOR";
                    updateContainerHighlight(
                      container,
                      patientData.radiation_exposure
                    );
                  });

                  const optionLabel = document.createElement("label");
                  optionLabel.setAttribute(
                    "for",
                    `${key}-${subKey}-${option.id}`
                  );
                  optionLabel.style.marginLeft = "0";
                  optionLabel.style.display = "inline-block";
                  optionLabel.style.whiteSpace = "nowrap";
                  optionLabel.style.cursor = "pointer";
                  optionLabel.style.flexGrow = "1";

                  // Display field_id in parentheses if available
                  if (option.field_id) {
                    optionLabel.innerText = `${option.value} (${option.field_id})`;
                  } else {
                    optionLabel.innerText = option.value;
                  }

                  // Create a wrapper for the checkbox and label to ensure they're tightly aligned
                  const inputLabelWrapper = document.createElement("div");
                  inputLabelWrapper.style.display = "flex";
                  inputLabelWrapper.style.alignItems = "center";
                  inputLabelWrapper.style.gap = "0";

                  inputLabelWrapper.appendChild(input);
                  inputLabelWrapper.appendChild(optionLabel);
                  checkboxWrapper.appendChild(inputLabelWrapper);
                  dropdownContent.appendChild(checkboxWrapper);
                });

                // Function to position the dropdown content properly
                const positionDropdown = () => {
                  const headerRect = dropdownHeader.getBoundingClientRect();
                  const viewportHeight = window.innerHeight;
                  const viewportWidth = window.innerWidth;

                  // Position dropdown below the header
                  dropdownContent.style.top = `${headerRect.bottom}px`;

                  // Ensure dropdown doesn't go off the right edge of the screen
                  const rightEdge = headerRect.left + 250; // 250px is our dropdown width
                  if (rightEdge > viewportWidth) {
                    // Align to the right edge of the header instead
                    dropdownContent.style.left = `${headerRect.right - 250}px`;
                  } else {
                    dropdownContent.style.left = `${headerRect.left}px`;
                  }

                  // Set max height based on available space
                  const spaceBelow = viewportHeight - headerRect.bottom;
                  const maxHeight = Math.max(
                    100,
                    Math.min(200, spaceBelow - 20)
                  );
                  dropdownContent.style.maxHeight = `${maxHeight}px`;
                };

                // Close dropdown when clicking outside
                document.addEventListener("click", (e) => {
                  if (
                    !dropdownContainer.contains(e.target) &&
                    !dropdownContent.contains(e.target)
                  ) {
                    dropdownContent.style.display = "none";
                    dropdownArrow.innerHTML = "&#9662;";
                  }
                });

                // Simple function to close the dropdown
                const closeDropdown = () => {
                  dropdownContent.style.display = "none";
                  dropdownArrow.innerHTML = "&#9662;";
                  window.removeEventListener("scroll", closeDropdown);
                };

                // Clean up event listener when the component is removed
                const cleanupFunc = () => {
                  if (document.body.contains(dropdownContent)) {
                    document.body.removeChild(dropdownContent);
                  }
                  // Remove scroll event listener
                  window.removeEventListener("scroll", closeDropdown);
                };

                // Store the cleanup function for potential future use
                dropdownContainer.cleanupFunc = cleanupFunc;

                // Add header to container, but add content to document body for better visibility
                dropdownContainer.appendChild(dropdownHeader);

                // Add the dropdown content to the document body when needed
                const showDropdown = () => {
                  if (!document.body.contains(dropdownContent)) {
                    document.body.appendChild(dropdownContent);
                  }
                  dropdownContent.style.display = "block";
                  positionDropdown();
                };

                // Set up the click handler for the dropdown
                dropdownHeader.onclick = (e) => {
                  e.stopPropagation();
                  const isOpen = dropdownContent.style.display === "block";

                  if (!isOpen) {
                    showDropdown();
                    dropdownArrow.innerHTML = "&#9652;"; // Up arrow
                    window.addEventListener("scroll", closeDropdown);
                  } else {
                    closeDropdown();
                  }
                };

                subFieldContainer.appendChild(dropdownContainer);
              }

              conditionalContentContainer.appendChild(subFieldContainer);
            }
          );
        }
      };

      // Create radio buttons for each option
      if (value.options && Array.isArray(value.options)) {
        value.options.forEach((option) => {
          const radioWrapper = document.createElement("div");
          radioWrapper.style.display = "inline-flex"; // Use inline-flex to keep in one line
          radioWrapper.style.alignItems = "center";
          radioWrapper.style.marginRight = "20px";
          radioWrapper.style.whiteSpace = "nowrap";

          const input = document.createElement("input");
          input.type = "radio";
          input.name = key;
          input.value = option;
          input.id = `${key}-${option}`;
          input.checked = value.value === option;

          // Update patientData and render conditional content when a radio is selected
          input.addEventListener("change", (e) => {
            if (e.target.checked) {
              patientData.radiation_exposure[key].value = e.target.value;
              patientData.radiation_exposure[key].modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, e.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.radiation_exposure
              );

              // Always clear any previously rendered content first
              conditionalContentContainer.innerHTML = "";
              conditionalContentContainer.style.display = "none";

              // Check if this option has conditional content to display
              let hasConditionalContent = false;
              if (e.target.value === "Yes" && value.if_yes) {
                hasConditionalContent = true;
              } else if (e.target.value === "No" && value.if_no) {
                hasConditionalContent = true;
              } else if (e.target.value === "Alive" && value.if_alive) {
                hasConditionalContent = true;
              } else if (e.target.value === "Deceased" && value.if_deceased) {
                hasConditionalContent = true;
              }

              // Only render if there's conditional content to show
              if (hasConditionalContent) {
                // Render conditional content based on selected option
                renderConditionalContent(e.target.value);
              }
            }
          });

          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${key}-${option}`);
          optionLabel.innerText = option;
          if (value.description) {
            optionLabel.setAttribute("title", value.description);
          }

          radioWrapper.appendChild(input);
          radioWrapper.appendChild(optionLabel);
          radioContainer.appendChild(radioWrapper);
        });
      }

      // Apply initial style based on the selected option value
      updateTileStyle(fieldContainer, value.value || "");

      // Render initial conditional content if a value is already selected
      if (value.value) {
        // Always clear any previously rendered content first
        conditionalContentContainer.innerHTML = "";
        conditionalContentContainer.style.display = "none";

        // Check if this option has conditional content to display
        let hasConditionalContent = false;
        if (value.value === "Yes" && value.if_yes) {
          hasConditionalContent = true;
        } else if (value.value === "No" && value.if_no) {
          hasConditionalContent = true;
        } else if (value.value === "Alive" && value.if_alive) {
          hasConditionalContent = true;
        } else if (value.value === "Deceased" && value.if_deceased) {
          hasConditionalContent = true;
        }

        // Only render if there's conditional content to show
        if (hasConditionalContent) {
          // Render conditional content based on selected option
          renderConditionalContent(value.value);
        }
      }

      // Add elements to container
      multiInputContainer.appendChild(radioContainer);
      multiInputContainer.appendChild(conditionalContentContainer);
      fieldContainer.appendChild(multiInputContainer);
    }

    // Append the field container and the modified_by display to the tile wrapper.
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);

    // Append the complete tile to the main container.
    container.appendChild(tileWrapper);
  });

  // Integrate the verified checkbox.
  // Ensure the verified field exists as an object.
  if (
    typeof patientData.radiation_exposure.verified !== "object" ||
    patientData.radiation_exposure.verified === null
  ) {
    patientData.radiation_exposure.verified = {
      value: patientData.radiation_exposure.verified || "False",
      modified_by: "",
    };
  }
  const verifiedData = patientData.radiation_exposure.verified;

  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";
  verifiedContainer.style.marginTop = "20px";
  verifiedContainer.style.paddingTop = "10px";

  const verifiedCheckbox = document.createElement("input");
  verifiedCheckbox.type = "checkbox";
  verifiedCheckbox.id = "radiation-exposure-verified-checkbox";
  verifiedCheckbox.checked = verifiedData.value === "True";
  verifiedCheckbox.style.width = "24px";
  verifiedCheckbox.style.height = "24px";

  verifiedCheckbox.addEventListener("change", (e) => {
    verifiedData.value = e.target.checked ? "True" : "False";
    updateContainerHighlight(container, patientData.radiation_exposure);
  });

  // Create label for the verified checkbox.
  const verifiedLabel = document.createElement("label");
  verifiedLabel.setAttribute("for", "radiation-exposure-verified-checkbox");
  verifiedLabel.classList.add("mt-2", "ml-2");
  verifiedLabel.innerText = "Verified";
  // Make the text bigger
  verifiedLabel.style.fontSize = "18px";
  verifiedLabel.style.fontWeight = "bold";

  verifiedContainer.appendChild(verifiedCheckbox);
  verifiedContainer.appendChild(verifiedLabel);
  container.appendChild(verifiedContainer);

  // Initial update for container highlight.
  updateContainerHighlight(container, patientData.radiation_exposure);
}
