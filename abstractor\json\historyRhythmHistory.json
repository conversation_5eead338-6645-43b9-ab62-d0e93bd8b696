{"history_rhythm_history": {"atrial_fibrillation": {"field_id": "13709", "label": "Atrial Fibrillation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "atrial_fibrillation_classification": {"field_id": "4400", "label": "Atrial Fibrillation Classification", "options": [{"id": "1", "value": "Paroxysmal"}, {"id": "2", "value": "Persistent"}, {"id": "3", "value": "Long standing persistent"}, {"id": "4", "value": "Permanent"}], "input_type": "select", "value": "id"}, "valvular_atrial_fibrillation": {"field_id": "4380", "label": "Valvular Atrial Fibrillation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "hx_of_rheumatic_valve_disease": {"field_id": "14799", "label": "Hx of Rheumatic Valve Disease", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "hx_of_mitral_valve_replacement": {"field_id": "4385", "label": "Hx of Mitral Valve Replacement", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "mechanical_valve_in_mitral_position": {"field_id": "4390", "label": "Mechanical Valve in Mitral Position", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "hx_of_mitral_valve_repair": {"field_id": "4395", "label": "Hx of Mitral Valve Repair", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "attempt_at_atrial_fibrillation_termination": {"field_id": "4410", "label": "Attempt at Atrial Fibrillation Termination", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "pharmacologic_cardioversion": {"field_id": "4415", "label": "Pharmacologic Cardioversion", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "dc_cardioversion": {"field_id": "4420", "label": "DC Cardioversion", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "catheter_ablation": {"field_id": "4425", "label": "Catheter Ablation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_catheter_ablation_date": {"field_id": "4430", "label": "Most Recent Catheter Ablation Date", "options": [], "input_type": "string", "value": ""}, "prior_ablation_strategy": {"field_id": "4435", "label": "Prior Ablation Strategy(s)", "options": [], "input_type": "string", "value": ""}, "surgical_ablation": {"field_id": "4440", "label": "Surgical Ablation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_surgical_ablation_date": {"field_id": "4445", "label": "Most Recent Surgical Ablation Date", "options": [], "input_type": "string", "value": ""}, "atrial_flutter": {"field_id": "4450", "label": "Atrial Flutter", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "atrial_flutter_classification": {"field_id": "4455", "label": "Atrial Flutter Classification", "options": [{"id": "1", "value": "Typical/Cavotricuspid Isthmus (CTI) Dependent"}, {"id": "2", "value": "Atypical"}], "input_type": "select", "value": "id"}, "attempt_at_atrial_flutter_termination": {"field_id": "4460", "label": "Attempt at Atrial Flutter Termination", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "pharmacologic_cardioversion_flutter": {"field_id": "4465", "label": "Pharmacologic Cardioversion (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "dc_cardioversion_flutter": {"field_id": "4470", "label": "DC Cardioversion (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "catheter_ablation_flutter": {"field_id": "4475", "label": "Catheter Ablation (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_catheter_ablation_date_flutter": {"field_id": "4480", "label": "Most Recent Catheter Ablation Date (Flutter)", "options": [], "input_type": "string", "value": ""}}}