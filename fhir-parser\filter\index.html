<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="../fav.jpg" type="image/jpeg">

    <!-- For Apple devices (optional) -->
    <link rel="apple-touch-icon" href="../fav.jpg">

    <!-- For Android and Chrome (optional) -->
    <meta name="theme-color" content="#8143D9">
    <title>FHIR Resource Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .upload-section {
            text-align: center;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .controls {
            margin-bottom: 20px;
        }

        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
            font-size: 14px;
            width: 200px;
        }

        .resource-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .resource-header {
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        .key-value-pair {
            display: grid;
            grid-template-columns: 200px 1fr;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }

        .key {
            font-weight: bold;
            color: #555;
        }

        .value {
            word-break: break-word;
        }

        .array-container {
            margin-left: 20px;
            border-left: 2px solid #ddd;
            padding-left: 10px;
        }

        .array-item {
            margin: 5px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .array-header {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .sub-item {
            margin: 3px 0 3px 15px;
            padding: 2px 0;
            display: grid;
            grid-template-columns: 180px 1fr;
            gap: 10px;
        }

        .sub-key {
            color: #666;
            font-size: 0.9em;
        }

        .error {
            color: red;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid red;
            border-radius: 4px;
            display: none;
        }

        .stats {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        @media print {

            .no-print,
            .no-print * {
                display: none !important;
            }
        }
    </style>
</head>

<body>
    <div class="container">

        <div class="no-print">
            <h1>FHIR Resource Viewer</h1>

            <div style="margin: 25px;">
                <button onclick="print()">Print</button>
            </div>

            <div class="upload-section">
                <h3>Upload FHIR JSON File</h3>
                <input type="file" id="fileInput" accept=".json">
            </div>

            <div class="error" id="errorMessage"></div>

            <div class="stats" id="statsSection" style="display: none;">
                <h3>File Statistics</h3>
                <div id="statsContent"></div>
            </div>

            <div class="controls">
                <label for="resourceType">Resource Type:</label>
                <select id="resourceType"></select>
            </div>
        </div>



        <div id="resourceViewer"></div>
    </div>

    <script>
        let fhirData = null;
        let resourcesByType = {};

        document.getElementById('fileInput').addEventListener('change', handleFileUpload);
        document.getElementById('resourceType').addEventListener('change', handleResourceTypeChange);

        function handleFileUpload(event) {
            const file = event.target.files[0];
            const reader = new FileReader();

            reader.onload = function (e) {
                try {
                    fhirData = JSON.parse(e.target.result);
                    processAndDisplayData();
                } catch (error) {
                    showError('Invalid JSON file. Please upload a valid FHIR JSON file.');
                }
            };

            reader.readAsText(file);
        }

        function processAndDisplayData() {
            if (!fhirData || !fhirData.entry) {
                showError('Invalid FHIR format. The file must contain an "entry" array.');
                return;
            }

            resourcesByType = {};
            fhirData.entry.forEach(entry => {
                const resource = entry.resource;
                if (resource && resource.resourceType) {
                    if (!resourcesByType[resource.resourceType]) {
                        resourcesByType[resource.resourceType] = [];
                    }
                    resourcesByType[resource.resourceType].push(resource);
                }
            });

            updateResourceTypeDropdown();
            displayStatistics();
            hideError();
        }

        function updateResourceTypeDropdown() {
            const resourceTypeSelect = document.getElementById('resourceType');
            resourceTypeSelect.innerHTML = '<option value="">Select a resource type...</option>';
            Object.keys(resourcesByType).sort().forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = `${type} (${resourcesByType[type].length})`;
                resourceTypeSelect.appendChild(option);
            });
        }

        function displayStatistics() {
            const statsSection = document.getElementById('statsSection');
            const statsContent = document.getElementById('statsContent');

            const totalResources = Object.values(resourcesByType)
                .reduce((sum, resources) => sum + resources.length, 0);

            let statsHTML = `
                <p><strong>Total Resources:</strong> ${totalResources}</p>
                <p><strong>Resource Types:</strong> ${Object.keys(resourcesByType).length}</p>
                <p><strong>Available Types:</strong></p>
                <ul>
                    ${Object.entries(resourcesByType)
                    .map(([type, resources]) =>
                        `<li>${type}: ${resources.length} resources</li>`)
                    .join('')}
                </ul>
            `;

            statsContent.innerHTML = statsHTML;
            statsSection.style.display = 'block';
        }

        function handleResourceTypeChange(event) {
            const resourceType = event.target.value;
            if (!resourceType) {
                document.getElementById('resourceViewer').innerHTML = '';
                return;
            }

            const resources = resourcesByType[resourceType] || [];
            displayResourceDetails(resources);
        }

        function displayResourceDetails(resources) {
            const viewer = document.getElementById('resourceViewer');
            viewer.innerHTML = '';

            resources.forEach((resource, index) => {
                const section = document.createElement('div');
                section.className = 'resource-section';

                const header = document.createElement('div');
                header.className = 'resource-header';
                header.textContent = `${resource.resourceType} ${index + 1}`;
                section.appendChild(header);

                const content = document.createElement('div');
                content.className = 'resource-content';

                processObject(resource, content);
                section.appendChild(content);
                viewer.appendChild(section);
            });
        }

        function processObject(obj, container, prefix = '') {
            for (let key in obj) {
                if (obj.hasOwnProperty(key) && key !== 'resourceType') {
                    const value = obj[key];
                    const fullKey = prefix ? `${prefix}.${key}` : key;

                    if (Array.isArray(value)) {
                        displayArray(fullKey, value, container);
                    } else if (typeof value === 'object' && value !== null) {
                        displayObject(fullKey, value, container);
                    } else {
                        displaySimpleKeyValue(fullKey, value, container);
                    }
                }
            }
        }

        function displayArray(key, array, container) {
            const arrayContainer = document.createElement('div');
            arrayContainer.className = 'key-value-pair';

            const keyElem = document.createElement('div');
            keyElem.className = 'key';
            keyElem.textContent = key;

            const valueContainer = document.createElement('div');
            valueContainer.className = 'array-container';

            array.forEach((item, index) => {
                const itemContainer = document.createElement('div');
                itemContainer.className = 'array-item';

                const itemHeader = document.createElement('div');
                itemHeader.className = 'array-header';
                itemHeader.textContent = `Item ${index + 1}`;
                itemContainer.appendChild(itemHeader);

                if (typeof item === 'object' && item !== null) {
                    processObject(item, itemContainer);
                } else {
                    const textValue = document.createElement('div');
                    textValue.textContent = formatValue(item);
                    itemContainer.appendChild(textValue);
                }

                valueContainer.appendChild(itemContainer);
            });

            arrayContainer.appendChild(keyElem);
            arrayContainer.appendChild(valueContainer);
            container.appendChild(arrayContainer);
        }

        function displayObject(key, obj, container) {
            const pair = document.createElement('div');
            pair.className = 'key-value-pair';

            const keyElem = document.createElement('div');
            keyElem.className = 'key';
            keyElem.textContent = key;

            const valueContainer = document.createElement('div');
            valueContainer.className = 'array-container';

            Object.entries(obj).forEach(([subKey, subValue]) => {
                if (Array.isArray(subValue)) {
                    displayArray(subKey, subValue, valueContainer);
                } else if (typeof subValue === 'object' && subValue !== null) {
                    displayObject(subKey, subValue, valueContainer);
                } else {
                    displaySimpleKeyValue(subKey, subValue, valueContainer);
                }
            });

            pair.appendChild(keyElem);
            pair.appendChild(valueContainer);
            container.appendChild(pair);
        }

        function displaySimpleKeyValue(key, value, container) {
            const pair = document.createElement('div');
            pair.className = 'key-value-pair';

            const keyElem = document.createElement('div');
            keyElem.className = 'key';
            keyElem.textContent = key;

            const valueElem = document.createElement('div');
            valueElem.className = 'value';
            valueElem.textContent = formatValue(value);

            pair.appendChild(keyElem);
            pair.appendChild(valueElem);
            container.appendChild(pair);
        }

        function formatValue(value) {
            if (value === null) return 'null';
            if (value === undefined) return 'undefined';
            if (typeof value === 'boolean') return value.toString();
            if (typeof value === 'number') return value.toString();
            if (typeof value === 'string') return value;
            if (Array.isArray(value)) return ''; // No direct text, will process below
            if (typeof value === 'object') return ''; // No direct text, will process below
            return value.toString();
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }
    </script>
</body>

</html>