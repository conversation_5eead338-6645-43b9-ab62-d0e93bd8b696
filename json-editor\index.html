<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="../svg/welcome-icon.svg" type="image/x-icon">
    <title>SIte Configuration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/10.1.2/jsoneditor.min.css"
        integrity="sha512-8G+Vb2+10BSrSo+wupdzJIylDLpGtEYniQhp0rsbTigPG7Onn2S08Ai/KEGlxN2Ncx9fGqVHtRehMuOjPb9f8g=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/10.1.2/jsoneditor.min.js"
        integrity="sha512-j78GVmoaez3SedgGMQfYAQ1LFaBGy0KyOJuJKcseERaJt+Ws1p+0gDJso8txo1pq7QGibquf6cGHzl9CgXpGdg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <style>
        .jsoneditor-menu {
            background-color: #8143d9 !important;
            color: white !important;
        }
        .jsoneditor {
            border:  thin solid #8143d9;
        }
    </style>
</head>

<body>
    <div class="absolute top-14 right-6">
        <a href="/ncdr_registry/index.html"
           class="bg-[#8143d9] text-white font-serif py-2 px-4 rounded-lg shadow-md hover:bg-[#6f38c4] transition">
            NCDR
        </a>
    </div>    
    <div class="p-6 h-screen m-2">
        <div class="flex justify-center">
            <div class="bg-white p-6 rounded-lg shadow-lg w-[600px] text-center border border-[#8143d9]">
                <h1 class="text-2xl font-bold text-[#8143d9] font-serif">Site Configuration Editor</h1>
                <input type="file" id="fileInput" accept="application/json" class="hidden">
                <button
                    class="mt-4 bg-[#8143d9] text-white py-2 px-4 rounded-lg shadow-md hover:bg-[#8143d9] transition font-serif"
                    onclick="document.getElementById('fileInput').click()">Upload JSON File</button>
            </div>
        </div>
        <div>
            <div class="h-full">
                <div id="jsonEditor" class="mt-4 p-4 bg-gray-100 border border-gray-300 rounded-lg"></div>
                <div class="flex justify-center">
                    <button id="submitJson"
                        class="mt-4 bg-[#8143d9] text-white py-2 px-6 rounded-lg shadow-md transition font-serif">Submit
                        JSON</button>
                </div>
            </div>
        </div>
    </div>
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="flex flex-col items-center">
            <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-white border-opacity-75"></div>
            <p class="text-white mt-4 text-lg font-semibold font-serif">Loading...</p>
        </div>
    </div>
    

    <script src="../config.js"></script>
    <script src="./js/index.js"></script>
</body>

</html>