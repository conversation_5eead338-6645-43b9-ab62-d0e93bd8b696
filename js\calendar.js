const patientCount = document.getElementById("patient_count");
const monthYear = document.getElementById("monthYear");
const monthYearMobile = document.getElementById("monthYearMobile");
const calendarDays = document.getElementById("calendarDays");
let currentDate = new Date();
const dateTimeMobile = document.getElementById("date_time_mobile");
const weekContainer = document.getElementById("weekContainer");
const mobileAppointmentList = document.getElementById(
  "mobile_appointment_list"
);

// mobile_appointment_list
let selectedDate = foramtDateIdGenerator(currentDate);
let touchStartX = 0;
let touchEndX = 0;
let isAnimating = false;
const thisWeek = generateWeek(currentDate);
renderWeek(thisWeek);

// handle procedure
function groupByProcedureDate(data) {
  const result = [];
  const dateMap = {};
  data.forEach((item) => {
    const date = item.procedure_date;
    if (!dateMap[date]) {
      dateMap[date] = { date: date, data: [] };
      result.push(dateMap[date]);
    }
    dateMap[date].data.push(item);
  });
  return result;
}

// utils
function getMonthStartAndEnd(date) {
  const month = date.getMonth() + 1;
  const firstDay = new Date(date.getFullYear(), month - 1, 1); // First day of the month
  const lastDay = new Date(date.getFullYear(), month, 0); // Last day of the month
  return {
    firstDay,
    lastDay,
  };
}

function convertToAMPMUsingDate(time, dict = true) {
  const date = new Date(`1970-01-01T${time}Z`);
  let hours = date.getUTCHours(); // Use getUTCHours to avoid timezone interference
  let minutes = date.getUTCMinutes();
  const period = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  if (hours === 0) {
    hours = 12; // Handle midnight or noon (12 AM or 12 PM)
  }
  if (dict) {
    const formattedTime = `${hours}:${minutes
      .toString()
      .padStart(2, "0")} ${period}`;
    return formattedTime;
  }
  return {
    hours: `${hours}:${minutes.toString().padStart(2, "0")}`,
    period: period,
  };
}

function formatWeekDate(date) {
  return date.toLocaleDateString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
  });
}

function generateWeek(startDate) {
  const week = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(startDate);
    day.setDate(startDate.getDate() + i);
    week.push(day);
  }
  return week;
}

function foramtDateIdGenerator(date) {
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const year = date.getFullYear();
  return `${month}-${day}-${year}`;
}

function formatDate(year, month, day) {
  const validDate = new Date(year, month, day);
  return `${validDate.getFullYear()}-${String(
    validDate.getMonth() + 1
  ).padStart(2, "0")}-${String(validDate.getDate()).padStart(2, "0")}`;
}

const apiDateFormater = (dateObj) => {
  const date = new Date(dateObj);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based, so add 1
  const day = String(date.getDate()).padStart(2, "0"); // Ensure two digits
  return `${year}-${month}-${day}`;
};

function formatDateToYMD(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Add 1 because months are zero-based
  const day = String(date.getDate()).padStart(2, "0"); // Ensure two digits for day

  return `${year}-${month}-${day}`;
}

// render the mobile week slider
function renderWeek(week) {
  weekContainer.innerHTML = "";
  week.forEach((date) => {
    const dayCard = document.createElement("div");
    dayCard.className =
      "flex flex-col items-center w-10 p-2 rounded-lg cursor-pointer transition duration-300  hover:text-white shrink-0";
    // dayCard.id= foramtDateIdGenerator(date)
    dayCard.innerHTML = `
      <p class="text-xs w-10 h-10 text-[200] flex items-center justify-center rounded-full font-normal	 text-purple-500" id='${foramtDateIdGenerator(
        date
      )}' >${date.getDate()}</p>
      <span class="text-xs text-black font-medium">${date.toLocaleDateString(
        "en-US",
        { weekday: "short" }
      )}</span>
    `;
    monthYearMobile.textContent = date.toLocaleString("default", {
      month: "short",
      year: "numeric",
    });
    dayCard.addEventListener("click", async () => {
      try {
        $("#loader").removeClass("hidden");
        document
          .querySelectorAll("#weekContainer div")
          .forEach((div) =>
            div
              .querySelector("p")
              .classList.remove("bg-purple-500", "!text-white")
          );

        selectedDate = dayCard.querySelector("p").id;
        let [month, day, year] = selectedDate.split("-");
        const date = new Date(new Date(year, month - 1, day));
        const tomorrow = new Date(new Date(year, month - 1, day));
        tomorrow.setDate(tomorrow.getDate + 1);
        const getCases = await fetchschedulerdata(
          apiDateFormater(date),
          apiDateFormater(tomorrow)
        );
        mobileRender(getCases, date);
        dateTimeMobile.textContent = `${date.toLocaleString("default", {
          month: "short",
        })} ${date.getDate()}`;
        dayCard
          .querySelector("p")
          .classList.add("bg-purple-500", "!text-white");
      } catch (error) {
        console.log(error);
      } finally {
        $("#loader").addClass("hidden");
      }
    });

    weekContainer.appendChild(dayCard);
  });
  if (document.getElementById(selectedDate)) {
    document
      .getElementById(selectedDate)
      .classList.add("bg-purple-500", "!text-white");
    const [month, day, year] = selectedDate.split("-");
    const reformattedDate = `${year}-${month}-${day}`;
    const date = new Date(reformattedDate);
    dateTimeMobile.textContent = `${date.toLocaleString("default", {
      month: "short",
    })} ${date.getDate()}`;
    monthYearMobile.textContent = date.toLocaleString("default", {
      month: "short",
      year: "numeric",
    });
  }
}

// Helper: Navigate weeks
function navigateWeek(offset) {
  if (isAnimating) return; // Prevent navigation during animation
  isAnimating = true;

  const direction = offset > 0 ? -1 : 1; // Direction of animation
  weekContainer.style.transform = `translateX(${direction * 100}%)`;

  setTimeout(() => {
    currentDate.setDate(currentDate.getDate() + offset * 7);
    const newWeek = generateWeek(currentDate);
    renderWeek(newWeek);
    weekContainer.style.transition = "none";
    weekContainer.style.transform = "translateX(0)";

    // Force a reflow to reset transition
    void weekContainer.offsetWidth;
    weekContainer.style.transition = "transform 0.3s ease-in-out";
    isAnimating = false;
  }, 300);
}

// Swipe Event Handlers mobile
weekContainer.addEventListener("touchstart", (e) => {
  touchStartX = e.changedTouches[0].screenX;
});

weekContainer.addEventListener("touchend", (e) => {
  touchEndX = e.changedTouches[0].screenX;
  handleSwipeGesture();
});

// Detect swipe direction for mobile
function handleSwipeGesture() {
  const swipeDistance = touchEndX - touchStartX;
  const swipeThreshold = 50; // Minimum swipe distance to trigger navigation
  if (swipeDistance > swipeThreshold) {
    // Swipe right: Previous week
    navigateWeek(-1);
  } else if (swipeDistance < -swipeThreshold) {
    // Swipe left: Next week
    navigateWeek(1);
  }
}

// desktop calendar
function renderCalendar(date) {
  const year = date.getFullYear();
  const month = date.getMonth();
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const prevMonthDays = new Date(year, month, 0).getDate();
  const leadingDays = firstDayOfMonth;
  const totalDays = leadingDays + daysInMonth;
  const trailingDays = totalDays <= 35 ? 35 - totalDays : 42 - totalDays;
  monthYear.textContent = date.toLocaleString("default", {
    month: "long",
    year: "numeric",
  });
  calendarDays.innerHTML = "";
  // Add days from the previous month
  for (let i = leadingDays; i > 0; i--) {
    const prevDay = prevMonthDays - i + 1;
    const prevDate = formatDate(year, month - 1, prevDay);
    const dayDiv = document.createElement("div");
    dayDiv.textContent = prevDay;
    dayDiv.id = prevDate;
    dayDiv.className =
      "h-24 bg-gray-200 text-gray-400 p-4 text-right hover:bg-gray-300 ";
    calendarDays.appendChild(dayDiv);
  }

  // Add days of the current month
  for (let day = 1; day <= daysInMonth; day++) {
    const currentDateId = formatDate(year, month, day);
    const dayDiv = document.createElement("div");
    dayDiv.textContent = day;
    dayDiv.id = currentDateId;
    dayDiv.className =
      "h-24 bg-gray-200 text-gray-700 p-4 text-right hover:bg-gray-300 ";
    calendarDays.appendChild(dayDiv);
  }

  // Add days from the next month
  for (let i = 1; i <= trailingDays; i++) {
    const nextDate = formatDate(year, month + 1, i);
    const dayDiv = document.createElement("div");
    dayDiv.textContent = i;
    dayDiv.id = nextDate;
    dayDiv.className =
      "h-24 bg-gray-200 text-gray-400 p-4 text-right hover:bg-gray-300 ";
    calendarDays.appendChild(dayDiv);
  }
  renderApptData();
}

// desktop calender prev btn
function prevMonth() {
  currentDate.setMonth(currentDate.getMonth() - 1);
  renderCalendar(currentDate);
}

// desktop calender next btn
function nextMonth() {
  currentDate.setMonth(currentDate.getMonth() + 1);
  renderCalendar(currentDate);
}
function getCalendarBounds(year, month) {
  const startDate = new Date(year, month, 1);
  const endDate = new Date(year, month + 1, 0);
  const firstDay = new Date(startDate);
  firstDay.setDate(startDate.getDate() - startDate.getDay());
  const lastDay = new Date(endDate);
  lastDay.setDate(endDate.getDate() + (6 - endDate.getDay()));

  return {
    firstDay,
    lastDay,
  };
}

// render the api data
const renderApptData = async (mobileDate = new Date()) => {
  try {
    $("#loader").removeClass("hidden");
    const { firstDay, lastDay } = getCalendarBounds(
      currentDate.getFullYear(),
      currentDate.getMonth()
    );
    const patientRecord = await fetchschedulerdata(
      apiDateFormater(firstDay),
      apiDateFormater(lastDay)
    );
    // patientCount.textContent = `${patientRecord.count.length} Patients`
    if (
      patientRecord.cases_details &&
      patientRecord.cases_details.length !== 0
    ) {
      // desktop render
      const groupCaseDetail = groupByProcedureDate(patientRecord.cases_details);
      groupCaseDetail.map((item) => {
        const date = new Date(item.date).getDate();
        const element = document.getElementById(item.date);
        element.innerHTML = "";
        element.className =
          "flex flex-row justify-between h-24 bg-gray-200 text-gray-700 p-4 text-right hover:bg-gray-300  ";
        const divTag = document.createElement("div");
        const cases = document.createElement("span");
        divTag.className = "text-left ";
        cases.textContent = `Cases: ${item.data.length}`;
        cases.className = "break-words  font-bold";
        divTag.appendChild(cases);
        element.appendChild(divTag);
        const dateEle = document.createElement("p");
        dateEle.textContent = date;
        element.appendChild(dateEle);
        element.onclick = () => {
          onclickAppointment(item);
        };
      });
    }
    mobileRender(patientRecord);
  } catch (err) {
    console.log(err);
    // console.error(err);
    // localStorage.clear();
    // window.location.href = "/login.html";
  } finally {
    $("#loader").addClass("hidden");
  }
};

function mobileRender(patientRecord, mobileDate = new Date()) {
  try {
    mobileAppointmentList.innerHTML = "";
    const filterData = patientRecord.cases_details.filter(
      (item) => item.procedure_date === formatDateToYMD(mobileDate)
    );
    if (filterData && filterData.length !== 0) {
      filterData.map((item) => {
        mobileAppointmentList.appendChild(createSideBardCard(item));
      });
    } else {
      mobileAppointmentList.innerHTML = "";
      mobileAppointmentList.innerHTML = `
    <div  class="w-full h-full flex items-center justify-center">
    <h1>No Cases Found</h1>
    </div>
    `;
    }
  } catch (err) {
    console.log(err);
    // localStorage.clear();
    // window.location.href = "/login.html";
  }
}

function createSideBardCard(item) {
  // Create main container
  const mainDiv = document.createElement("div");
  mainDiv.className = "flex mt-5 flex-col";

  // Create first child div
  const topDiv = document.createElement("div");
  topDiv.className = "w-full p-2 px-4 bg-[#F1F1FB] rounded-t-lg";

  // Create header container
  const headerDiv = document.createElement("div");
  headerDiv.className = "flex flex-row items-center justify-between";

  // Create site name element
  const siteName = document.createElement("h1");
  siteName.className = "text-md md:text-2xl font-bold text-ellipsis";
  siteName.textContent = item.site.name;

  // Append site name to header
  headerDiv.appendChild(siteName);

  // Add header to topDiv
  topDiv.appendChild(headerDiv);

  // Create rep name paragraph
  const repName = document.createElement("p");
  repName.className = "text-sm md:text-lg";
  repName.textContent = item.rep.name;

  // Add rep name to topDiv
  topDiv.appendChild(repName);

  // Create cases container
  const casesDiv = document.createElement("div");
  casesDiv.className =
    "flex text-purple-600 flex-row items-center justify-start mt-3 gap-x-2";

  // Add user icon
  const userIcon = document.createElement("i");
  userIcon.className = "fa-solid fa-user";
  casesDiv.appendChild(userIcon);

  // Add cases text
  const casesText = document.createElement("p");
  casesText.className = "text-sm";
  casesText.textContent =
    item.cases === 1 ? `case: ${item.cases}` : `cases: ${item.cases}`;
  casesDiv.appendChild(casesText);

  // Add casesDiv to topDiv
  topDiv.appendChild(casesDiv);

  // Create second child div
  const bottomDiv = document.createElement("div");
  bottomDiv.className =
    "bg-purple-200 rounded-b-lg flex flex-row p-2 px-4 items-center justify-between";

  // Create implanting physician name
  const physicianName = document.createElement("h1");
  physicianName.className = "text-sm font-semibold";
  physicianName.textContent = item.implanting_physician.name;

  // Add physician name to bottomDiv
  bottomDiv.appendChild(physicianName);

  // Add both child divs to main container
  mainDiv.appendChild(topDiv);
  mainDiv.appendChild(bottomDiv);

  return mainDiv;
}

const onclickAppointment = (data) => {
  const element = document.getElementById("caseDetail");
  element.innerHTML = "";
  if (
    document.getElementById("navbar").classList.contains("translate-x-full")
  ) {
    document.getElementById("navbar").classList.remove("translate-x-full");
  }
  const div = document.createElement("div");
  div.className = "w-full p-4";
  const header = document.createElement("h1");
  header.className = "text-2xl font-bold";
  header.textContent = new Date(data.date).toLocaleDateString("en-US", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  });
  div.appendChild(header);
  const ele = data.data.map((item) => {
    div.appendChild(createSideBardCard(item));
  });
  element.appendChild(div);
};

document.getElementById("closeNav").addEventListener("click", () => {
  document.getElementById("navbar").classList.toggle("translate-x-full");
});

document.getElementById("monthPicker").addEventListener("change", (event) => {
  const [year, month] = event.target.value.split("-");
  currentDate.setFullYear(year);
  currentDate.setMonth(parseInt(month) - 1); // Month is 0-based (0 = Jan, 11 = Dec)
  const newWeek = generateWeek(currentDate);
  renderWeek(newWeek);
});

renderCalendar(currentDate);

async function addScheduleCases() {
  try {
    document.getElementById("loader").classList.remove("hidden");
    const scheduleCases = document.getElementById("scheduleCases");
    const siteList = await getAllSites();
    const siteEle = document.getElementById("site_id");
    siteEle.innerHTML = "";
    const option = document.createElement("option");
    option.textContent = "Select the Site";
    option.disabled = true;
    option.selected = true;
    siteEle.appendChild(option);
    siteEle.required = true;
    siteList.map((item) => {
      const option = document.createElement("option");
      option.textContent = item.name;
      option.value = item.id;
      siteEle.appendChild(option);
    });
    scheduleCases.classList.remove("hidden");
  } catch (error) {
    console.log(error);
  } finally {
    document.getElementById("loader").classList.add("hidden");
  }
}

document.getElementById("caseDate").addEventListener("focus", function (e) {
  e.target.type = "date";
});

document.getElementById("caseDate").addEventListener("blur", function (e) {
  if (!e.target.value) {
    e.target.type = "text";
    e.target.placeholder = "Select a date";
  }
});

document.getElementById("caseDate").addEventListener("change", (event) => {
  event.preventDefault();
  document.getElementById("site_id").disabled = false;
});

function closeModal() {
  document.getElementById("scheduleCases").classList.add("hidden");
}

document.getElementById("site_id").addEventListener("change", async (event) => {
  try {
    event.preventDefault();
    document.getElementById("loader").classList.remove("hidden");
    let date = document.getElementById("caseDate").value;
    const { value } = event.target;
    const patients = await getPatientsApi(value, date);
    const patientEle = document.getElementById("patientDropdown");
    patientEle.innerHTML = "";
    if (patients && patients.length !== 0) {
      patientEle.disabled = false;
      patientEle.required = true;
      patients.map((item) => {
        const option = document.createElement("option");
        option.textContent = item.patient.name;
        option.value = item.case_id;
        patientEle.appendChild(option);
      });
      return;
    }
    scheduleModalOpen(false, {
      message: "No Patient Found",
    });
  } catch (err) {
    console.log(err);
  } finally {
    document.getElementById("loader").classList.add("hidden");
  }
});

function scheduleModalOpen(success, data) {
  document.getElementById("scheduleModal").classList.remove("hidden");
  const scheduleModal = document.getElementById("scheduleModal");
  const scheduleHeader = document.getElementById("scheduleHeader");
  const scheduleDes = document.getElementById("scheduleDes");
  const scheduleHeaderTitle = document.getElementById("scheduleHeaderTitle");
  const scheduleOkBtn = document.getElementById("scheduleOkBtn");
  if (success) {
    scheduleHeaderTitle.textContent = "REP added Successfully";
    scheduleDes.textContent =
      "The REP has been successfully added to the system.";
    scheduleHeader.classList.remove("bg-red-500");
    scheduleOkBtn.classList.remove("bg-red-500");
    scheduleHeader.classList.add("bg-green-500");
    scheduleOkBtn.classList.add("bg-green-500");
    scheduleModal.classList.remove("hidden");
    document.getElementById("scheduleOkBtn").addEventListener("click", (e) => {
      e.preventDefault();
      document.getElementById("scheduleCases").classList.add("hidden");
      document.getElementById("scheduleModal").classList.add("hidden");
      document.getElementById("scheduleForm").reset();
    });
    return;
  }
  scheduleHeaderTitle.textContent = "Error While Adding the REP";
  scheduleDes.textContent =
    data && data.message ? data.message : "Please Enter the valid data";
  scheduleHeader.classList.remove("bg-green-500");
  scheduleOkBtn.classList.remove("bg-green-500");
  scheduleHeader.classList.add("bg-red-500");
  scheduleOkBtn.classList.add("bg-red-500");
  scheduleModal.classList.remove("hidden");
  document.getElementById("scheduleOkBtn").addEventListener("click", (e) => {
    e.preventDefault();
    document.getElementById("scheduleModal").classList.add("hidden");
  });
  return;
}

// document.getElementById('scheduleForm').addEventListener('submit',(event)=>{
// })

async function addSchedule(event) {
  try {
    event.preventDefault();
    document.getElementById("loader").classList.remove("hidden");
    const caseId = document.getElementById("patientDropdown").value;
    if (caseId) {
      const result = await scheduleCase(caseId);
      scheduleModalOpen(true);
      return;
    }
    scheduleModalOpen(false);
  } catch (err) {
    console.log(err);
    scheduleModalOpen(false, err.message);
  } finally {
    document.getElementById("loader").classList.add("hidden");
  }
}
