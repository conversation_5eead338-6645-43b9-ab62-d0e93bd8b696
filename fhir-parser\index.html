<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="fav.jpg" type="image/jpeg">

    <!-- For Apple devices (optional) -->
    <link rel="apple-touch-icon" href="fav.jpg">

    <!-- For Android and Chrome (optional) -->
    <meta name="theme-color" content="#8143D9">

    <title>FHIR Resource Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .upload-section {
            text-align: center;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .controls {
            margin-bottom: 20px;
        }

        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
            font-size: 14px;
            width: 200px;
        }

        .resource-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .resource-header {
            background-color: #8143D9;
            color: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .resource-item-header {
            background-color: #f0f0f0;
            color: #333;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .resource-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .key-value-pair {
            display: grid;
            grid-template-columns: 200px 1fr;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }

        .key {
            font-weight: bold;
            color: #555;
        }

        .value {
            word-break: break-word;
        }

        .array-container {
            margin-left: 20px;
            border-left: 2px solid #ddd;
            padding-left: 10px;
        }

        .array-item {
            margin: 5px 0;
            padding: 5px;
            background-color: #f1f1f1;
            border-radius: 4px;
        }

        .array-header {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .sub-item {
            margin: 3px 0 3px 15px;
            padding: 2px 0;
            display: grid;
            grid-template-columns: 180px 1fr;
            gap: 10px;
        }

        .sub-key {
            color: #666;
            font-size: 0.9em;
        }

        .error {
            color: red;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid red;
            border-radius: 4px;
            display: none;
        }

        .stats {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        #loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            z-index: 9999;
        }

        .scroll-to-top-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #8143D9;
            color: white;
            border: none;
            border-radius: 50%;
            padding: 15px;
            font-size: 18px;
            cursor: pointer;
            display: none;
            /* Initially hidden */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .scroll-to-top-btn:hover {
            background-color: #000000;
        }
    </style>
</head>

<body>
    <div class="container">

        <div id="loader" class="loader" style="display: none;">
            <p>Loading...</p>
        </div>

        <button id="scrollToTopBtn" class="scroll-to-top-btn" style="display: none;">⬆ Scroll to Top</button>


        <h1>FHIR Resource Viewer</h1>


        <div class="upload-section">
            <h3>Upload FHIR JSON File</h3>
            <input type="file" id="fileInput" accept=".json">
        </div>

        <div class="error" id="errorMessage"></div>

        <div class="stats" id="statsSection" style="display: none;">
            <h3>File Statistics</h3>
            <div id="statsContent"></div>
        </div>

        <div id="resourceViewer"></div>
    </div>

    <script>
        let fhirData = null;
        let resourcesByType = {};

        // Show the "Scroll to Top" button when scrolling down
        window.onscroll = function () {
            const scrollToTopBtn = document.getElementById('scrollToTopBtn');
            if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
                scrollToTopBtn.style.display = 'block'; // Show button
            } else {
                scrollToTopBtn.style.display = 'none'; // Hide button
            }
        };


        // Scroll to top functionality
        document.getElementById('scrollToTopBtn').addEventListener('click', function () {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });


        document.getElementById('fileInput').addEventListener('change', handleFileUpload);

        function handleFileUpload(event) {
            const file = event.target.files[0];
            const reader = new FileReader();

            // Show the loader
            document.getElementById('loader').style.display = 'flex';

            reader.onload = function (e) {
                try {
                    fhirData = JSON.parse(e.target.result);
                    processAndDisplayData();
                } catch (error) {
                    showError('Invalid JSON file. Please upload a valid FHIR JSON file.');
                }
            };

            reader.readAsText(file);
        }


        function processAndDisplayData() {
            if (!fhirData || !fhirData.entry) {
                showError('Invalid FHIR format. The file must contain an "entry" array.');
                document.getElementById('loader').style.display = 'none'; // Hide the loader
                return;
            }

            resourcesByType = {};
            fhirData.entry.forEach(entry => {
                const resource = entry.resource;
                if (resource && resource.resourceType) {
                    if (!resourcesByType[resource.resourceType]) {
                        resourcesByType[resource.resourceType] = [];
                    }
                    resourcesByType[resource.resourceType].push(resource);
                }
            });

            displayStatistics();
            displayGroupedResources();
            hideError();

            // Hide the loader after processing is done
            document.getElementById('loader').style.display = 'none';
        }


        function displayStatistics() {
            const statsSection = document.getElementById('statsSection');
            const statsContent = document.getElementById('statsContent');

            const totalResources = Object.values(resourcesByType)
                .reduce((sum, resources) => sum + resources.length, 0);

            let statsHTML = ` 
        <p><strong>Total Resources:</strong> ${totalResources}</p>
        <p><strong>Resource Types:</strong> ${Object.keys(resourcesByType).length}</p>
        <p><strong>Available Types:</strong></p>
        <ul>
            ${Object.entries(resourcesByType)
                    .map(([type, resources]) =>
                        `<li><a href="#${type}" class="scroll-link">${type}: ${resources.length} resources</a></li>`)
                    .join('')}
        </ul>
    `;

            statsContent.innerHTML = statsHTML;
            statsSection.style.display = 'block';

            // Add smooth scrolling behavior
            document.querySelectorAll('.scroll-link').forEach(link => {
                link.addEventListener('click', function (event) {
                    event.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    document.getElementById(targetId)?.scrollIntoView({ behavior: 'smooth' });
                });
            });
        }


        function displayGroupedResources() {
            const viewer = document.getElementById('resourceViewer');
            viewer.innerHTML = '';

            Object.entries(resourcesByType).forEach(([type, resources]) => {
                const section = document.createElement('div');
                section.className = 'resource-section';
                section.id = type; // Add ID for scrolling

                const header = document.createElement('div');
                header.className = 'resource-header';
                header.textContent = `${type}s`;
                section.appendChild(header);

                resources.forEach((resource, index) => {
                    const resourceHeader = document.createElement('div');
                    resourceHeader.className = 'resource-item-header';
                    resourceHeader.textContent = `${type} ${index + 1}`;
                    section.appendChild(resourceHeader);

                    const content = document.createElement('div');
                    content.className = 'resource-content';
                    processObject(resource, content);
                    section.appendChild(content);
                });

                viewer.appendChild(section);
            });
        }


        function processObject(obj, container, prefix = '') {
            for (let key in obj) {
                if (obj.hasOwnProperty(key) && key !== 'resourceType') {
                    const value = obj[key];
                    const fullKey = prefix ? `${prefix}.${key}` : key;

                    if (Array.isArray(value)) {
                        displayArray(fullKey, value, container);
                    } else if (typeof value === 'object' && value !== null) {
                        displayObject(fullKey, value, container);
                    } else {
                        displaySimpleKeyValue(fullKey, value, container);
                    }
                }
            }
        }

        function displayArray(key, array, container) {
            const arrayContainer = document.createElement('div');
            arrayContainer.className = 'key-value-pair';

            const keyElem = document.createElement('div');
            keyElem.className = 'key';
            keyElem.textContent = key;

            const valueContainer = document.createElement('div');
            valueContainer.className = 'array-container';

            array.forEach((item, index) => {
                const itemContainer = document.createElement('div');
                itemContainer.className = 'array-item';

                const itemHeader = document.createElement('div');
                itemHeader.className = 'array-header';
                itemHeader.textContent = `Item ${index + 1}`;
                itemContainer.appendChild(itemHeader);

                if (typeof item === 'object' && item !== null) {
                    processObject(item, itemContainer);
                } else {
                    const textValue = document.createElement('div');
                    textValue.textContent = formatValue(item);
                    itemContainer.appendChild(textValue);
                }

                valueContainer.appendChild(itemContainer);
            });

            arrayContainer.appendChild(keyElem);
            arrayContainer.appendChild(valueContainer);
            container.appendChild(arrayContainer);
        }

        function displayObject(key, obj, container) {
            const pair = document.createElement('div');
            pair.className = 'key-value-pair';

            const keyElem = document.createElement('div');
            keyElem.className = 'key';
            keyElem.textContent = key;

            const valueContainer = document.createElement('div');
            valueContainer.className = 'array-container';

            Object.entries(obj).forEach(([subKey, subValue]) => {
                if (Array.isArray(subValue)) {
                    displayArray(subKey, subValue, valueContainer);
                } else if (typeof subValue === 'object' && subValue !== null) {
                    displayObject(subKey, subValue, valueContainer);
                } else {
                    displaySimpleKeyValue(subKey, subValue, valueContainer);
                }
            });

            pair.appendChild(keyElem);
            pair.appendChild(valueContainer);
            container.appendChild(pair);
        }

        function displaySimpleKeyValue(key, value, container) {
            const pair = document.createElement('div');
            pair.className = 'key-value-pair';

            const keyElem = document.createElement('div');
            keyElem.className = 'key';
            keyElem.textContent = key;

            const valueElem = document.createElement('div');
            valueElem.className = 'value';
            valueElem.textContent = formatValue(value);

            pair.appendChild(keyElem);
            pair.appendChild(valueElem);
            container.appendChild(pair);
        }

        function formatValue(value) {
            if (value === null) return 'null';
            if (value === undefined) return 'undefined';
            if (typeof value === 'boolean') return value.toString();
            if (typeof value === 'number') return value.toString();
            if (typeof value === 'string') {
                // Check if it's base64 encoded
                if (isBase64(value)) {
                    try {
                        return decodeBase64(value);
                    } catch (e) {
                        return '[Invalid base64 data]';
                    }
                }
                return value;
            }
            if (Array.isArray(value)) return ''; // No direct text, will process below
            if (typeof value === 'object') return ''; // No direct text, will process below
            return value.toString();
        }

        function isBase64(str) {
            // Ensure the string length is greater than 100 characters (or another threshold)
            if (str.length < 100) return false;

            // Check for valid base64 characters (alphanumeric + /, +, =)
            const base64Pattern = /^[A-Za-z0-9+/=]+$/;
            if (!base64Pattern.test(str)) return false;

            // Ensure the length of the string is a multiple of 4 (padding rule)
            if (str.length % 4 !== 0) return false;

            // Optional: You can decode and check if the base64 string is valid
            try {
                // Attempt to decode the base64 string
                atob(str);
                return true;  // If it decodes successfully, it is valid base64
            } catch (e) {
                return false;  // If decoding fails, it's not valid base64
            }
        }


        function decodeBase64(base64Str) {
            // Decode base64 string
            const decodedStr = atob(base64Str);
            return decodedStr;
        }



        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }
    </script>
</body>

</html>