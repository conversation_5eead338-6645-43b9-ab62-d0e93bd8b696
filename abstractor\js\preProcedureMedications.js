import {
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  // Check if the section is verified
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

export function renderPreProcedureMedications(patientData) {
  const container = document.getElementById("preProcedureMedications");
  container.innerHTML = ""; // Clear the container before rendering
  container.style.position = "relative"; // Ensure verified checkbox is positioned within the section
  // Set up grid layout but don't display it yet - this will be controlled by the accordion toggle
  container.style.display = "none"; // Start with accordion closed
  container.dataset.gridDisplay = "grid"; // Store the desired display value for when it's opened
  container.style.gridTemplateColumns = "repeat(3, 1fr)"; // Three columns grid
  container.style.gap = "16px";
  container.style.padding = "16px";

  // Handle the verified checkbox separately
  // We'll use this later when adding the verified checkbox

  // Special handling for the medication category which has a nested structure
  if (
    patientData.pre_procedure_medications.medication &&
    patientData.pre_procedure_medications.medication.medications
  ) {
    // Get the medications object
    const medications =
      patientData.pre_procedure_medications.medication.medications;

    // Render each medication
    Object.entries(medications).forEach(([medKey, medValue]) => {
      // Skip non-medication fields
      if (medKey === "label" || medKey === "field_id") return;

      // Create a tile wrapper for each medication
      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";

      // Create the field container
      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");
      fieldContainer.style.display = "flex";
      fieldContainer.style.flexDirection = "column";
      fieldContainer.style.height = "100%";
      fieldContainer.style.boxSizing = "border-box";
      fieldContainer.style.padding = "16px";
      fieldContainer.style.border = "1px solid #dee2e6";
      fieldContainer.style.borderRadius = "4px";
      fieldContainer.style.backgroundColor = "#fff";

      // Create the label
      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.textContent = `${medValue.label} (${medValue.field_id})`;
      if (medValue.description) {
        label.setAttribute("title", medValue.description);
      }
      fieldContainer.appendChild(label);

      // Create the modified_by display
      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.position = "absolute";
      modifiedByDisplay.style.bottom = "-20px";
      modifiedByDisplay.style.right = "0";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";

      // Initialize modified_by if not set
      if (!medValue.modified_by) {
        medValue.modified_by = "";
      }
      modifiedByDisplay.textContent = medValue.modified_by;

      // Process the input based on its type.
      if (medValue.input_type === "select" && medValue.options) {
        const select = document.createElement("select");
        select.name = medKey;
        select.style.marginTop = "8px";

        // Add a default option
        if (!medValue.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          select.appendChild(defaultOption);
        }

        // Add options from the data
        medValue.options.forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          if (medValue.value === option.value) {
            optionElement.selected = true;
          }
          select.appendChild(optionElement);
        });

        updateTileStyle(fieldContainer, medValue.value);

        select.addEventListener("change", (e) => {
          const selectedOption = medValue.options.find(
            (option) => option.id.toString() === e.target.value
          );
          const newValue = selectedOption ? selectedOption.value : "";
          patientData.pre_procedure_medications.medication.medications[
            medKey
          ].value = newValue;
          patientData.pre_procedure_medications.medication.medications[
            medKey
          ].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, newValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.pre_procedure_medications
          );
        });

        fieldContainer.appendChild(select);
      } else if (
        medValue.input_type === "string" ||
        medValue.input_type === "text"
      ) {
        const input = document.createElement("input");
        input.type = "text";
        input.name = medKey;
        input.placeholder = `Enter ${medValue.label}`;
        input.value = medValue.value || "";
        input.style.marginTop = "8px";

        updateTileStyle(fieldContainer, input.value);

        let previousValue = input.value;

        input.addEventListener("input", (e) => {
          const currentValue = e.target.value;

          // Use validation utilities with temporary callback
          let isValid = false;
          validateStringInput(
            currentValue,
            medValue.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update model and UI only if value is valid
              patientData.pre_procedure_medications.medication.medications[
                medKey
              ].value = validatedValue;
              patientData.pre_procedure_medications.medication.medications[
                medKey
              ].modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.pre_procedure_medications
              );

              // Update input value if validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );

          // Revert to previous value if validation failed
          if (!isValid) {
            e.target.value = previousValue;
          }
        });

        fieldContainer.appendChild(input);
      } else if (medValue.input_type === "date") {
        const dateWrapper = document.createElement("div");
        dateWrapper.style.position = "relative";
        dateWrapper.style.marginTop = "8px";

        // Create display input
        const displayInput = document.createElement("input");
        displayInput.type = "text";
        displayInput.name = `${medKey}-display`;
        displayInput.readOnly = true;
        displayInput.value = formatDisplayDate(medValue.value);
        displayInput.placeholder = "MM/DD/YYYY";
        displayInput.style.cursor = "pointer";
        displayInput.style.width = "100%";

        // Hidden date input
        const dateInput = document.createElement("input");
        dateInput.type = "date";
        dateInput.name = medKey;
        dateInput.value = medValue.value || "";
        dateInput.style.position = "absolute";
        dateInput.style.opacity = "0";
        dateInput.style.cursor = "pointer";
        dateInput.style.width = "100%";
        dateInput.style.height = "100%";
        dateInput.style.top = "0";
        dateInput.style.left = "0";
        dateInput.style.zIndex = "1";

        // Set max date to today
        const today = new Date().toISOString().split("T")[0];
        dateInput.max = today;

        updateTileStyle(fieldContainer, dateInput.value);

        dateInput.addEventListener("change", (e) => {
          const selectedDate = e.target.value;
          displayInput.value = formatDisplayDate(selectedDate);
          patientData.pre_procedure_medications.medication.medications[
            medKey
          ].value = selectedDate;
          patientData.pre_procedure_medications.medication.medications[
            medKey
          ].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, selectedDate);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.pre_procedure_medications
          );
        });

        // Trigger date picker when clicking display input
        displayInput.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          try {
            dateInput.showPicker();
          } catch (err) {
          
            // Fallback for browsers that don't support showPicker
            dateInput.focus();
            dateInput.click();
          }
        });

        // Also make the wrapper clickable
        dateWrapper.addEventListener("click", () => {
          try {
            dateInput.showPicker();
          } catch (err) {
          
            // Fallback for browsers that don't support showPicker
            dateInput.focus();
            dateInput.click();
          }
        });

        dateWrapper.appendChild(displayInput);
        dateWrapper.appendChild(dateInput);
        fieldContainer.appendChild(dateWrapper);
      } else if (medValue.input_type === "radio") {
        const radioContainer = document.createElement("div");
        radioContainer.classList.add("radio-container");
        radioContainer.style.marginTop = "8px";
        radioContainer.style.display = "flex";
        radioContainer.style.flexDirection = "column";
        radioContainer.style.gap = "10px";

        medValue.options.forEach((option) => {
          const radioWrapper = document.createElement("div");
          radioWrapper.style.display = "flex";
          radioWrapper.style.alignItems = "center";
          radioWrapper.style.marginBottom = "10px";

          const radioInput = document.createElement("input");
          radioInput.type = "radio";
          radioInput.name = `radio-${medKey}`;
          radioInput.value = option;
          radioInput.id = `${medKey}-${option}`;
          radioInput.checked = medValue.value === option; // Set the checked radio based on value

          // Event listener for radio selection.
          radioInput.addEventListener("change", (e) => {
            if (e.target.checked) {
              patientData.pre_procedure_medications.medication.medications[
                medKey
              ].value = e.target.value;
              patientData.pre_procedure_medications.medication.medications[
                medKey
              ].modified_by = "ABSTRACTOR";
              updateTileStyle(fieldContainer, e.target.value);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.pre_procedure_medications
              );
            }
          });

          const optionLabel = document.createElement("label");
          optionLabel.setAttribute("for", `${medKey}-${option}`);
          optionLabel.innerText = option;
          optionLabel.style.marginLeft = "8px";

          radioWrapper.appendChild(radioInput);
          radioWrapper.appendChild(optionLabel);
          radioContainer.appendChild(radioWrapper);
        });

        // Apply initial border styling based on the selected radio.
        updateTileStyle(fieldContainer, medValue.value || "");
        fieldContainer.appendChild(radioContainer);
      } else if (value.input_type === "select") {
        const select = document.createElement("select");
        select.name = key;

        // If no value is set, add a default option prompting selection.
        if (!value.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          select.appendChild(defaultOption);
        }

        value.options.forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id; // Assuming option has id and value
          optionElement.innerText = option.value;
          if (value.value && value.value === option.value) {
            optionElement.selected = true;
          }
          select.appendChild(optionElement);
        });

        // Event listener for select dropdown changes.
        select.addEventListener("change", (e) => {
          const selectedOption = value.options.find(
            (option) => option.id === e.target.value
          );
          patientData.pre_procedure_medications[key].value =
            selectedOption.value;
          patientData.pre_procedure_medications[key].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, selectedOption.value);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.pre_procedure_medications
          );
        });

        // Apply initial border styling from current selection.
        const selectedOption = value.options.find(
          (option) => option.value === value.value
        );
        updateTileStyle(
          fieldContainer,
          selectedOption ? selectedOption.value : ""
        );
        fieldContainer.appendChild(select);
      } else if (value.input_type === "multi_input_field") {
        const multiInputContainer = document.createElement("div");
        multiInputContainer.classList.add("multi-input-container");

        // Create radio buttons for options instead of text input and checkbox
        const radioContainer = document.createElement("div");
        radioContainer.classList.add("radio-container");
        radioContainer.style.display = "flex";
        radioContainer.style.flexDirection = "row";
        radioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
        radioContainer.style.gap = "15px";
        radioContainer.style.width = "100%";

        // Create a container for conditional content that will be shown/hidden
        const conditionalContentContainer = document.createElement("div");
        conditionalContentContainer.classList.add(
          "conditional-content-container"
        );
        conditionalContentContainer.style.marginTop = "10px";
        conditionalContentContainer.style.display = "none";

        // Function to render conditional content based on selected option
        const renderConditionalContent = (selectedOption) => {
          // Clear previous content
          conditionalContentContainer.innerHTML = "";
          conditionalContentContainer.style.display = "none";

          let conditionalKey = null;

          // Determine which conditional key to use based on selected option
          if (selectedOption === "Yes" && value.if_yes) {
            conditionalKey = "if_yes";
          } else if (selectedOption === "No" && value.if_no) {
            conditionalKey = "if_no";
          } else if (selectedOption === "Alive" && value.if_alive) {
            conditionalKey = "if_alive";
          } else if (selectedOption === "Deceased" && value.if_deceased) {
            conditionalKey = "if_deceased";
          }

          // If no conditional content for this option, return early
          if (!conditionalKey) return;

          // Show the container since we have conditional content
          conditionalContentContainer.style.display = "block";

          // Process each subfield in the conditional content
          Object.entries(value[conditionalKey]).forEach(
            ([subKey, subField]) => {
              // Create a container for this subfield
              const subFieldContainer = document.createElement("div");
              subFieldContainer.classList.add("subfield-container");
              subFieldContainer.style.marginBottom = "15px";

              // Create and append the sublabel
              const subLabel = document.createElement("label");
              subLabel.classList.add("sublabel");
              subLabel.textContent = `${subField.label} ${
                subField.field_id ? `(${subField.field_id})` : ""
              }`;
              if (subField.description) {
                subLabel.setAttribute("title", subField.description);
              }
              subFieldContainer.appendChild(subLabel);

              // Handle different input types for subfields
              if (
                subField.input_type === "string" ||
                subField.input_type === "text"
              ) {
                const input = document.createElement("input");
                input.type = "text";
                input.name = `${key}-${subKey}`;
                input.placeholder = `Enter ${subField.label}`;
                input.value = subField.value || "";

                let previousValue = input.value;

                input.addEventListener("input", (e) => {
                  const currentValue = e.target.value;

                  // Use validation utilities with temporary callback
                  let isValid = false;
                  validateStringInput(
                    currentValue,
                    subField.field_id,
                    (validatedValue) => {
                      isValid = true;
                      previousValue = validatedValue;

                      // Update model and UI only if value is valid
                      value[conditionalKey][subKey].value = validatedValue;
                      value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight(
                        container,
                        patientData.pre_procedure_medications
                      );

                      // Update input value if validation modified it
                      if (validatedValue !== currentValue) {
                        e.target.value = validatedValue;
                      }
                    }
                  );

                  // Revert to previous value if validation failed
                  if (!isValid) {
                    e.target.value = previousValue;
                  }
                });

                subFieldContainer.appendChild(input);
              } else if (subField.input_type === "date") {
                const dateWrapper = document.createElement("div");
                dateWrapper.style.position = "relative";

                // Create display input
                const displayInput = document.createElement("input");
                displayInput.type = "text";
                displayInput.name = `${key}-${subKey}_display`;
                displayInput.readOnly = true;
                displayInput.value = formatDisplayDate(subField.value);
                displayInput.placeholder = "MM/DD/YYYY";
                displayInput.style.cursor = "pointer";

                // Hidden date input
                const dateInput = document.createElement("input");
                dateInput.type = "date";
                dateInput.name = `${key}-${subKey}`;
                dateInput.value = subField.value || "";
                dateInput.style.position = "absolute";
                dateInput.style.opacity = "0";
                dateInput.style.cursor = "pointer";

                // Set max date to today
                const today = new Date().toISOString().split("T")[0];
                dateInput.max = today;

                dateInput.addEventListener("change", (e) => {
                  const selectedDate = e.target.value;
                  displayInput.value = formatDisplayDate(selectedDate);
                  value[conditionalKey][subKey].value = selectedDate;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.pre_procedure_medications
                  );
                });

                // Trigger date picker when clicking display input
                displayInput.addEventListener("click", () => {
                  dateInput.showPicker();
                });

                dateWrapper.appendChild(displayInput);
                dateWrapper.appendChild(dateInput);
                subFieldContainer.appendChild(dateWrapper);
              } else if (subField.input_type === "radio") {
                const subRadioContainer = document.createElement("div");
                subRadioContainer.classList.add("radio-container");
                subRadioContainer.style.display = "flex";
                subRadioContainer.style.flexDirection = "row";
                subRadioContainer.style.flexWrap = "nowrap";
                subRadioContainer.style.gap = "15px";

                subField.options.forEach((option) => {
                  const radioWrapper = document.createElement("div");
                  radioWrapper.style.display = "flex";
                  radioWrapper.style.alignItems = "center";
                  radioWrapper.style.marginRight = "10px";

                  const input = document.createElement("input");
                  input.type = "radio";
                  input.name = `${key}-${subKey}`;
                  input.value = option;
                  input.id = `${key}-${subKey}-${option}`;
                  input.checked = subField.value === option;

                  input.addEventListener("change", (e) => {
                    if (e.target.checked) {
                      // Update the sub-field value
                      value[conditionalKey][subKey].value = e.target.value;
                      value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight(
                        container,
                        patientData.pre_procedure_medications
                      );
                    }
                  });

                  const optionLabel = document.createElement("label");
                  optionLabel.setAttribute("for", `${key}-${subKey}-${option}`);
                  optionLabel.innerText = option;
                  if (subField.description) {
                    optionLabel.setAttribute("title", subField.description);
                  }

                  radioWrapper.appendChild(input);
                  radioWrapper.appendChild(optionLabel);
                  subRadioContainer.appendChild(radioWrapper);
                });

                subFieldContainer.appendChild(subRadioContainer);
              } else if (subField.input_type === "select") {
                const select = document.createElement("select");
                select.name = `${key}-${subKey}`;

                // Add a default option if no value is set
                if (!subField.value) {
                  const defaultOption = document.createElement("option");
                  defaultOption.value = "";
                  defaultOption.innerText = "Select an option";
                  defaultOption.disabled = true;
                  defaultOption.selected = true;
                  if (subField.description) {
                    defaultOption.setAttribute("title", subField.description);
                  }
                  select.appendChild(defaultOption);
                }

                subField.options.forEach((option) => {
                  const optionElement = document.createElement("option");
                  optionElement.value = option.id;
                  optionElement.innerText = option.value;
                  if (subField.value && subField.value === option.value) {
                    optionElement.selected = true;
                  }
                  select.appendChild(optionElement);
                });

                select.addEventListener("change", (e) => {
                  const selOption = subField.options.find(
                    (option) => option.id === e.target.value
                  );
                  const newValue = selOption ? selOption.value : "";
                  value[conditionalKey][subKey].value = newValue;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.pre_procedure_medications
                  );
                });

                subFieldContainer.appendChild(select);
              } else if (subField.input_type === "multi_select") {
                // Initialize the value as an array if it's not already
                if (!Array.isArray(subField.value)) {
                  subField.value = subField.value ? [subField.value] : [];
                }

                // Create a dropdown container with relative positioning
                const dropdownContainer = document.createElement("div");
                dropdownContainer.classList.add("dropdown-container");
                dropdownContainer.style.position = "relative";
                dropdownContainer.style.width = "100%";

                // Make sure all parent containers allow overflow
                subFieldContainer.style.overflow = "visible";

                // Create dropdown header
                const dropdownHeader = document.createElement("div");
                dropdownHeader.classList.add("dropdown-header");
                dropdownHeader.style.display = "flex";
                dropdownHeader.style.justifyContent = "space-between";
                dropdownHeader.style.alignItems = "center";
                dropdownHeader.style.padding = "8px 12px";
                dropdownHeader.style.border = "1px solid #ccc";
                dropdownHeader.style.borderRadius = "4px";
                dropdownHeader.style.cursor = "pointer";

                // Create selected text display
                const selectedText = document.createElement("div");
                selectedText.classList.add("selected-text");
                selectedText.style.overflow = "hidden";
                selectedText.style.textOverflow = "ellipsis";
                selectedText.style.whiteSpace = "nowrap";
                selectedText.style.flex = "1";

                // Create dropdown arrow
                const dropdownArrow = document.createElement("span");
                dropdownArrow.innerHTML = "&#9662;"; // Down arrow
                dropdownArrow.style.marginLeft = "8px";

                // Add elements to dropdown header
                dropdownHeader.appendChild(selectedText);
                dropdownHeader.appendChild(dropdownArrow);

                // Create dropdown content
                const dropdownContent = document.createElement("div");
                dropdownContent.classList.add("dropdown-content");
                dropdownContent.style.display = "none";
                dropdownContent.style.position = "absolute";
                dropdownContent.style.backgroundColor = "#f9f9f9";
                dropdownContent.style.minWidth = "100%";
                dropdownContent.style.width = "max-content";
                dropdownContent.style.boxShadow =
                  "0px 8px 16px 0px rgba(0,0,0,0.2)";
                dropdownContent.style.zIndex = "1";
                dropdownContent.style.maxHeight = "200px";
                dropdownContent.style.overflowY = "auto";
                dropdownContent.style.border = "1px solid #ddd";
                dropdownContent.style.borderRadius = "4px";
                dropdownContent.style.marginTop = "4px";

                // Function to update the selected text display
                const updateSelectedText = () => {
                  if (subField.value.length === 0) {
                    selectedText.textContent = "Select options";
                  } else {
                    selectedText.textContent = subField.value.join(", ");
                  }
                };

                // Initial update of selected text
                updateSelectedText();

                // Create options for the dropdown
                subField.options.forEach((option) => {
                  const optionWrapper = document.createElement("div");
                  optionWrapper.style.padding = "8px 12px";
                  optionWrapper.style.display = "flex";
                  optionWrapper.style.alignItems = "center";
                  optionWrapper.style.cursor = "pointer";
                  optionWrapper.style.borderBottom = "1px solid #eee";

                  const input = document.createElement("input");
                  input.type = "checkbox";
                  input.id = `${key}-${subKey}-${option.value}`;
                  input.value = option.value;
                  input.style.marginRight = "8px";

                  // Check if this option is in the selected values array
                  input.checked = subField.value.includes(option.value);

                  input.addEventListener("change", (e) => {
                    // Get the current values array
                    let currentValues = Array.isArray(subField.value)
                      ? [...subField.value]
                      : [];

                    if (e.target.checked) {
                      // Add the value if it's not already in the array
                      if (!currentValues.includes(option.value)) {
                        currentValues.push(option.value);
                      }
                    } else {
                      // Remove the value if it's in the array
                      currentValues = currentValues.filter(
                        (val) => val !== option.value
                      );
                    }

                    // Update the model
                    value[conditionalKey][subKey].value = currentValues;
                    value[conditionalKey][subKey].modified_by = "ABSTRACTOR";

                    // Update selected text display
                    updateSelectedText();

                    // Update UI
                    modifiedByDisplay.textContent = "ABSTRACTOR";
                    updateContainerHighlight(
                      container,
                      patientData.pre_procedure_medications
                    );
                  });

                  const optionLabel = document.createElement("label");
                  optionLabel.setAttribute(
                    "for",
                    `${key}-${subKey}-${option.value}`
                  );
                  optionLabel.innerText = option.value;
                  if (option.field_id) {
                    optionLabel.innerText += ` (${option.field_id})`;
                  }

                  optionWrapper.appendChild(input);
                  optionWrapper.appendChild(optionLabel);
                  dropdownContent.appendChild(optionWrapper);
                });

                // Function to show dropdown
                const showDropdown = () => {
                  dropdownContent.style.display = "block";
                };

                // Function to close dropdown
                const closeDropdown = () => {
                  dropdownContent.style.display = "none";
                  dropdownArrow.innerHTML = "&#9662;"; // Down arrow
                  window.removeEventListener("scroll", closeDropdown);
                };

                // Close dropdown when clicking outside
                window.addEventListener("click", (e) => {
                  if (
                    !dropdownHeader.contains(e.target) &&
                    !dropdownContent.contains(e.target)
                  ) {
                    closeDropdown();
                  }
                });

                // Set up the click handler for the dropdown
                dropdownHeader.onclick = (e) => {
                  e.stopPropagation();
                  const isOpen = dropdownContent.style.display === "block";

                  if (!isOpen) {
                    showDropdown();
                    dropdownArrow.innerHTML = "&#9652;"; // Up arrow
                    window.addEventListener("scroll", closeDropdown);
                  } else {
                    closeDropdown();
                  }
                };

                // Add elements to dropdown container
                dropdownContainer.appendChild(dropdownHeader);
                dropdownContainer.appendChild(dropdownContent);
                subFieldContainer.appendChild(dropdownContainer);
              } else if (subField.input_type === "multi_input_field") {
                // Handle nested multi_input_field
                const nestedMultiInputContainer = document.createElement("div");
                nestedMultiInputContainer.classList.add(
                  "multi-input-container"
                );
                nestedMultiInputContainer.style.marginTop = "10px";

                // Create radio buttons for the nested multi_input_field
                const nestedRadioContainer = document.createElement("div");
                nestedRadioContainer.classList.add("radio-container");
                nestedRadioContainer.style.display = "flex";
                nestedRadioContainer.style.flexDirection = "row";
                nestedRadioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
                nestedRadioContainer.style.gap = "15px";
                nestedRadioContainer.style.width = "100%";

                // Create a single container for nested conditional content
                const nestedConditionalContainer =
                  document.createElement("div");
                nestedConditionalContainer.classList.add(
                  "conditional-content-container"
                );
                nestedConditionalContainer.style.marginTop = "10px";
                nestedConditionalContainer.style.display = "none";

                // Function to update nested value
                const updateNestedValue = (newValue) => {
                  subField.value = newValue;
                  subField.modified_by = "ABSTRACTOR";
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.pre_procedure_medications
                  );
                };

                // Function to render nested conditional content
                const renderNestedConditionalContent = (selectedOption) => {
                  // Clear previous content
                  nestedConditionalContainer.innerHTML = "";
                  nestedConditionalContainer.style.display = "none";

                  let nestedConditionalKey = null;

                  // Determine which conditional key to use based on selected option
                  if (selectedOption === "Yes" && subField.if_yes) {
                    nestedConditionalKey = "if_yes";
                  } else if (selectedOption === "No" && subField.if_no) {
                    nestedConditionalKey = "if_no";
                  } else if (selectedOption === "Alive" && subField.if_alive) {
                    nestedConditionalKey = "if_alive";
                  } else if (
                    selectedOption === "Deceased" &&
                    subField.if_deceased
                  ) {
                    nestedConditionalKey = "if_deceased";
                  }

                  // If no conditional content for this option, return early
                  if (!nestedConditionalKey) return;

                  // Show the container since we have conditional content
                  nestedConditionalContainer.style.display = "block";

                  // Process each nested subfield in the conditional content
                  Object.entries(subField[nestedConditionalKey]).forEach(
                    ([nestedSubKey, nestedSubField]) => {
                      // Create a container for this nested subfield
                      const nestedSubFieldContainer =
                        document.createElement("div");
                      nestedSubFieldContainer.classList.add(
                        "subfield-container"
                      );
                      nestedSubFieldContainer.style.marginBottom = "15px";

                      // Create and append the nested sublabel
                      const nestedSubLabel = document.createElement("label");
                      nestedSubLabel.classList.add("sublabel");
                      nestedSubLabel.textContent = `${nestedSubField.label} ${
                        nestedSubField.field_id
                          ? `(${nestedSubField.field_id})`
                          : ""
                      }`;
                      if (nestedSubField.description) {
                        nestedSubLabel.setAttribute(
                          "title",
                          nestedSubField.description
                        );
                      }
                      nestedSubFieldContainer.appendChild(nestedSubLabel);

                      // Handle different input types for nested subfields
                      // This would be similar to the subfield input type handling
                      // For brevity, we'll just handle the basic types here
                      if (
                        nestedSubField.input_type === "string" ||
                        nestedSubField.input_type === "text"
                      ) {
                        const input = document.createElement("input");
                        input.type = "text";
                        input.name = `${key}-${subKey}-${nestedSubKey}`;
                        input.placeholder = `Enter ${nestedSubField.label}`;
                        input.value = nestedSubField.value || "";

                        let previousValue = input.value;

                        input.addEventListener("input", (e) => {
                          const currentValue = e.target.value;

                          // Use validation utilities with temporary callback
                          let isValid = false;
                          validateStringInput(
                            currentValue,
                            nestedSubField.field_id,
                            (validatedValue) => {
                              isValid = true;
                              previousValue = validatedValue;

                              // Update model and UI only if value is valid
                              subField[nestedConditionalKey][
                                nestedSubKey
                              ].value = validatedValue;
                              subField[nestedConditionalKey][
                                nestedSubKey
                              ].modified_by = "ABSTRACTOR";
                              value[conditionalKey][subKey].modified_by =
                                "ABSTRACTOR";
                              modifiedByDisplay.textContent = "ABSTRACTOR";
                              updateContainerHighlight(
                                container,
                                patientData.pre_procedure_medications
                              );

                              // Update input value if validation modified it
                              if (validatedValue !== currentValue) {
                                e.target.value = validatedValue;
                              }
                            }
                          );

                          // Revert to previous value if validation failed
                          if (!isValid) {
                            e.target.value = previousValue;
                          }
                        });

                        nestedSubFieldContainer.appendChild(input);
                      }

                      // Add the nested subfield container to the nested conditional content container
                      nestedConditionalContainer.appendChild(
                        nestedSubFieldContainer
                      );
                    }
                  );
                };

                // Create radio buttons for each option in the nested multi_input_field
                if (subField.options && Array.isArray(subField.options)) {
                  subField.options.forEach((option) => {
                    const optionWrapper = document.createElement("div");
                    optionWrapper.style.display = "flex";
                    optionWrapper.style.alignItems = "center";

                    const radioWrapper = document.createElement("div");
                    radioWrapper.style.display = "flex";
                    radioWrapper.style.alignItems = "center";
                    radioWrapper.style.marginRight = "5px";

                    const input = document.createElement("input");
                    input.type = "radio";
                    input.name = `${key}-${subKey}`;
                    input.value = option;
                    input.id = `${key}-${subKey}-${option}`;
                    input.checked = subField.value === option;

                    // Update data model and render nested conditional content
                    input.addEventListener("change", (e) => {
                      if (e.target.checked) {
                        // Update the nested field value
                        updateNestedValue(e.target.value);

                        // Always clear any previously rendered content first
                        nestedConditionalContainer.innerHTML = "";
                        nestedConditionalContainer.style.display = "none";

                        // Check if this option has conditional content to display
                        let hasConditionalContent = false;
                        if (option === "Yes" && subField.if_yes) {
                          hasConditionalContent = true;
                        } else if (option === "No" && subField.if_no) {
                          hasConditionalContent = true;
                        } else if (option === "Alive" && subField.if_alive) {
                          hasConditionalContent = true;
                        } else if (
                          option === "Deceased" &&
                          subField.if_deceased
                        ) {
                          hasConditionalContent = true;
                        }

                        // Only render if there's conditional content to show
                        if (hasConditionalContent) {
                          renderNestedConditionalContent(e.target.value);
                        }
                      }
                    });

                    const optionLabel = document.createElement("label");
                    optionLabel.setAttribute(
                      "for",
                      `${key}-${subKey}-${option}`
                    );
                    optionLabel.innerText = option;
                    if (subField.description) {
                      optionLabel.setAttribute("title", subField.description);
                    }

                    radioWrapper.appendChild(input);
                    radioWrapper.appendChild(optionLabel);
                    optionWrapper.appendChild(radioWrapper);
                    nestedRadioContainer.appendChild(optionWrapper);

                    // Render initial nested conditional content if this option is selected
                    if (subField.value === option) {
                      // Check if this option has conditional content to display
                      let hasConditionalContent = false;
                      if (option === "Yes" && subField.if_yes) {
                        hasConditionalContent = true;
                      } else if (option === "No" && subField.if_no) {
                        hasConditionalContent = true;
                      } else if (option === "Alive" && subField.if_alive) {
                        hasConditionalContent = true;
                      } else if (
                        option === "Deceased" &&
                        subField.if_deceased
                      ) {
                        hasConditionalContent = true;
                      }

                      // Only render if there's conditional content to show
                      if (hasConditionalContent) {
                        renderNestedConditionalContent(option);
                      }
                    }
                  });
                }

                // Add radio container and conditional container to the multi-input container
                nestedMultiInputContainer.appendChild(nestedRadioContainer);
                nestedMultiInputContainer.appendChild(
                  nestedConditionalContainer
                );
                subFieldContainer.appendChild(nestedMultiInputContainer);
              }

              // Add the subfield container to the conditional content container
              conditionalContentContainer.appendChild(subFieldContainer);
            }
          );
        };

        // Create radio buttons for each option
        if (value.options && Array.isArray(value.options)) {
          value.options.forEach((option) => {
            const optionWrapper = document.createElement("div");
            optionWrapper.style.display = "flex";
            optionWrapper.style.alignItems = "center";

            const radioWrapper = document.createElement("div");
            radioWrapper.style.display = "flex";
            radioWrapper.style.alignItems = "center";
            radioWrapper.style.marginRight = "5px";

            const input = document.createElement("input");
            input.type = "radio";
            input.name = key;
            input.value = option;
            input.id = `${key}-${option}`;
            input.checked = value.value === option;

            // Update patientData and render conditional content when a radio is selected
            input.addEventListener("change", (e) => {
              if (e.target.checked) {
                patientData.pre_procedure_medications[key].value =
                  e.target.value;
                patientData.pre_procedure_medications[key].modified_by =
                  "ABSTRACTOR";
                updateTileStyle(fieldContainer, e.target.value);
                modifiedByDisplay.textContent = "ABSTRACTOR";
                updateContainerHighlight(
                  container,
                  patientData.pre_procedure_medications
                );

                // Clear and re-render conditional content
                renderConditionalContent(e.target.value);
              }
            });

            const optionLabel = document.createElement("label");
            optionLabel.setAttribute("for", `${key}-${option}`);
            optionLabel.innerText = option;
            if (value.description) {
              optionLabel.setAttribute("title", value.description);
            }

            radioWrapper.appendChild(input);
            radioWrapper.appendChild(optionLabel);
            optionWrapper.appendChild(radioWrapper);
            radioContainer.appendChild(optionWrapper);

            // Render initial conditional content if this option is selected
            if (value.value === option) {
              renderConditionalContent(option);
            }
          });
        }

        // Apply initial border styling
        updateTileStyle(fieldContainer, value.value || "");

        // Add elements to container
        multiInputContainer.appendChild(radioContainer);
        multiInputContainer.appendChild(conditionalContentContainer);
        fieldContainer.appendChild(multiInputContainer);
      }

      // Append the field container and the modified_by display to the tile wrapper.
      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);

      // Append the complete tile to the main container.
      container.appendChild(tileWrapper);
    });

    // Integrate container-level verified checkbox.
    if (
      typeof patientData.pre_procedure_medications.verified !== "object" ||
      patientData.pre_procedure_medications.verified === null
    ) {
      patientData.pre_procedure_medications.verified = {
        value: patientData.pre_procedure_medications.verified || "False",
        modified_by: "",
      };
    }
    const verifiedData = patientData.pre_procedure_medications.verified;
    const verifiedContainer = document.createElement("div");
    // Place the verified checkbox within the section.
    verifiedContainer.style.position = "absolute";
    verifiedContainer.style.bottom = "16px";
    verifiedContainer.style.right = "16px";
    verifiedContainer.style.display = "flex";
    verifiedContainer.style.alignItems = "center";
    const containerVerifiedCheckbox = document.createElement("input");
    containerVerifiedCheckbox.type = "checkbox";
    containerVerifiedCheckbox.id = "preProcedureMedications-verified-checkbox";
    containerVerifiedCheckbox.checked = verifiedData.value === "True";
    containerVerifiedCheckbox.style.width = "24px";
    containerVerifiedCheckbox.style.height = "24px";
    containerVerifiedCheckbox.addEventListener("change", (e) => {
      verifiedData.value = e.target.checked ? "True" : "False";
      updateContainerHighlight(
        container,
        patientData.pre_procedure_medications
      );
    });
    const containerVerifiedLabel = document.createElement("label");
    containerVerifiedLabel.setAttribute(
      "for",
      "preProcedureMedications-verified-checkbox"
    );
    containerVerifiedLabel.classList.add("mt-2", "ml-2");
    containerVerifiedLabel.innerText = "Verified";
    containerVerifiedLabel.style.fontSize = "18px";
    containerVerifiedLabel.style.fontWeight = "bold";
    verifiedContainer.appendChild(containerVerifiedCheckbox);
    verifiedContainer.appendChild(containerVerifiedLabel);
    container.appendChild(verifiedContainer);

    // Initial update of container highlight.
    updateContainerHighlight(container, patientData.pre_procedure_medications);
  }
}
