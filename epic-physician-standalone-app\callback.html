<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Callback</title>
    <script src="https://cdn.jsdelivr.net/npm/fhirclient/build/fhir-client.min.js"></script>
    <script src="../config.js"></script>
</head>

<body>
    <p>code from URL: <span id="code"></span></p>
    <p>Name: <span id="name"></span></p>
    <p>Birth Date: <span id="birthDate"></span></p>
    <p>Gender: <span id="gender"></span></p><br />
    <button type="button" id="fetchButton" onclick="getAuthUrl()">Send the Patient Details to AtriAI</button><br />

    <script type="text/javascript">
        const queryString = window.location.search;
        const searchParams = new URLSearchParams(queryString);
        const callback_code = searchParams.get('code');
        document.getElementById("code").innerHTML = callback_code;

        let myApp = {};
        FHIR.oauth2.ready().then((client) => {
            console.log('clint', client)
            myApp.smart = client;
            getPatient()
            // getEncounter()
            // getBinary()
        });
        const getPatient = async () => {
            let patient = await fetch(`${myApp.smart.state.serverUrl}/Patient/${myApp.smart.patient.id}`, {
                headers: {
                    "Accept": "application/json",
                    "Authorization": `Bearer ${myApp.smart.state.tokenResponse.access_token}`
                }
            }).then((data) => data);
            let patientName = '';
            let patientData = await patient.json();
            console.log("patientData0", patientData)
            localStorage.setItem("patientData", JSON.stringify(patientData))
            for (const name of patientData.name) {

                if (name.use == 'official') {
                    patientName = `${name.given.join(' ')} ${name.family}`
                }
            }
            document.getElementById("name").innerHTML = patientName;
            document.getElementById("birthDate").innerHTML = patientData.birthDate;
            document.getElementById("gender").innerHTML = patientData.gender;
        }

        const getEncounter = async () => {
            // let encounter = await fetch(`${myApp.smart.state.serverUrl}/Encounter/${myApp.smart.encounter.id}`, {
            //     headers: {
            //         "Accept": "application/json",
            //         "Authorization": `Bearer ${myApp.smart.state.tokenResponse.access_token}`
            //     }
            // }).then((data) => data);
            // // let encounterName = '';
            // let encounterData = await encounter.json();
            // document.getElementById("encounter").innerHTML = encounterData.class.display;
            // console.log("encounter", encounterData);
            let clinical = await fetch(`${myApp.smart.state.serverUrl}/DocumentReference?encounter=${myApp.smart.encounter.id}`, {
                headers: {
                    "Accept": "application/json",
                    "Authorization": `Bearer ${myApp.smart.state.tokenResponse.access_token}`
                }
            }).then((data) => data);
            let clinicalData = await clinical.json();
            console.log("clinicalData", clinicalData)
            let selectElement = document.getElementById('clinical')
            selectElement.innerHTML = "";

            console.log("clinical list", clinicalData)

            for (const item of clinicalData.entry) {
                try {
                    const option = document.createElement("option");
                    option.value = item['resource']["content"][0]['attachment']['url']; // Set the option value
                    option.textContent = item['resource']["content"][0]['attachment']['url']; // Set the option label
                    selectElement.appendChild(option);
                    console.log("i", item['resource']["content"][0]['attachment']['url'])
                }
                catch (error) {
                    console.log("err", error)
                }


            }
        }

        const createClinicalNotes = async () => {
            // Define the string
            var decodedStringBtoA = document.getElementById('notes').value;

            console.log("dec", decodedStringBtoA)

            // var topic = document.getElementById('Topic').value;

            // Encode the String
            var encodedStringBtoA = btoa(decodedStringBtoA);

            console.log("encode", encodedStringBtoA);
            var checkbody = {
                "resourceType": "DocumentReference",
                "docStatus": "final",
                "type": {
                    "coding": [
                        {
                            "system": "http://loinc.org",
                            "code": "11488-4",
                            "display": "Consultant Notes"
                        }
                    ],
                    "text": "Consultant Notes"
                },
                "subject": {
                    "reference": "Patient/" + myApp.smart.patient.id
                },
                "content": [
                    {
                        "attachment": {
                            "contentType": "text/plain",
                            "data": encodedStringBtoA
                        }
                    }
                ],
                "context": {
                    "encounter": [{
                        "reference": "Encounter/" + myApp.smart.encounter.id
                    }]
                }
            }
            console.log("check body", checkbody);
            let clinicalDoc = await fetch(`${myApp.smart.state.serverUrl}/DocumentReference`, {
                method: 'POST',
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${myApp.smart.state.tokenResponse.access_token}`
                },
                body: JSON.stringify(
                    {
                        "resourceType": "DocumentReference",
                        "docStatus": "final",
                        "type": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "11488-4",
                                    "display": "Consultant Notes"
                                }
                            ],
                            "text": "Consultant Notes"
                        },
                        "subject": {
                            "reference": "Patient/" + myApp.smart.patient.id
                        },
                        "content": [
                            {
                                "attachment": {
                                    "contentType": "text/plain",
                                    "data": encodedStringBtoA
                                }
                            }
                        ],
                        "context": {
                            "encounter": [{
                                "reference": "Encounter/" + myApp.smart.encounter.id
                            }]
                        }
                    }
                )
            }).then((data) => data);
            // let clinicalName = '';
            let clinicalDocRes = await clinicalDoc.headers.entries();
            console.log("clinical id", clinicalDocRes);
            for (var pair of clinicalDocRes) {
                console.log(pair[0] + ': ' + pair[1]);
            }
        }


        const selectElement = document.getElementById("clinical");
        const getBinary = async () => {
            const selectedValue = selectElement.value;
            try {
                let binary = await fetch(`${myApp.smart.state.serverUrl}/` + selectedValue, {
                    headers: {
                        "Accept": "application/json",
                        "Authorization": `Bearer ${myApp.smart.state.tokenResponse.access_token}`
                    }
                }).then((data) => data);
                let binaryRes = await binary.text()
                document.getElementById('binary').innerHTML = binaryRes;
                console.log("Bin", binaryRes)
            }
            catch (error) {
                console.error("Error fetching data:", error);
            }
        }

        async function getAuthUrl() {
            const patientdata = JSON.parse(localStorage.getItem('patientData'));
            if (patientdata.issue) {
                alert("Patient Data not found")
                window.location.href = "launch.html";
                localStorage.clear()
            } else {
                const url = `${config.apiUrl}/auth?redirect_url=${config.epic_redirect_url}`
                window.location.href = url;
            }
        }


    </script>


</body>

</html>