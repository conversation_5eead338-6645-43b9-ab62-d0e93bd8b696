const chatArea = document.getElementById("chatArea");
const messageInput = document.getElementById("messageInput");
const sendButton = document.getElementById("sendButton");

sendButton.addEventListener("click", () => {
  const message = messageInput.value.trim();
  if (message) {
    // Add the user's message
    const userMessage = document.createElement("div");
    userMessage.classList.add("flex", "justify-end");
    userMessage.innerHTML = `<div class='bg-blue-500 text-white px-4 py-2 rounded-lg max-w-xs'>${message}</div>`;
    chatArea.appendChild(userMessage);

    // Scroll to the bottom of the chat area
    chatArea.scrollTop = chatArea.scrollHeight;

    // Clear the input field
    messageInput.value = "";

    // Simulate a response
    setTimeout(() => {
      const botMessage = document.createElement("div");
      botMessage.classList.add("flex");
      botMessage.innerHTML = `<div class='bg-gray-200 px-4 py-2 rounded-lg max-w-xs'>This  is response from the bot!</div>`;
      chatArea.appendChild(botMessage);
      chatArea.scrollTop = chatArea.scrollHeight;
    }, 1000);
  }
});

// Enable sending messages with Enter key
messageInput.addEventListener("keydown", (e) => {
  if (e.key === "Enter") {
    sendButton.click();
  }
});



function getDeviceOS() {
  const platform = navigator.platform.toLowerCase();  
  alert(platform)
  if(!(/iphone|ipad|ipod/.test(platform))){
    messageInput.addEventListener("focus",()=>{
      document.getElementById('inputDiv').style.marginBottom = "70%"
    })
    messageInput.addEventListener("focusout",()=>{
      document.getElementById('inputDiv').style.marginBottom = "0%"
    })
    
  }
}

getDeviceOS()


window.addEventListener('message', (event) => {
  alert("data recived   ")
});

window.addEventListener("message", message => {
  window.ReactNativeWebView.postMessage('Client received data')
});


function getData(data){
  alert(data)
}

function closeModal(){
  window.ReactNativeWebView.postMessage("close")
}
