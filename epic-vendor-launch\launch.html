<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>Smart App Launch</title>
  <script src="https://cdn.jsdelivr.net/npm/fhirclient/build/fhir-client.min.js"></script>
  <style>
    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f4f4f9;
    }

    .container img {
      max-width: 100%;
      height: auto;
    }
  </style>
</head>

<body>

  <div class="container" style="display: none;" id="container">
    <img src="./failed.png" alt="Loading failed..">
  </div>
  <script src="../config.js"></script>
  <script>
    const queryString = new URLSearchParams(window.location.search);
    const auth = () => {
      iss_url = queryString.get("iss");
      launch_token = queryString.get("launch");
      if (!iss_url || !launch_token) {
        alert("Issuer URL or launch code is missing");
        document.getElementById("container").style.display = "";
        return;
      }
      scope_base = "user"
      try {
        FHIR.oauth2.authorize({
          clientId: config.epic_vendor_clientid,
          responseType: "code",
          aud: iss_url,
          launch: launch_token,
          completeInTarget: true,
          scope: [
            "fhirUser openid launch",
            // `${scope_base}/DocumentReference.create`,
            `${scope_base}/Patient.read`,
            // `${scope_base}/Encounter.read`,
            // `${scope_base}/DocumentReference.read`,
            // `${scope_base}/Binary.read`,
            // `${scope_base}/Appointment.read`,
            // `${scope_base}/Condition.read`,
            // `${scope_base}/DiagnosticReport.read`,
            // `${scope_base}/ExplanationOfBenefit.read`,
            // `${scope_base}/CarePlan.read`,
            // `${scope_base}/AllergyIntolerance.read`,
            // `${scope_base}/MedicationRequest.read`,
            // `${scope_base}/Device.read`,
            // `${scope_base}/Observation.read`,
            // `${scope_base}/Procedure.read`,
            // `${scope_base}/Immunization.read`,
            `${scope_base}/Condition.search`,
            `${scope_base}/DiagnosticReport.search`,
            `${scope_base}/Encounter.search`,
            `${scope_base}/DocumentReference.search`,
            `${scope_base}/Appointment.search`,
            `${scope_base}/ExplanationOfBenefit.search`,
            `${scope_base}/CarePlan.search`,
            `${scope_base}/AllergyIntolerance.search`,
            `${scope_base}/MedicationRequest.search`,
            `${scope_base}/Device.search`,
            `${scope_base}/Observation.search`,
            `${scope_base}/Procedure.search`,
            `${scope_base}/Immunization.search`,
            `${scope_base}/MedicationAdministration.search`,
            "launch/patient",
            "launch/encounter",
            "launch/clinicalnotes"
          ].join(" "),
          redirectUri: "callback.html"
        });
      } catch (error) {
        alert("something went wrong...");
        document.getElementById("container").style.display = "";
      }
    }
    auth();
  </script>
</body>

</html>