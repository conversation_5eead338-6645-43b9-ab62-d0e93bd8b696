import { updateTileStyle } from "../utils.js";

function updateContainerHighlight(container, data) {
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

export function renderDischargeMedication(patientData) {
  const container = document.getElementById("dischargeMedications");
  container.innerHTML = ""; // Clear the container before rendering
  container.style.position = "relative";

  // // Add note if available
  // if (patientData.discharge_medications.note) {
  //   const noteContainer = document.createElement("div");
  //   noteContainer.classList.add("note-container");
  //   noteContainer.style.padding = "12px";
  //   noteContainer.style.marginBottom = "16px";
  //   noteContainer.style.backgroundColor = "#f8f9fa";
  //   noteContainer.style.border = "1px solid #dee2e6";
  //   noteContainer.style.borderRadius = "4px";
  //   noteContainer.style.fontSize = "14px";
  //   noteContainer.style.color = "#495057";

  //   const noteIcon = document.createElement("i");
  //   noteIcon.classList.add("fas", "fa-info-circle");
  //   noteIcon.style.marginRight = "8px";
  //   noteIcon.style.color = "#8143d9";

  //   noteContainer.appendChild(noteIcon);
  //   noteContainer.appendChild(
  //     document.createTextNode(patientData.discharge_medications.note)
  //   );
  //   container.appendChild(noteContainer);
  // }

  // Initialize verified field if it doesn't exist
  if (
    typeof patientData.discharge_medications.verified !== "object" ||
    patientData.discharge_medications.verified === null
  ) {
    patientData.discharge_medications.verified = {
      value: patientData.discharge_medications.verified || "False",
      modified_by: "",
    };
  }

  // Create a section for the medication category
  const medicationSection = document.createElement("div");
  medicationSection.classList.add("medication-section");
  medicationSection.style.marginBottom = "24px";

  // Add medication section heading
  const medicationHeading = document.createElement("h4");
  medicationHeading.style.fontWeight = "bold";
  medicationHeading.style.marginBottom = "16px";
  medicationHeading.innerText =
    patientData.discharge_medications.medication.label;
  medicationSection.appendChild(medicationHeading);

  // Create a container for prescribed medications
  const prescribedContainer = document.createElement("div");
  prescribedContainer.classList.add("prescribed-container");
  prescribedContainer.style.marginBottom = "16px";
  prescribedContainer.style.display = "grid";
  prescribedContainer.style.gridTemplateColumns = "repeat(3, 2fr)";
  prescribedContainer.style.gap = "16px";

  // Add prescribed at discharge heading
  const prescribedHeading = document.createElement("h5");
  prescribedHeading.style.fontWeight = "bold";
  prescribedHeading.style.marginBottom = "12px";
  prescribedHeading.style.width = "100%"; 
  prescribedHeading.innerText = "Prescribed at Discharge";
  prescribedContainer.appendChild(prescribedHeading);

  // Render each medication
  const medications = patientData.discharge_medications.medication.medications;

  Object.entries(medications).forEach(([medicationName, medicationData]) => {
    // Create a tile wrapper for each medication
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.width = "calc(33.33% - 16px)";
    tileWrapper.style.minWidth = "250px";
    tileWrapper.style.flexGrow = "1";
    // Create the field container
    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");
    fieldContainer.style.padding = "16px";
    fieldContainer.style.border = "1px solid #dee2e6";
    fieldContainer.style.borderRadius = "4px";
    fieldContainer.style.backgroundColor = "#fff";

    // Create and append the label element
    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = medicationData.label;
    if (medicationData.field_id) {
      label.textContent += ` (${medicationData.field_id})`;
    }

    // Add tooltip with description if available
    if (medicationData.description) {
      label.setAttribute("title", medicationData.description);
    }
    fieldContainer.appendChild(label);

    // Handle different input types
    if (medicationData.input_type === "select") {
      const select = document.createElement("select");
      select.name = medicationName;
      select.style.width = "100%";
      select.style.padding = "8px";
      select.style.marginTop = "8px";
      select.style.borderRadius = "4px";
      select.style.border = "1px solid #ced4da";

      // Add default empty option
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.text = "Select an option";
      defaultOption.disabled = true;
      defaultOption.selected = !medicationData.value;
      select.appendChild(defaultOption);

      // Update tile style based on current value
      updateTileStyle(fieldContainer, medicationData.value);

      // Add options from the medication data
      medicationData.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.value;
        optionElement.text = option.value;
        optionElement.selected = medicationData.value === option.value;
        select.appendChild(optionElement);
      });

      // Create a container for conditional fields (if_yes)
      const conditionalContainer = document.createElement("div");
      conditionalContainer.classList.add("conditional-container");
      conditionalContainer.style.marginTop = "12px";
      conditionalContainer.style.paddingLeft = "16px";
      conditionalContainer.style.borderLeft = "2px solid #8143d9";
      conditionalContainer.style.display =
        medicationData.value.startsWith("Yes") &&
        (medicationName.toLowerCase() === "aspirin" ||
          medicationName.toLowerCase() === "asprin")
          ? "block"
          : "none";

      // If this medication has conditional fields and is aspirin
      if (
        medicationData.if_yes &&
        (medicationName.toLowerCase() === "aspirin" ||
          medicationName.toLowerCase() === "asprin")
      ) {
        // Render each conditional field
        Object.entries(medicationData.if_yes).forEach(
          ([conditionalKey, conditionalData]) => {
            const conditionalFieldContainer = document.createElement("div");
            conditionalFieldContainer.classList.add("conditional-field");
            conditionalFieldContainer.style.marginBottom = "12px";

            // Create label for conditional field
            const conditionalLabel = document.createElement("label");
            conditionalLabel.classList.add("label");
            conditionalLabel.textContent = conditionalData.label;
            if (conditionalData.field_id) {
              conditionalLabel.textContent += ` (${conditionalData.field_id})`;
            }
            conditionalFieldContainer.appendChild(conditionalLabel);

            // Create select for conditional options
            if (conditionalData.options) {
              const conditionalSelect = document.createElement("select");
              conditionalSelect.name = `${medicationName}_${conditionalKey}`;
              conditionalSelect.style.width = "100%";
              conditionalSelect.style.padding = "8px";
              conditionalSelect.style.marginTop = "8px";
              conditionalSelect.style.borderRadius = "4px";
              conditionalSelect.style.border = "1px solid #ced4da";

              // Add default empty option
              const defaultOption = document.createElement("option");
              defaultOption.value = "";
              defaultOption.text = "Select an option";
              defaultOption.disabled = true;
              defaultOption.selected = !conditionalData.value;
              conditionalSelect.appendChild(defaultOption);

              // Add options from the conditional data
              conditionalData.options.forEach((option) => {
                const optionElement = document.createElement("option");
                optionElement.value = option.value;
                optionElement.text = option.value;
                optionElement.selected = conditionalData.value === option.value;
                conditionalSelect.appendChild(optionElement);
              });

              // Add event listener to update the model
              conditionalSelect.addEventListener("change", (e) => {
                medicationData.if_yes[conditionalKey].value = e.target.value;
                medicationData.if_yes[conditionalKey].modified_by =
                  "ABSTRACTOR";
                conditionalModifiedByDisplay.textContent = "ABSTRACTOR";
                updateTileStyle(fieldContainer, e.target.value);
                updateContainerHighlight(
                  container,
                  patientData.discharge_medications
                );
              });

              conditionalFieldContainer.appendChild(conditionalSelect);
            }

            // Create modified_by display for conditional field
            const conditionalModifiedByDisplay = document.createElement("span");
            conditionalModifiedByDisplay.style.display = "block";
            conditionalModifiedByDisplay.style.textAlign = "right";
            conditionalModifiedByDisplay.style.marginTop = "4px";
            conditionalModifiedByDisplay.style.color = "#8143d9";
            conditionalModifiedByDisplay.style.fontSize = "12px";
            conditionalModifiedByDisplay.textContent =
              conditionalData.modified_by || "";

            conditionalFieldContainer.appendChild(conditionalModifiedByDisplay);
            conditionalContainer.appendChild(conditionalFieldContainer);
          }
        );
      }

      // Add event listener to show/hide conditional fields
      select.addEventListener("change", (e) => {
        medicationData.value = e.target.value;
        medicationData.modified_by = "ABSTRACTOR";
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateTileStyle(fieldContainer, e.target.value);
        updateContainerHighlight(container, patientData.discharge_medications);

        // Show conditional fields only for aspirin with Yes selected
        if (
          e.target.value.startsWith("Yes") &&
          (medicationName.toLowerCase() === "aspirin" ||
            medicationName.toLowerCase() === "asprin") &&
          medicationData.if_yes
        ) {
          conditionalContainer.style.display = "block";
        } else {
          conditionalContainer.style.display = "none";
        }
      });

      fieldContainer.appendChild(select);
      fieldContainer.appendChild(conditionalContainer);
    } else if (medicationData.input_type === "multi_select") {
      // Initialize the value as an array if it's not already
      if (!Array.isArray(medicationData.value)) {
        medicationData.value = medicationData.value
          ? [medicationData.value]
          : [];
      }

      // Create a simpler dropdown implementation
      // Create dropdown container with relative positioning
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";

      // Make sure all parent containers allow overflow
      fieldContainer.style.overflow = "visible";
      const tileWrapper = fieldContainer.parentElement;
      if (tileWrapper && tileWrapper.classList.contains("tile-wrapper")) {
        tileWrapper.style.overflow = "visible";
      }
      // Also set the container's overflow to visible
      container.style.overflow = "visible";

      // Create dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.cursor = "pointer";
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";
      dropdownHeader.style.backgroundColor = "#fff";

      // Display selected values or placeholder
      const selectedText = document.createElement("span");
      selectedText.classList.add("selected-text");

      // Function to update the selected text display
      const updateSelectedText = () => {
        if (medicationData.value.length === 0) {
          selectedText.textContent = "Select options...";
        } else if (medicationData.value.length === 1) {
          const selectedOption = medicationData.options.find(
            (opt) => opt.value === medicationData.value[0]
          );
          selectedText.textContent = selectedOption
            ? selectedOption.value
            : medicationData.value[0];
        } else {
          selectedText.textContent = `${medicationData.value.length} options selected`;
        }
      };

      updateSelectedText();

      // Add dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create dropdown content (initially hidden)
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      // Position the dropdown directly under the header
      dropdownContent.style.position = "fixed"; // Use fixed positioning to ensure visibility
      dropdownContent.style.width = "350px"; // Fixed width to ensure content is visible
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.overflowX = "hidden";
      dropdownContent.style.backgroundColor = "#fff";
      dropdownContent.style.border = "1px solid #ccc";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
      dropdownContent.style.zIndex = "9999"; // Very high z-index to ensure it's on top

      // Create a checkbox for each option
      medicationData.options.forEach((option) => {
        const checkboxWrapper = document.createElement("div");
        checkboxWrapper.classList.add("checkbox-wrapper");
        checkboxWrapper.style.display = "flex";
        checkboxWrapper.style.alignItems = "center";
        checkboxWrapper.style.padding = "8px 12px";
        checkboxWrapper.style.borderBottom = "1px solid #eee";
        checkboxWrapper.style.textAlign = "left";
        checkboxWrapper.style.cursor = "pointer";

        const input = document.createElement("input");
        input.type = "checkbox";
        input.name = `${medicationName}-${option.id}`;
        input.value = option.id;
        input.id = `${medicationName}-${option.id}`;
        input.style.marginRight = "4px"; // Add a small space between checkbox and text

        // Check if this option is in the selected values array
        input.checked = medicationData.value.includes(option.value);

        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(medicationData.value)
            ? [...medicationData.value]
            : [];

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(option.value)) {
              currentValues.push(option.value);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== option.value);
          }

          // Update the model
          medicationData.value = currentValues;
          medicationData.modified_by = "ABSTRACTOR";

          // Update selected text display
          updateSelectedText();

          // Update UI
          updateTileStyle(
            fieldContainer,
            currentValues.length > 0 ? "filled" : ""
          );
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.discharge_medications
          );
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${medicationName}-${option.id}`);
        optionLabel.style.marginLeft = "0";
        optionLabel.style.display = "inline-block";
        optionLabel.style.whiteSpace = "nowrap";
        optionLabel.style.cursor = "pointer";
        optionLabel.style.flexGrow = "1";

        // Display field_id in parentheses if available
        if (option.field_id) {
          optionLabel.innerText = `${option.value} (${option.field_id})`;
        } else {
          optionLabel.innerText = option.value;
        }

        // Create a wrapper for the checkbox and label to ensure they're tightly aligned
        const inputLabelWrapper = document.createElement("div");
        inputLabelWrapper.style.display = "flex";
        inputLabelWrapper.style.alignItems = "center";
        inputLabelWrapper.style.gap = "0";

        inputLabelWrapper.appendChild(input);
        inputLabelWrapper.appendChild(optionLabel);
        checkboxWrapper.appendChild(inputLabelWrapper);
        dropdownContent.appendChild(checkboxWrapper);
      });

      // Function to position the dropdown content properly
      const positionDropdown = () => {
        const headerRect = dropdownHeader.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // Position dropdown below the header
        dropdownContent.style.top = `${headerRect.bottom}px`;

        // Ensure dropdown doesn't go off the right edge of the screen
        const rightEdge = headerRect.left + 250; // 250px is our dropdown width
        if (rightEdge > viewportWidth) {
          // Align to the right edge of the header instead
          dropdownContent.style.left = `${headerRect.right - 250}px`;
        } else {
          dropdownContent.style.left = `${headerRect.left}px`;
        }

        // Set max height based on available space
        const spaceBelow = viewportHeight - headerRect.bottom;
        const maxHeight = Math.max(100, Math.min(200, spaceBelow - 20));
        dropdownContent.style.maxHeight = `${maxHeight}px`;
      };

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !dropdownContainer.contains(e.target) &&
          !dropdownContent.contains(e.target)
        ) {
          dropdownContent.style.display = "none";
          dropdownArrow.innerHTML = "&#9662;";
        }
      });

      // Simple function to close the dropdown
      const closeDropdown = () => {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
        window.removeEventListener("scroll", closeDropdown);
      };

      // Clean up event listener when the component is removed
      const cleanupFunc = () => {
        if (document.body.contains(dropdownContent)) {
          document.body.removeChild(dropdownContent);
        }
        // Remove scroll event listener
        window.removeEventListener("scroll", closeDropdown);
      };

      // Store the cleanup function for potential future use
      dropdownContainer.cleanupFunc = cleanupFunc;

      // Add header to container, but add content to document body for better visibility
      dropdownContainer.appendChild(dropdownHeader);

      // Add the dropdown content to the document body when needed
      const showDropdown = () => {
        if (!document.body.contains(dropdownContent)) {
          document.body.appendChild(dropdownContent);
        }
        dropdownContent.style.display = "block";
        positionDropdown();
      };

      // Set up the click handler for the dropdown
      dropdownHeader.onclick = (e) => {
        e.stopPropagation();
        const isOpen = dropdownContent.style.display === "block";

        if (!isOpen) {
          showDropdown();
          dropdownArrow.innerHTML = "&#9652;"; // Up arrow
          window.addEventListener("scroll", closeDropdown);
        } else {
          closeDropdown();
        }
      };

      // Update the tile style based on whether any options are selected
      updateTileStyle(
        fieldContainer,
        medicationData.value.length > 0 ? "filled" : ""
      );

      fieldContainer.appendChild(dropdownContainer);

      // Create a container for conditional fields (if_yes) for multi_select
      if (
        medicationData.if_yes &&
        (medicationName.toLowerCase() === "aspirin" ||
          medicationName.toLowerCase() === "asprin")
      ) {
        const conditionalContainer = document.createElement("div");
        conditionalContainer.classList.add("conditional-container");
        conditionalContainer.style.marginTop = "12px";
        conditionalContainer.style.paddingLeft = "16px";
        conditionalContainer.style.borderLeft = "2px solid #8143d9";

        // Check if any value starting with "Yes" is in the selected values
        const hasYesSelected = medicationData.value.some((val) =>
          val.startsWith("Yes")
        );
        conditionalContainer.style.display = hasYesSelected ? "block" : "none";

        // Render each conditional field
        Object.entries(medicationData.if_yes).forEach(
          ([conditionalKey, conditionalData]) => {
            const conditionalFieldContainer = document.createElement("div");
            conditionalFieldContainer.classList.add("conditional-field");
            conditionalFieldContainer.style.marginBottom = "12px";

            // Create label for conditional field
            const conditionalLabel = document.createElement("label");
            conditionalLabel.classList.add("label");
            conditionalLabel.textContent = conditionalData.label;
            if (conditionalData.field_id) {
              conditionalLabel.textContent += ` (${conditionalData.field_id})`;
            }
            conditionalFieldContainer.appendChild(conditionalLabel);

            // Create select for conditional options
            if (conditionalData.options) {
              const conditionalSelect = document.createElement("select");
              conditionalSelect.name = `${medicationName}_${conditionalKey}`;
              conditionalSelect.style.width = "100%";
              conditionalSelect.style.padding = "8px";
              conditionalSelect.style.marginTop = "8px";
              conditionalSelect.style.borderRadius = "4px";
              conditionalSelect.style.border = "1px solid #ced4da";

              // Add default empty option
              const defaultOption = document.createElement("option");
              defaultOption.value = "";
              defaultOption.text = "Select an option";
              defaultOption.disabled = true;
              defaultOption.selected = !conditionalData.value;
              conditionalSelect.appendChild(defaultOption);

              // Add options from the conditional data
              conditionalData.options.forEach((option) => {
                const optionElement = document.createElement("option");
                optionElement.value = option.value;
                optionElement.text = option.value;
                optionElement.selected = conditionalData.value === option.value;
                conditionalSelect.appendChild(optionElement);
              });

              // Add event listener to update the model
              conditionalSelect.addEventListener("change", (e) => {
                medicationData.if_yes[conditionalKey].value = e.target.value;
                medicationData.if_yes[conditionalKey].modified_by =
                  "ABSTRACTOR";
                conditionalModifiedByDisplay.textContent = "ABSTRACTOR";
                updateTileStyle(fieldContainer, e.target.value);
                updateContainerHighlight(
                  container,
                  patientData.discharge_medications
                );
              });

              conditionalFieldContainer.appendChild(conditionalSelect);
            }

            // Create modified_by display for conditional field
            const conditionalModifiedByDisplay = document.createElement("span");
            conditionalModifiedByDisplay.style.display = "block";
            conditionalModifiedByDisplay.style.textAlign = "right";
            conditionalModifiedByDisplay.style.marginTop = "4px";
            conditionalModifiedByDisplay.style.color = "#8143d9";
            conditionalModifiedByDisplay.style.fontSize = "12px";
            conditionalModifiedByDisplay.textContent =
              conditionalData.modified_by || "";

            conditionalFieldContainer.appendChild(conditionalModifiedByDisplay);
            conditionalContainer.appendChild(conditionalFieldContainer);
          }
        );

        // Add event listener to the multi-select to show/hide conditional fields
        // We need to monitor changes to the medicationData.value array

        // Add a MutationObserver to watch for changes in the dropdown text
        const observer = new MutationObserver(() => {
          // Check if any value starting with "Yes" is in the selected values
          const hasYesSelected = medicationData.value.some((val) =>
            val.startsWith("Yes")
          );
          conditionalContainer.style.display = hasYesSelected
            ? "block"
            : "none";
        });

        observer.observe(selectedText, {
          childList: true,
          characterData: true,
          subtree: true,
        });

        fieldContainer.appendChild(conditionalContainer);
      }
    }

    // Create the modified_by display element
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "8px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    modifiedByDisplay.textContent = medicationData.modified_by || "";

    // Assemble tile wrapper
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);

    // Append the tile wrapper to the prescribed container
    prescribedContainer.appendChild(tileWrapper);
  });

  medicationSection.appendChild(prescribedContainer);
  container.appendChild(medicationSection);

  // Add verified checkbox at the bottom right
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";

  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "discharge-medications-verified-checkbox";
  containerVerifiedCheckbox.checked =
    patientData.discharge_medications.verified.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";

  containerVerifiedCheckbox.addEventListener("change", (e) => {
    patientData.discharge_medications.verified.value = e.target.checked
      ? "True"
      : "False";
    patientData.discharge_medications.verified.modified_by = "ABSTRACTOR";
    updateContainerHighlight(container, patientData.discharge_medications);
  });

  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "discharge-medications-verified-checkbox"
  );
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";

  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);

  // Apply initial container highlight
  updateContainerHighlight(container, patientData.discharge_medications);
}
