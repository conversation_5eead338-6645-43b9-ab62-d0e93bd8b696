<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CorMetrix Abstractor</title>

    <link rel="stylesheet" href="../css/login.css" />
    <link rel="stylesheet" href="index.css" />
    <link rel="stylesheet" href="../css/style.css" />
    <link rel="stylesheet" href="../css/output.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Flatpickr CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <link rel="icon" href="images/scheduler_logo.png" type="image/x-icon" />

    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.1/css/all.min.css"
      integrity="sha512-5Hs3dF2AEPkpNAR7UiOHba+lRSJNeM2ECkwxUIxC1Q/FLycGTbNapWXB4tP889k5T5Ju8fs4b1P5z/iB4nMfSQ=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="datepicker.material.css" />
    <script src="datepicker.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>
  </head>

  <body class="h-screen max-h-screen flex flex-col">
    <header class="bg-[#F1F1FB] px-3 md:px-8 flex flex-col md:flex-row">
      <div
        class="flex flex-row items-center md:justify-start justify-between w-full md:w-auto pt-4"
      >
        <div class="flex justify-start items-center space-x-3">
          <img
            src="../svg/calendar-icon.svg"
            class="h-[2rem] w-[2rem] md:h-auto md:w-auto"
          />
          <h1 class="text-lg font-semibold text-black hidden md:block">
            CorMetrix <br />Abstractor
          </h1>
          <h1 class="text-lg font-semibold text-black block md:hidden">
            CorMetrix Abstractor
          </h1>
        </div>
        <img
          src="/svg/menu.svg"
          class="block md:hidden cursor-pointer w-6 h-6"
          onclick="opensidemenu()"
        />
      </div>
      <div class="border md:hidden mt-4"></div>
      <div
        class="justify-between flex flex-row space-x-3 md:items-end mb-3 mr-10 w-full"
      >
        <div class="flex items-start mb-3 sm:ml-2 md:ml-[5rem] mt-4 md:my-0">
          <h1
            class="text-xl md:text-4xl font-bold text-purple-600 capitalize md:truncate"
            id="page-title"
          ></h1>
        </div>
      </div>
    </header>

    <div class="bg-[#F1F1FB] flex-1 md:overflow-hidden flex flex-row">
      <aside
        id="logo-sidebar"
        class="w-60 h-full max-h-full bg-[#F1F1FB] flex-col justify-between transform transition-transform duration-300 ease-in-out hidden md:flex"
      >
        <nav class="flex-1 px-2.5 bg-[#F1F1FB] ml-4 mt-4 mb-16">
          <ul class="space-y-4 font-[400] flex flex-col h-full justify-between">
            <li>
              <a
                id="tasksNav"
                class="nav-link flex items-center p-3 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                onclick="tasks()"
              >
                <img src="/svg/schedule.svg" alt="Icon" class="w-6 h-6" />

                <span
                  class="text-center text-xl transition-all ease-in-out duration-200"
                  >Tasks</span
                >
              </a>
            </li>
            <li>
              <a
                id="logoutNav"
                class="nav-link flex items-center p-3 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                onclick="logout()"
              >
                <img src="/svg/logout.svg" alt="Logout Icon" class="w-6 h-6" />
                <span
                  class="text-center text-xl transition-all ease-in-out duration-200"
                  >Logout</span
                >
              </a>
            </li>
          </ul>
        </nav>
      </aside>

      <main
        id="content-div"
        class="relative flex-1 md:overflow-hidden transition-all ease-in-out duration-300 bg-slate-50 border rounded-[20px] mb-10 ml-4 mr-4 md:mr-10"
      >
        <div
          id="loader"
          class="w-full hidden h-full flex justify-center items-center overflow-hidden absolute top-0 left-0 bg-opacity-60 z-30 bg-[#F1F1FB]"
        >
          <div class="loader"></div>
        </div>
        <div
          id="abstractorCalendar"
          class="justify-start md:justify-center flex-col md:flex-row p-2 w-full max-w-full md:max-w-full h-full max-h-full overflow-scroll custom-scrollbar-vertical"
        >
          <div id="patientList"></div>
        </div>
        <div
          id="abstractorTasks"
          class="justify-start md:justify-center flex-col md:flex-row p-2 w-full max-w-full md:max-w-full h-full max-h-full overflow-scroll custom-scrollbar-vertical"
        >
          <div id="taskList" class="mt-14"></div>
        </div>
      </main>
    </div>

    <div
      id="loader-with-opacity"
      class="absolute hidden justify-center items-center z-40 inset-0 w-full h-full bg-gray-200 bg-opacity-50 backdrop-blur-sm transition-all duration-100 ease-in-out"
    >
      <div class="loader"></div>
    </div>
    <div id="side-menu" class="hidden md:hidden">
      <div class="fixed inset-0 bg-black bg-opacity-50 z-40"></div>
      <div
        class="fixed right-0 top-0 h-full w-[15rem] bg-[#F1F1FB] shadow-lg z-50 transform transition-transform duration-300 flex flex-col"
      >
        <img
          src="/svg/clear.svg"
          alt="Close"
          class="absolute m-4 transform w-5 h-5 cursor-pointer"
          onclick="closesidebar()"
        />
        <nav class="px-2.5 overflow-y-auto bg-[#F1F1FB] mx-4 my-10">
          <ul class="space-y-2 font-[400]">
            <li>
              <a
                id="logoutNav"
                class="nav-link flex items-center p-2 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                onclick="logout()"
              >
                <img src="/svg/logout.svg" alt="Icon" class="w-5 h-5" />
                <span
                  class="text-center text-lg transition-all ease-in-out duration-200"
                >
                  Logout
                </span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>

   <!-- Chatbot Button -->
<div id="chatbot-button" class="fixed bottom-6 right-6 z-50">
  <button
    id="toggle-chatbot"
    onclick="toggleChatbot()"
    class="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-purple-700"
  >
    <i id="chatbot-icon" class="fas fa-robot text-2xl"></i>
  </button>
</div>

<!-- Chatbot Modal -->
<div
  id="chatbot-modal"
  class="fixed right-6 bottom-[-100%] z-50 hidden flex justify-end items-end transition-all duration-500 ease-in-out"
>
  <div
    class="bg-white rounded-lg shadow-lg w-[550px] h-[85vh] relative"
    onclick="event.stopPropagation();"
  >
    <iframe
      id="chatbot-iframe"
      src="/chatbot/index.html"
      class="w-full h-full rounded-lg border-2 border-[#8143d9] shadow-lg"
    ></iframe>
  </div>
</div>


    <script src="../js/jquery.min.js"></script>
    <script src="../js/main.js"></script>
    <script src="../config.js"></script>
    <script src="../js/api.js"></script>
    <script src="./js/tasks.js"></script>
    <script src="./js/calendar.js"></script>
    <!-- <script src="index.js"></script> -->
    <script src="../chatbot/index.js"></script>
  </body>
</html>
