{"follow_up_adjudication": {"verified": "", "follow_up_adjudication": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient.last_name", "value_type": "string", "q_prompt": "What is the patient's last name, including any hyphenated names, as recorded on arrival at this facility?", "description": "\n**Coding Instruction:**\nIndicate the patient's last name. Hyphenated names should be recorded with a hyphen.\n**Target Value:**\nThe value on arrival at this facility"}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient.first_name", "value_type": "string", "q_prompt": "What is the patient's first name as recorded upon arrival at this facility?", "description": "\n**Coding Instruction:**\nIndicate the patient's first name.\n**Target Value:**\nThe value on arrival at this facility"}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": "", "modified_by": "", "value_source": "Database", "value_path": "patient._id", "value_type": "object", "q_prompt": "What is the unique Patient ID number created and automatically inserted by the software that identifies this patient at this facility?", "description": "\n**Coding Instruction:**\nIndicate the number created and automatically inserted by the software that uniquely identifies this patient.\n**Target Value:**\nThe value on arrival at this facility"}, "reference_procedure_start_date_time": {"field_id": "11001", "label": "Reference Procedure Start Date/Time", "input_type": "date_time", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the reference procedure start date and time on the follow-up assessment date?", "description": "\n**Coding Instruction:**\nIndicate the reference procedure start date and time on the follow-up assessment date.\n**Target Value:**\nThe value on Follow-up"}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the optional patient identifier, such as the medical record number, that can be associated with the patient?", "description": "\n**Coding Instruction:**\nIndicate an optional patient identifier, such as medical record number, that can be associated with the patient.\n**Target Value:**\nN/A"}, "study_patient_id": {"field_id": "3030", "label": "Study Patient ID", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the research study patient identification number as assigned by the research protocol? If the patient is in more than one research study, please list each identification number separately.", "description": "\n**Coding Instruction:**\nIndicate the research study patient identification number as assigned by the research protocol.\n**Target Value:**\nN/A"}, "follow_up_event": {"field_id": "14967", "label": "Follow-up Event", "input_type": "string", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What event is being adjudicated? Please select from the following options:\n\n- Endocarditis\n- Iatrogenic ASD (requiring intervention)\n- LAA Occlusion Reintervention\n- Myocardial Infarction\n- PCI\n- Pericarditis\n- Unplanned Cardiac Surgery\n- Unplanned Intervention\n- Deep Vein Thrombosis\n- New Requirement for Dialysis\n- Non-Device Related Readmission\n- Systemic Thromboembolism (other than stroke) (Complete Adjudication)\n- Device Explant\n- Device Fracture\n- Device Migration\n- Device Related Readmission\n- Device Systemic Embolism\n- Device Thrombus\n- Hemorrhagic Stroke (Complete Adjudication)\n- Intracranial Hemorrhage (other than hemorrhagic stroke) (Complete Adjudication)\n- Ischemic Stroke (Complete Adjudication)\n- TIA (Complete Adjudication)\n- Undetermined Stroke (Complete Adjudication)\n- Access Site Bleeding (Complete Adjudication)\n- GI Bleeding (Complete Adjudication)\n- Hematoma (Complete Adjudication)\n- Hemothorax (not requiring drainage) (Complete Adjudication)\n- Hemothorax (requiring drainage) (Complete Adjudication)\n- Other Hemorrhage (non-intracranial) (Complete Adjudication)\n- Pericardial Effusion (requiring open cardiac surgery) (Complete Adjudication)\n- Pericardial Effusion with tamponade (requiring percutaneous drainage) (Complete Adjudication)\n- Pericardial Effusion without tamponade (requiring percutaneous drainage) (Complete Adjudication)\n- Retroperitoneal Bleeding (Complete Adjudication)\n- Vascular Complications (Complete Adjudication)\n- AV Fistula (requiring surgical repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring endovascular repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring surgical repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring thrombin injection only) (Complete Adjudication)\n- Pulmonary Embolism", "description": "\n**Coding Instruction:**\nIndicate the event being adjudicated.\n**Target Value:**\nThe value on Follow-up"}, "follow_up_event_date": {"field_id": "14386", "label": "Follow-up Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the clinical event being adjudicated occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the clinical event being adjudicated occurred.\n**Target Value:**\nN/A"}}, "neurologic": {"follow_up_adjudication_status": {"field_id": "14969", "label": "Follow-up Adjudication Status", "input_type": "multi_input_field", "options": ["Alive", "Deceased"], "if_deceased": {"date_of_death": {"field_id": "14970", "label": "Date of Death", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the patient was declared deceased?", "description": "\n**Coding Instruction:**\nIndicate the date the patient was declared deceased.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the patient's adjudication status (alive or deceased) on the date the adjudication was performed?", "description": "\n**Coding Instruction:**\nIndicate whether the patient was alive or deceased on the date the adjudication was performed.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "symptom_onset_date": {"field_id": "14976", "label": "Symptom Onset Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date of symptom onset associated with this event? Please provide any date between discharge or last follow-up and the current follow-up.", "description": "\n**Coding Instruction:**\nIndicate the date of symptom onset associated with this event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "neurologic_deficit_with_rapid_onset": {"field_id": "14977", "label": "Neurologic Deficit with Rapid Onset", "input_type": "multi_input_field", "options": ["No", "Yes"], "if_yes": {"neurologic_deficit_clinical_presentation": {"field_id": "14978", "label": "Neurologic Deficit Clinical Presentation", "input_type": "radio", "options": ["Stroke-related", "Non-Stroke-related"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the clinical presentation of the neurologic deficit observed in the patient? Please specify if it was stroke-related or non-stroke-related.", "description": "\n**Coding Instruction:**\nIndicate the clinical presentation of the neurologic deficit.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient experience a sudden onset of a focal or global neurologic deficit, regardless of the duration of symptoms, at any point between discharge or last follow-up and the current follow-up?", "description": "\n**Coding Instruction:**\nIndicate if the patient had a sudden onset of a focal or global neurologic deficit regardless of the duration of symptoms.\nRapid onset means sudden or maximal within minutes.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "diagnosis_confirmation_by_neurology": {"field_id": "14979", "label": "Diagnosis Confirmation by Neurology", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the diagnosis confirmed by a neurologist or a neurosurgeon?", "description": "\n**Coding Instruction:**\nIndicate if the diagnosis was confirmed by a neurologist or a neurosurgeon.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "brain_imaging_performed": {"field_id": "14980", "label": "Brain Imaging Performed", "input_type": "multi_input_field", "options": ["No", "Yes"], "if_yes": {"brain_imaging_type": {"field_id": "14981", "label": "Brain Imaging Type", "input_type": "multi_select", "options": [{"id": "1", "value": "Cerebral Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Other"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of neurologic imaging was performed? Please select from the following options:\n- Cerebral Angiography\n- Computed Tomography\n- Magnetic Resonance Imaging", "description": "\n**Coding Instruction:**\nIndicate the type of neurologic imaging which was performed.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "deficit_type": {"field_id": "14982", "label": "Deficit Type", "input_type": "multi_input_field", "options": [{"id": "1", "value": "No deficit"}, {"id": "2", "value": "Infarction"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Both"}], "if_hemorrhage": {"hemorrhage_type": {"field_id": "14983", "label": "Hemorrhage Type", "input_type": "multi_select", "options": [{"id": "1", "value": "Intracerebral"}, {"id": "2", "value": "Subarachnoid"}, {"id": "3", "value": "Subdural"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of hemorrhagic stroke did the patient experience? Please specify the location of the hemorrhage as either Intracerebral, Subarachnoid, or Subdural.", "description": "\n**Coding Instruction:**\nFor patients presenting with an intracranial hemorrhage, indicate the hemorrhage location.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of deficit was identified by the neuroimaging study? The options are:\n- No deficit\n- Infarction\n- Hemorrhage\n- Both", "description": "\n**Coding Instruction:**\nIndicate the type of deficit identified by the neuroimaging study.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo any neuro imaging, such as CT, MRI, or cerebral angiography, to confirm the diagnosis?", "description": "\n**Coding Instruction:**\nIndicate if neuro imaging (such as CT, MRI, cerebral angiography) was performed in an attempt to confirm the diagnosis.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "subsequent_iv_rtpa_administered": {"field_id": "14984", "label": "Subsequent IV rtPA Administered", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient receive intravenous recombinant tissue plasminogen activator (rtPA) as a treatment option related to this event?", "description": "\n**Coding Instruction:**\nIndicate if intravascular (IV) recombinant tissue plasminogen activator (rtPA) was used as a treatment option related to this event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "subsequent_endovascular_therapeutic_intervention": {"field_id": "14985", "label": "Subsequent Endovascular Therapeutic Intervention", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo any endovascular interventional therapy as a treatment option related to this event between discharge or last follow-up and the current follow-up?", "description": "\n**Coding Instruction:**\nIndicate if an endovascular interventional therapy was performed as a treatment option related to this event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "symptoms_duration": {"field_id": "14986", "label": "Symptoms Duration", "input_type": "select", "options": [{"id": "1", "value": "< 1 Hour"}, {"id": "2", "value": "1 – 24 Hours"}, {"id": "3", "value": "> 24 Hours"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the duration of the neurologic symptoms experienced by the patient? Please select one of the following options:\n- Less than 1 Hour\n- 1 - 24 Hours\n- Greater than 24 Hours", "description": "\n**Coding Instruction:**\nIndicate the duration (in hours) of the neurologic symptoms.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "trauma": {"field_id": "14987", "label": "<PERSON>rauma", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient experience any physical trauma within 24 hours prior to the neurologic event?", "description": "\n**Coding Instruction:**\nIndicate if the patient experienced a physical trauma within 24 hours prior to the neurologic event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "modified_rankin_scale_mrs": {"field_id": "14988", "label": "Modified Rankin Scale (mRS)", "input_type": "select", "dependency": "14989", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "6: Death"}, {"id": "8", "value": "Not Administered"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the patient's functional ability according to the Modified Rankin Scale (mRS) administered following the event? Please select one of the following options:\n\n1. 0: No symptoms at all\n2. 1: No significant disability despite symptoms (Able to carry out all usual duties and activities)\n3. 2: Slight disability (Unable to carry out all previous activities, but able to look after own affairs without assistance)\n4. 3: Moderate disability (Requiring some help, but able to walk without assistance)\n5. 4: Moderately severe disability (Unable to walk without assistance and unable to attend to own bodily needs without assistance)\n6. 5: Severe disability (Bedridden, incontinent and requiring constant nursing care and attention)\n7. 6: Death", "description": "\n**Coding Instruction:**\nIndicate the patients functional ability according to the modified Rankin Scale (mRS) administered following the event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "modified_rankin_scale_mrs_na": {"field_id": "14989", "label": "Not Administered", "input_type": "checkbox", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient not receive the modified Rankin Scale (mRS) assessment following the event?", "description": "\n**Coding Instruction:**\nIndicate if the modified Rankin Scale (mRS) was not administered following the event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "procedure_related_neurologic_event": {"field_id": "14990", "label": "Procedure Related Neurologic Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the likelihood that the neurologic event is related to the LAAO procedure based on the clinician's best clinical judgment? Please select one of the following options:\n- Certain: The clinical adverse event occurs in a plausible time relationship to the procedure and cannot be explained by concurrent disease or other drugs or devices.\n- Probable: The clinical adverse event occurs within a reasonable time sequence to the procedure and it is unlikely to be attributed to concurrent disease or other drugs or devices.\n- Possible: The clinical adverse event occurs within a reasonable time sequence to the procedure, but the event could also be explained by concurrent disease or other drugs or devices.\n- Unlikely: The clinical adverse event, based upon its temporal relationship to the procedure, makes a causal relationship improbable. Additionally, other drugs, devices or underlying disease provide plausible explanations.\n- Unclassifiable: The clinical adverse event is reported yet more data is essential for a proper assessment OR the clinical adverse event is reported yet the causality cannot be judged because information is insufficient or contradictory, and cannot be supplemented or verified.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "device_related_neurologic_event": {"field_id": "15015", "label": "Device Related Neurologic Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the likelihood that the follow-up device-related neurologic event is related to the LAAO device based on the clinician's best clinical judgment? Please select one of the following options: Certain, Probable, Possible, Unlikely, or Unclassifiable.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}}, "bleeding": {"follow_up_adjudication_status": {"field_id": "14971", "label": "Follow-up Adjudication Status", "input_type": "multi_input_field", "options": ["Alive", "Deceased"], "if_deceased": {"adjudication_date_of_death": {"field_id": "14972", "label": "Adjudication Date of Death", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the patient was declared deceased?", "description": "\n**Coding Instruction:**\nIndicate the date the patient was declared deceased.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the adjudication status of the patient (alive or deceased) on the date the adjudication was performed?", "description": "\n**Coding Instruction:**\nIndicate whether the patient was alive or deceased on the date the adjudication was performed.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "invasive_intervention_required": {"field_id": "14991", "label": "Invasive Intervention Required", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What surgical or percutaneous intervention was required to treat the patient for the bleeding event?", "description": "\n**Coding Instruction:**\nIndicate if there was a surgical or percutaneous intervention required to treat the patient for this bleeding event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "rbc_transfusion": {"field_id": "14992", "label": "RBC Transfusion", "input_type": "multi_input_field", "options": ["No", "Yes"], "if_yes": {"number_of_rbc_units_transfused": {"field_id": "14993", "label": "Number of RBC Units Transfused", "input_type": "string", "value": "", "metric": "unit(s)", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the number of PRBC units transfused for the treatment of the bleeding event between discharge or last follow-up and the current follow-up?", "description": "\n**Coding Instruction:**\nIndicate the number of PRBC units transfused for treatment of this bleeding event.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "hemoglobin_pre_transfusion": {"field_id": "14994", "label": "Hemoglobin Pre-Transfusion", "input_type": "string", "value": "", "metric": "g/dL", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the lowest hemoglobin (Hgb) value (g/dL) obtained via lab assay or point of care assay between the intra or post-procedure bleeding event and prior to the transfusion?", "description": "\n**Coding Instruction:**\nIndicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, between the intra or post procedure bleeding event and prior to the transfusion.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was there at least one transfusion of packed red blood cells (PRBCs) given to treat the patient for this bleeding event?", "description": "\n**Coding Instruction:**\nIndicate if there was at least one transfusion of PRBCs given to treat the patient for this bleeding event.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "diagnostic_imaging_performed": {"field_id": "14995", "label": "Diagnostic Imaging Performed", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was any diagnostic imaging, such as CT or MRI, performed to confirm the diagnosis between discharge or the last follow-up and the current follow-up?", "description": "\n**Coding Instruction:**\nIndicate if imaging (such as CT, MRI) was performed in an attempt to confirm the diagnosis.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "end_organ_damage": {"field_id": "14996", "label": "End Organ Damage", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient experience end organ damage following the bleeding event?", "description": "\n**Coding Instruction:**\nIndicate if the patient was diagnosed with end organ damage after this bleeding event.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "bleeding_event_readmission": {"field_id": "14975", "label": "Bleeding Event Readmission", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Based on the provided coder dictionary entry, here is a well-formed question to retrieve the requested data:\n\n\"Was there a readmission associated with a bleeding-related diagnosis since the patient's discharge or last follow-up?\"", "description": "\n**Coding Instruction:**\nIndicate if a readmission was associated with a bleeding related diagnosis.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "major_surgery_within_past_30_days": {"field_id": "14997", "label": "Major Surgery within Past 30 days", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did the patient undergo any major surgery within 30 days prior to the bleeding event?", "description": "\n**Coding Instruction:**\nIndicate if the patient underwent surgery within 30 days prior to this bleeding event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "percutaneous_coronary_intervention_within_past_30_days": {"field_id": "14998", "label": "Percutaneous Coronary Intervention within Past 30 days", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Based on the provided coder dictionary entry, here is a well-formed question to retrieve the requested data:\n\n\"Did the patient undergo a percutaneous coronary intervention (PCI) within 30 days prior to the bleeding event? Please include any procedures involving the placement of an angioplasty guide wire, balloon, stent, atherectomy, brachytherapy, or thrombectomy catheter into a native coronary artery or coronary artery bypass graft for the purpose of mechanical coronary revascularization.\"", "description": "\n**Coding Instruction:**\nIndicate if the patient had a percutaneous coronary artery or percutaneous valvular intervention within 30 days prior to this bleeding event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "procedure_related_bleeding_event": {"field_id": "14999", "label": "Procedure Related Bleeding Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the likelihood that the bleeding event observed and documented in the medical record, which was associated with a hematocrit drop of 10% and/or a hemoglobin drop of 3 g/dL or that required transfusion or surgical intervention, is related to the LAAO procedure based on the clinician's best clinical judgment? Please select from the following options: Certain, Probable, Possible, Unlikely, or Unclassifiable.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "device_related_bleeding_event": {"field_id": "15000", "label": "<PERSON>ce Related Bleeding Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "**Question:**\n\nBased on the clinician's best clinical judgment, how likely is it that the observed bleeding event, which was associated with a hematocrit drop of 10% and/or a hemoglobin drop of 3 g/dL or required transfusion or surgical intervention, is related to the LAAO device? Please select one of the following options:\n\n- Certain: The clinical adverse event occurs in a plausible time relationship to the procedure and cannot be explained by concurrent disease or other drugs or devices.\n- Probable: The clinical adverse event occurs within a reasonable time sequence to the procedure and it is unlikely to be attributed to concurrent disease or other drugs or devices.\n- Possible: The clinical adverse event occurs within a reasonable time sequence to the procedure, but the event could also be explained by concurrent disease or other drugs or devices.\n- Unlikely: The clinical adverse event, based upon its temporal relationship to the procedure, makes a causal relationship improbable. Additionally, other drugs, devices or underlying disease provide plausible explanations.\n- Unclassifiable: The clinical adverse event is reported yet more data is essential for a proper assessment OR the clinical adverse event is reported yet the causality cannot be judged because information is insufficient or contradictory, and cannot be supplemented or verified.", "description": "\n**Coding Instruction:**\nIndicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}}, "systemic_thromboembolism": {"follow_up_adjudication_status": {"field_id": "14973", "label": "Follow-up Adjudication Status", "input_type": "multi_input_field", "options": ["Alive", "Deceased"], "if_deceased": {"date_of_death": {"field_id": "14974", "label": "Date of Death", "input_type": "date", "format": "mm/dd/yyyy", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the patient was declared deceased?", "description": "\n**Coding Instruction:**\nIndicate the date the patient was declared deceased.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "death_cause": {"field_id": "15016", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the cause of death for the patient? Specifically, was it due to systemic thromboembolization, focal end-organ hypoperfusion resulting from systemic thromboembolism, or a therapeutic intervention to treat systemic thromboembolism?", "description": "\n**Coding Instruction:**\nIf deceased, indicate if the patient's death cause was due to systemic thromboembolization, or focal end-organ hypoperfusion resulting from systemic thromboembolism, or therapeutic intervention to treat systemic thromboembolism.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the patient's adjudication status (alive or deceased) on the date the adjudication was performed?", "description": "\n**Coding Instruction:**\nIndicate whether the patient was alive or deceased on the date the adjudication was performed.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "focal_end_organ_hypoperfusion_present": {"field_id": "15001", "label": "Focal End-Organ Hypoperfusion Present", "input_type": "radio", "options": ["No", "Yes"], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Is there evidence of focal end-organ hypoperfusion resulting from the systemic thromboembolism event?", "description": "\n**Coding Instruction:**\nIndicate if focal end-organ hypoperfusion resulted from the systemic thromboembolism event.\n**Target Value:**\nAny value between discharge or last follow up and the current follow up"}, "systemic_thromboembolization_imaging_evidence": {"field_id": "15002", "label": "Systemic Thromboembolization Imaging Evidence", "input_type": "multi_input_field", "options": ["No", "Yes"], "if_yes": {"imaging_method": {"field_id": "15003", "label": "Imaging Method", "input_type": "multi_select", "options": [{"id": "1", "value": "Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Ultrasound"}, {"id": "5", "value": "Other Imaging"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What imaging method was used to identify systemic thromboembolism during the follow-up period? Please select from the following options:\n- Angiography\n- Computed Tomography\n- Magnetic Resonance Imaging\n- Ultrasound\n- Other Imaging", "description": "\n**Coding Instruction:**\nIndicate the imaging method to identify systemic thromboembolism.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the imaging evidence indicating systemic thromboembolism between discharge or last follow-up and the current follow-up?", "description": "\n**Coding Instruction:**\nIndicate if imaging evidence indicated systemic thromboembolism.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}, "therapeutic_intervention_performed": {"field_id": "15004", "label": "Therapeutic Intervention Performed", "input_type": "multi_input_field", "options": ["No", "Yes"], "if_yes": {"intervention_type": {"field_id": "15005", "label": "Intervention Type", "input_type": "multi_select", "options": [{"id": "1", "value": "Catheter"}, {"id": "2", "value": "Pharmacological"}, {"id": "3", "value": "Surgical"}, {"id": "4", "value": "Other"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What type of intervention was performed, selecting from the following options: Catheter, Pharmacological, Surgical, or Other?", "description": "\n**Coding Instruction:**\nIndicate the intervention type.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was any pharmacological, catheter, surgical, or other therapeutic intervention performed to treat the systemic thromboembolism?", "description": "\n**Coding Instruction:**\nIndicate if any pharmacological, catheter, surgical, or other therapeutic intervention was performed to treat the systemic thromboembolism.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}, "current_medications_at_time_of_event": {"field_id": "15007", "label": "Current Medications at Time of Event", "medications": {"fondaparinux": {"field_id": "15006", "label": "Fondaparinux", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "heparin_derivative": {"field_id": "", "label": "Heparin Derivative", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "low_molecular_weight_heparin": {"field_id": "", "label": "Low Molecular Weight Heparin", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "unfractionated_heparin": {"field_id": "", "label": "Unfractionated Heparin", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "warfarin": {"field_id": "", "label": "Warfarin", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "aspirin_81_100_mg": {"field_id": "", "label": "Aspirin (81-100 mg)", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "aspirin_101_324_mg": {"field_id": "", "label": "Aspirin (101-324 mg)", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "aspirin_325_mg": {"field_id": "", "label": "Aspirin 325 mg", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "aspirin_dipyridamole": {"field_id": "", "label": "Aspirin/Dipyridamole", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "vorapaxar": {"field_id": "", "label": "Vorapaxar", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "apixaban": {"field_id": "", "label": "Apixaban", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "dabigatran": {"field_id": "", "label": "Dabigatran", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "edoxaban": {"field_id": "", "label": "Edoxaban", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "rivaroxaban": {"field_id": "", "label": "Rivaroxaban", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "cangrelor": {"field_id": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "clopidogrel": {"field_id": "", "label": "Clopidogrel", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "other_p2y12": {"field_id": "", "label": "Other P2Y12", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "prasugrel": {"field_id": "", "label": "Prasug<PERSON>", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "ticagrelor": {"field_id": "", "label": "Ticagrelor", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}, "ticlopidine": {"field_id": "", "label": "Ticlopidine", "input_type": "radio", "options": ["Yes", "No"], "value": "", "modified_by": "", "value_source": "Embeddings"}}, "q_prompt": "Was the patient taking or being administered any medication at the time of the event? Please indicate \"Yes\" or \"No\".", "description": "\n**Coding Instruction:**\nIndicate if the patient was taking or being administered the medication at the time of the event.\n**Target Value:**\nAll values between discharge or last follow up and the current follow up"}}}