<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.3/dist/tailwind.min.css" rel="stylesheet"> -->
    <script src="https://cdn.tailwindcss.com"></script>
    <title>Document</title>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">

    <!-- Loader -->
    <div class="flex items-center justify-center">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
    </div>
    <script src="../js/api.js"></script>
    <script>
        
        async function call(){
            const queryParams = new URLSearchParams(window.location.search);
            const code = queryParams.get("code");
            if(code){
                const redirect_url = `${window.location.origin}/dashboard/callback.html`;
                const {result} = await apiRequest(`/code?redirect_url=${redirect_url}&code=${code}`,"GET", null, false);
                const { refresh_token, access_token } = result;
                localStorage.setItem('dash_refresh_token', refresh_token);
                localStorage.setItem('dash_access_token', access_token);
                window.location.pathname = "/dashboard/index.html";
            }
        }
        call();
    </script>
</body>
</html>