<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Patient Demographics</title>
    <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
  />
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
  />
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
  />
  <script src="https://cdn.tailwindcss.com"></script>





    <link rel="stylesheet" href="index.css" />


  </head>
  <body>

    <div
    id="modal"
    class="fixed inset-0 z-50 flex flex-col hidden items-center justify-start bg-gray-800 bg-opacity-50 transition-opacity duration-300"
  >
  <!-- bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 -->
    <div
      id="modal-content"
      class="relative mt-12 bg-green-600 rounded-lg shadow-xl p-8 max-w-sm w-full mt-16 transform scale-95 transition-transform duration-300"
    >
      <!-- Close button -->
      <button
        id="modal-close"
        class="absolute top-2 right-2 text-white hover:text-gray-200 focus:outline-none transform hover:scale-110 transition-transform"
      >
        &times;
      </button>

      <!-- Modal message -->
      <p id="modal-message" class="text-white text-center text-lg font-semibold"></p>
    </div>

  </div>





    </div>
    <div class="save-button-container">
      <button class="save-button" id="cancelButton">Cancel</button>
      <button class="save-button" id="saveButton">Save</button>
    </div>

    <div class="accordion-container">


      <!-- Patient Demographics Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('patientDemographics')"
        >
          <span>Patient Demographics</span><span class="arrow">▼</span>
        </div>
        <div id="patientDemographics" class="accordion-content"></div>
      </div>

      <!-- Episode of Care Section -->
      <div class="accordion-item">
        <div class="accordion-title" onclick="toggleAccordion('episodeOfCare')">
          <span>Episode of Care (Admission)</span><span class="arrow">▼</span>
        </div>
        <div id="episodeOfCare" class="accordion-content"></div>
      </div>

      <!-- History and Risk Factors Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('historyAndRiskFactors')"
        >
          <span>History and Risk Factors (Prior to First Procedure)</span
          ><span class="arrow">▼</span>
        </div>
        <div id="historyAndRiskFactors" class="accordion-content"></div>
      </div>

      <!-- Additional Stroke and Bleeding Risk Factors Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('additionalStrokeAndBleedingRiskFactors')"
        >
          <span>Additional Stroke and Bleeding Risk Factors</span
          ><span class="arrow">▼</span>
        </div>
        <div
          id="additionalStrokeAndBleedingRiskFactors"
          class="accordion-content"
        ></div>
      </div>

      <!-- History – Rhythm History Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('historyRhythmHistory')"
        >
          <span>History – Rhythm History</span><span class="arrow">▼</span>
        </div>
        <div id="historyRhythmHistory" class="accordion-content"></div>
      </div>

      <!-- History – Interventions Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('historyInterventions')"
        >
          <span>History – Interventions</span><span class="arrow">▼</span>
        </div>
        <div id="historyInterventions" class="accordion-content"></div>
      </div>

      <!-- Additional History and Risk Factors Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('additionalHistoryRiskFactors')"
        >
          <span>Additional History and Risk Factors</span
          ><span class="arrow">▼</span>
        </div>
        <div id="additionalHistoryRiskFactors" class="accordion-content"></div>
      </div>

      <!-- Epicardial Access Assessment Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('epicardialAccessAssessment')"
        >
          <span>Epicardial Access Assessment</span><span class="arrow">▼</span>
        </div>
        <div id="epicardialAccessAssessment" class="accordion-content"></div>
      </div>

      <!-- Diagnostic Studies Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('diagnosticStudies')"
        >
          <span>Diagnostic Studies</span><span class="arrow">▼</span>
        </div>
        <div id="diagnosticStudies" class="accordion-content"></div>
      </div>

      <!-- Physical Exam and Labs Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('physicalExamAndLabs')"
        >
          <span>Physical Exam and Labs</span><span class="arrow">▼</span>
        </div>
        <div id="physicalExamAndLabs" class="accordion-content"></div>
      </div>

      <!-- Pre-Procedure Medications Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('preProcedureMedications')"
        >
          <span>Pre-Procedure Medications</span><span class="arrow">▼</span>
        </div>
        <div id="preProcedureMedications" class="accordion-content"></div>
      </div>

      <!-- Pre-Procedure Diagnostics Section -->
      <!-- <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('preProcedureDiagnostics')"
        >
          <span>Pre-Procedure Diagnostics</span><span class="arrow">▼</span>
        </div>
        <div id="preProcedureDiagnostics" class="accordion-content"></div>
      </div> -->

      <!-- Procedure and Device Information Section -->
      <div class="accordion-item">
        <div class="accordion-title" onclick="toggleAccordion('procedureInfo')">
          <span>Procedure Information</span
          ><span class="arrow">▼</span>
        </div>
        <div id="procedureInfo" class="accordion-content"></div>
      </div>

      <!--Radiation Exposure Section -->
      <div class="accordion-item">
        <div class="accordion-title" onclick="toggleAccordion('radiationExposure')">
          <span>Radiation Exposure</span><span class="arrow">▼</span>
        </div>
        <div id="radiationExposure" class="accordion-content"></div>
      </div>

      <!--Intraprocedure Aniticoagulation Strategy Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('intraProcedureAnticoagulationStrategy')"
        >
          <span>Intraprocedure Anticoagulation Strategy</span
          ><span class="arrow">▼</span>
        </div>
        <div
          id="intraProcedureAnticoagulationStrategy"
          class="accordion-content"
        ></div>
      </div>

      <!-- Intra- or Post-Procedure Events Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('intraPostProcedureEvents')"
        >
          <span>Intra- or Post-Procedure Events</span
          ><span class="arrow">▼</span>
        </div>
        <div id="intraPostProcedureEvents" class="accordion-content"></div>
      </div>

      <!-- Post Procedure Labs Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('postProcedureLabs')"
        >
          <span>Post Procedure Labs</span><span class="arrow">▼</span>
        </div>
        <div id="postProcedureLabs" class="accordion-content"></div>
      </div>

      <!-- Discharge Information Section -->
      <div class="accordion-item">
        <div class="accordion-title" onclick="toggleAccordion('discharge')">
          <span>Discharge Information</span><span class="arrow">▼</span>
        </div>
        <div id="discharge" class="accordion-content"></div>
      </div>

      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('dischargeMedications')"
        >
          <span>Discharge Medications</span><span class="arrow">▼</span>
        </div>
        <div id="dischargeMedications" class="accordion-content"></div>
      </div>

      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('inHospitalAdjudication')"
        >
          <span>In Hospital Adjudication</span><span class="arrow">▼</span>
        </div>
        <div id="inHospitalAdjudication" class="accordion-content"></div>
      </div>

      <div class="accordion-item">
        <div class="accordion-title" onclick="toggleAccordion('followUp')">
          <span>Follow Up</span><span class="arrow">▼</span>
        </div>
        <div id="followUp" class="accordion-content"></div>
      </div>



      <!--Follow Up Diagnostic Studies Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpDiagnosticStudies')"
        >
          <span>Follow Up Diagnostic Studies</span><span class="arrow">▼</span>
        </div>
        <div id="followUpDiagnosticStudies" class="accordion-content"></div>
      </div>

      <!--Follow Up Physical Exam and Labs Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpPhysicalExamAndLabs')"
        >
          <span>Follow Up Physical Exam and Labs</span><span class="arrow">▼</span>
        </div>
        <div id="followUpPhysicalExamAndLabs" class="accordion-content"></div>
      </div>

      <!--Follow Up Barthel Evaluation Section -->
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpBarthelIndexEvaluation')"
        >
          <span>Follow Up - Barthel Index Evaluation</span><span class="arrow">▼</span>
        </div>
        <div id="followUpBarthelIndexEvaluation" class="accordion-content"></div>
      </div>

      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpMedications')"
        >
          <span>Follow Up - Medications</span><span class="arrow">▼</span>
        </div>
        <div id="followUpMedications" class="accordion-content"></div>
      </div>

      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpAnticoagulationTherapy')"
        >
          <span>Follow Up - Anticoagulation Therapy</span
          ><span class="arrow">▼</span>
        </div>
        <div
          id="followUpAnticoagulationTherapy"
          class="accordion-content"
        ></div>
      </div>
      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpEvents')"
        >
          <span>Follow Up - Events</span><span class="arrow">▼</span>
        </div>
        <div id="followUpEvents" class="accordion-content"></div>
      </div>


      <div class="accordion-item">
        <div
          class="accordion-title"
          onclick="toggleAccordion('followUpAdjudication')"
        >
          <span>Follow Up Adjudication</span><span class="arrow">▼</span>
        </div>
        <div id="followUpAdjudication" class="accordion-content"></div>
      </div>

    </div>

<!-- Back to Top Button -->
<div id="back-to-top-container" class="fixed bottom-6 right-24 z-50" style="display: none;">
  <button
    id="back-to-top-btn"
    onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
    class="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-purple-700 transition-transform duration-300 hover:transform hover:translate-y-[-3px]"
    style="background-color: #8143d9;"
  >
    <i class="fas fa-chevron-up text-xl"></i>
  </button>
</div>

<!-- Chatbot Button -->
<div id="chatbot-button" class="fixed bottom-6 right-6 z-50">
  <button
    id="toggle-chatbot"
    onclick="toggleChatbot()"
    class="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-purple-700"
  >
    <i id="chatbot-icon" class="fas fa-robot text-2xl"></i>
  </button>
</div>

<!-- Chatbot Modal -->
<div
  id="chatbot-modal"
  class="fixed right-6 bottom-6 z-50 hidden flex justify-end items-end transition-all duration-500 ease-in-out"
>
  <div
    class="bg-white rounded-lg shadow-lg w-[550px] h-[85vh] relative"
    onclick="event.stopPropagation();"
  >
    <iframe
      id="chatbot-iframe"
      src="/chatbot/index.html"
      class="w-full h-full rounded-lg border-2 border-[#8143d9] shadow-lg"
    ></iframe>
  </div>
</div>


    <script src="../js/jquery.min.js"></script>
    <script src="../js/main.js" defer></script>
    <script src="../config.js" defer></script>
    <script src="../js/api.js" defer></script>
    <script src="./js/calendar.js" defer></script>
    <script src="index.js" defer></script>
    <script src="../chatbot/index.js" defer></script>


  </body>
</html>
