import {
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  // Check if all fields are filled (not used currently but kept for future use)
  Object.entries(data).forEach(([key, field]) => {
    if (key === "verified") return;
    if (!field.value || field.value.toString().trim() === "") {
      // Field is empty
    }
  });

  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

export function renderFollowUpMedication(patientData) {
  const container = document.getElementById("followUpMedications");
  container.innerHTML = "";
  container.style.position = "relative";
  // Don't set display to grid here - let the accordion control it
  // container.style.display = "grid";
  container.style.gridTemplateColumns = "repeat(3, minmax(0, 1fr))"; // Three equal columns with min width 0
  container.style.gap = "24px"; // Increased gap between grid items
  container.style.padding = "24px"; // Increased padding around the container
  container.style.boxSizing = "border-box";
  container.style.width = "100%";
  container.style.marginTop = "16px"; // Added margin at the top

  if (
    typeof patientData.follow_up_medications.verified !== "object" ||
    patientData.follow_up_medications.verified === null
  ) {
    patientData.follow_up_medications.verified = {
      value: patientData.follow_up_medications.verified || "False",
      modified_by: "",
    };
  }

  // Handle the medications field which contains all the medication elements
  if (
    patientData.follow_up_medications.medications &&
    patientData.follow_up_medications.medications.elements
  ) {
    // Create a heading for the medications section
    const medicationsHeading = document.createElement("h2");
    medicationsHeading.textContent =
      patientData.follow_up_medications.medications.label;
    medicationsHeading.style.gridColumn = "1 / -1"; // Span all columns
    medicationsHeading.style.marginBottom = "16px";
    medicationsHeading.style.fontWeight = "bold";
    container.appendChild(medicationsHeading);

    // Render each medication element
    Object.entries(
      patientData.follow_up_medications.medications.elements
    ).forEach(([medicationKey, medicationValue]) => {
      renderMedicationElement(
        medicationKey,
        medicationValue,
        container,
        patientData
      );
    });
  }

  // Handle any other top-level fields (though we don't expect any based on the JSON structure)
  Object.entries(patientData.follow_up_medications).forEach(([key, value]) => {
    if (key === "verified" || key === "medications") return;

    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px";

    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = `${value.label} (${value.field_id})`;
    if (value.description) {
      label.setAttribute("title", value.description);
    }
    fieldContainer.appendChild(label);

    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    if (!value.modified_by) {
      value.modified_by = "";
    }
    modifiedByDisplay.textContent = value.modified_by;

    if (value.input_type === "string" || value.input_type === "text") {
      const input = document.createElement("input");
      input.type = "text";
      input.name = key;
      input.placeholder = `Enter ${value.label}`;
      input.value = value.value || "";

      updateTileStyle(fieldContainer, input.value);

      let previousValue = input.value;

      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use validation utilities with temporary callback
        let isValid = false;
        validateStringInput(currentValue, value.field_id, (validatedValue) => {
          isValid = true;
          previousValue = validatedValue;

          // Update model and UI only if value is valid
          patientData.follow_up_medications[key].value = validatedValue;
          patientData.follow_up_medications[key].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, validatedValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_medications
          );

          // Update input value if validation modified it
          if (validatedValue !== currentValue) {
            e.target.value = validatedValue;
          }
        });

        // If validation failed, revert to the previous value
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      fieldContainer.appendChild(input);
    } else if (value.input_type === "date") {
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${key}-display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(value.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = key;
      dateInput.value = value.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      updateTileStyle(fieldContainer, dateInput.value);

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        patientData.follow_up_medications[key].value = selectedDate;
        patientData.follow_up_medications[key].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, selectedDate);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.follow_up_medications);
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      fieldContainer.appendChild(dateWrapper);
    } else if (value.input_type === "radio") {
      // Convert the value if it's an object
      if (typeof value.value === "object" && value.value !== null) {
        // If the value is an object, extract just the display value
        value.value = value.value.value || "";
      }

      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "row";
      radioContainer.style.flexWrap = "nowrap";
      radioContainer.style.gap = "15px";
      radioContainer.style.width = "100%";

      value.options.forEach((option) => {
        // Check if option is an object with id and value properties or just a string
        const isOptionObject = typeof option === "object" && option !== null;
        const optionValue = isOptionObject ? option.value : option;
        const optionId = isOptionObject ? option.id : option;

        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "inline-flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginRight = "20px";
        radioWrapper.style.whiteSpace = "nowrap";

        const input = document.createElement("input");
        input.type = "radio";
        input.name = key;
        input.value = optionId;
        input.id = `${key}-${optionId}`;
        input.checked = value.value === optionValue;

        // Store the display value as a data attribute for reference
        input.dataset.displayValue = optionValue;

        input.addEventListener("change", (event) => {
          if (event.target.checked) {
            // Store the display value in patientData
            patientData.follow_up_medications[key].value =
              event.target.dataset.displayValue;
            patientData.follow_up_medications[key].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, event.target.dataset.displayValue);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.follow_up_medications
            );
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${optionId}`);
        optionLabel.innerText = optionValue;
        if (value.description) {
          optionLabel.setAttribute("title", value.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });
      updateTileStyle(fieldContainer, value.value || "");
      fieldContainer.appendChild(radioContainer);
    } else if (value.input_type === "select") {
      // Convert the value if it's an object
      if (typeof value.value === "object" && value.value !== null) {
        // If the value is an object, extract just the display value
        value.value = value.value.value || "";
      }

      const select = document.createElement("select");
      select.name = key;
      if (!value.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        select.appendChild(defaultOption);
      }

      value.options.forEach((option) => {
        // Check if option is an object with id and value properties or just a string
        const isOptionObject = typeof option === "object" && option !== null;
        const optionValue = isOptionObject ? option.value : option;
        const optionId = isOptionObject ? option.id : option;

        const optionElement = document.createElement("option");
        optionElement.value = optionId;
        optionElement.innerText = optionValue;
        // Mark as selected if it matches the value
        if (value.value && value.value === optionValue) {
          optionElement.selected = true;
        }
        select.appendChild(optionElement);
      });

      // Find the selected option for initial styling
      let selectedDisplayValue = "";
      if (value.value) {
        const selectedOption = value.options.find((option) => {
          const optVal =
            typeof option === "object" && option !== null
              ? option.value
              : option;
          return optVal === value.value;
        });
        selectedDisplayValue = selectedOption
          ? typeof selectedOption === "object"
            ? selectedOption.value
            : selectedOption
          : "";
      }
      updateTileStyle(fieldContainer, selectedDisplayValue);

      select.addEventListener("change", (event) => {
        // Find the selected option to get its display value
        const selectedOpt = value.options.find((option) => {
          const optId =
            typeof option === "object" && option !== null ? option.id : option;
          return optId === event.target.value;
        });

        // Get the display value from the selected option
        const newValue = selectedOpt
          ? typeof selectedOpt === "object"
            ? selectedOpt.value
            : selectedOpt
          : "";

        patientData.follow_up_medications[key].value = newValue;
        patientData.follow_up_medications[key].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.follow_up_medications);
      });

      fieldContainer.appendChild(select);
    } else if (value.input_type === "multi_select") {
      // Initialize the value as an array if it's not already
      if (!Array.isArray(value.value)) {
        value.value = value.value ? [value.value] : [];
      }

      // Create a dropdown container
      const dropdownContainer = document.createElement("div");
      dropdownContainer.classList.add("dropdown-container");
      dropdownContainer.style.position = "relative";
      dropdownContainer.style.width = "100%";

      // Create the dropdown header/button
      const dropdownHeader = document.createElement("div");
      dropdownHeader.classList.add("dropdown-header");
      dropdownHeader.style.padding = "8px 12px";
      dropdownHeader.style.border = "1px solid #ccc";
      dropdownHeader.style.borderRadius = "4px";
      dropdownHeader.style.cursor = "pointer";
      dropdownHeader.style.display = "flex";
      dropdownHeader.style.justifyContent = "space-between";
      dropdownHeader.style.alignItems = "center";

      // Create the selected text display
      const selectedText = document.createElement("div");
      selectedText.classList.add("selected-text");
      selectedText.style.overflow = "hidden";
      selectedText.style.textOverflow = "ellipsis";
      selectedText.style.whiteSpace = "nowrap";

      // Create the dropdown arrow
      const dropdownArrow = document.createElement("span");
      dropdownArrow.innerHTML = "&#9662;"; // Down arrow
      dropdownArrow.style.marginLeft = "10px";

      // Add elements to the dropdown header
      dropdownHeader.appendChild(selectedText);
      dropdownHeader.appendChild(dropdownArrow);

      // Create the dropdown content container
      const dropdownContent = document.createElement("div");
      dropdownContent.classList.add("dropdown-content");
      dropdownContent.style.display = "none";
      dropdownContent.style.position = "absolute";
      dropdownContent.style.backgroundColor = "#f9f9f9";
      dropdownContent.style.minWidth = "100%";
      dropdownContent.style.boxShadow = "0px 8px 16px 0px rgba(0,0,0,0.2)";
      dropdownContent.style.zIndex = "1";
      dropdownContent.style.maxHeight = "200px";
      dropdownContent.style.overflowY = "auto";
      dropdownContent.style.border = "1px solid #ddd";
      dropdownContent.style.borderRadius = "4px";
      dropdownContent.style.marginTop = "4px";

      // Function to update the selected text display
      const updateSelectedText = () => {
        if (value.value.length === 0) {
          selectedText.innerText = "Select options...";
        } else {
          selectedText.innerText = value.value.join(", ");
        }
      };

      // Initial update of selected text
      updateSelectedText();

      // Function to show the dropdown
      const showDropdown = () => {
        dropdownContent.style.display = "block";
      };

      // Function to close the dropdown
      const closeDropdown = () => {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;"; // Down arrow
        window.removeEventListener("scroll", closeDropdown);
      };

      // Add options to the dropdown content
      value.options.forEach((option) => {
        // Check if option is an object with id and value properties or just a string
        const isOptionObject = typeof option === "object" && option !== null;
        const optionValue = isOptionObject ? option.value : option;
        const optionId = isOptionObject ? option.id : option;

        const optionContainer = document.createElement("div");
        optionContainer.style.padding = "8px 12px";
        optionContainer.style.cursor = "pointer";
        optionContainer.style.display = "flex";
        optionContainer.style.alignItems = "center";
        optionContainer.style.gap = "8px";

        // Create checkbox for the option
        const input = document.createElement("input");
        input.type = "checkbox";
        input.id = `${key}-${optionId}`;
        input.checked = value.value.includes(optionValue);

        // Create label for the option
        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${optionId}`);
        optionLabel.innerText = optionValue;
        optionLabel.style.cursor = "pointer";
        optionLabel.style.flexGrow = "1";

        // Add event listener to the checkbox
        input.addEventListener("change", (e) => {
          // Get the current values array
          let currentValues = Array.isArray(value.value)
            ? [...value.value]
            : [];

          if (e.target.checked) {
            // Add the value if it's not already in the array
            if (!currentValues.includes(optionValue)) {
              currentValues.push(optionValue);
            }
          } else {
            // Remove the value if it's in the array
            currentValues = currentValues.filter((val) => val !== optionValue);
          }

          // Update the model
          patientData.follow_up_medications[key].value = currentValues;
          patientData.follow_up_medications[key].modified_by = "ABSTRACTOR";

          // Update selected text display
          updateSelectedText();

          // Update UI
          updateTileStyle(
            fieldContainer,
            currentValues.length > 0 ? "filled" : ""
          );
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_medications
          );
        });

        // Add elements to the option container
        optionContainer.appendChild(input);
        optionContainer.appendChild(optionLabel);
        dropdownContent.appendChild(optionContainer);
      });

      // Set up the click handler for the dropdown
      dropdownHeader.onclick = (e) => {
        e.stopPropagation();
        const isOpen = dropdownContent.style.display === "block";

        if (!isOpen) {
          showDropdown();
          dropdownArrow.innerHTML = "&#9652;"; // Up arrow
          window.addEventListener("scroll", closeDropdown);
        } else {
          closeDropdown();
        }
      };

      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (!dropdownContainer.contains(e.target)) {
          closeDropdown();
        }
      });

      // Update the tile style based on whether any options are selected
      updateTileStyle(fieldContainer, value.value.length > 0 ? "filled" : "");

      // Add elements to the dropdown container
      dropdownContainer.appendChild(dropdownHeader);
      dropdownContainer.appendChild(dropdownContent);
      fieldContainer.appendChild(dropdownContainer);
    } else if (value.input_type === "multi_input_field") {
      const multiInputContainer = document.createElement("div");
      multiInputContainer.classList.add("multi-input-container");

      // Create radio buttons for options
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      radioContainer.style.display = "flex";
      radioContainer.style.flexDirection = "row";
      radioContainer.style.flexWrap = "nowrap"; // Prevent wrapping to keep in one line
      radioContainer.style.gap = "15px";
      radioContainer.style.width = "100%";

      // Create a container for conditional content that will be shown/hidden
      const conditionalContainer = document.createElement("div");
      conditionalContainer.classList.add("conditional-content-container");
      conditionalContainer.style.marginTop = "10px";
      conditionalContainer.style.padding = "10px";
      conditionalContainer.style.border = "1px solid #eee";
      conditionalContainer.style.borderRadius = "4px";
      conditionalContainer.style.display = "none"; // Initially hidden
      conditionalContainer.style.width = "100%"; // Full width

      // Function to render conditional content based on selected option
      const renderConditionalContent = (selectedOption) => {
        // Clear previous content
        conditionalContainer.innerHTML = "";
        conditionalContainer.style.display = "none";

        let conditionalKey = null;

        // Determine which conditional key to use based on selected option
        if (selectedOption === "Yes" && value.if_yes) {
          conditionalKey = "if_yes";
        } else if (selectedOption === "No" && value.if_no) {
          conditionalKey = "if_no";
        } else if (selectedOption === "Alive" && value.if_alive) {
          conditionalKey = "if_alive";
        } else if (selectedOption === "Deceased" && value.if_deceased) {
          conditionalKey = "if_deceased";
        }

        // If we have a conditional key, render its content
        if (conditionalKey && value[conditionalKey]) {
          conditionalContainer.style.display = "block";

          // Iterate through each field in the conditional content
          Object.entries(value[conditionalKey]).forEach(
            ([subKey, subField]) => {
              // Create a container for this sub-field
              const subFieldContainer = document.createElement("div");
              subFieldContainer.style.marginBottom = "15px";

              // Create and append the label for the sub-field
              const subLabel = document.createElement("label");
              subLabel.classList.add("label");
              subLabel.textContent = `${subField.label} ${
                subField.field_id ? `(${subField.field_id})` : ""
              }`;
              if (subField.description) {
                subLabel.setAttribute("title", subField.description);
              }
              subFieldContainer.appendChild(subLabel);

              // Render the appropriate input based on the sub-field's input_type
              if (
                subField.input_type === "string" ||
                subField.input_type === "text"
              ) {
                const input = document.createElement("input");
                input.type = "text";
                input.name = `${key}-${subKey}`;
                input.value = subField.value || "";
                input.placeholder = "Enter value...";

                // Track previous value for validation
                let previousValue = input.value;

                // Add event listener for input changes
                input.addEventListener("input", (e) => {
                  const currentValue = e.target.value;

                  // Use validation utilities with temporary callback
                  let isValid = false;
                  validateStringInput(
                    currentValue,
                    subField.field_id,
                    (validatedValue) => {
                      isValid = true;
                      previousValue = validatedValue;

                      // Update the sub-field value
                      value[conditionalKey][subKey].value = validatedValue;
                      value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                      modifiedByDisplay.textContent = "ABSTRACTOR";
                      updateContainerHighlight(
                        container,
                        patientData.follow_up_medications
                      );

                      // Update the input value if validation modified it
                      if (validatedValue !== currentValue) {
                        e.target.value = validatedValue;
                      }
                    }
                  );

                  // If validation failed, revert to the previous value
                  if (!isValid) {
                    e.target.value = previousValue;
                  }
                });

                subFieldContainer.appendChild(input);
              } else if (subField.input_type === "date") {
                const dateWrapper = document.createElement("div");
                dateWrapper.style.position = "relative";

                // Create display input
                const displayInput = document.createElement("input");
                displayInput.type = "text";
                displayInput.name = `${key}-${subKey}-display`;
                displayInput.readOnly = true;
                displayInput.value = formatDisplayDate(subField.value);
                displayInput.placeholder = "MM/DD/YYYY";
                displayInput.style.cursor = "pointer";

                // Hidden date input
                const dateInput = document.createElement("input");
                dateInput.type = "date";
                dateInput.name = `${key}-${subKey}`;
                dateInput.value = subField.value || "";
                dateInput.style.position = "absolute";
                dateInput.style.opacity = "0";
                dateInput.style.cursor = "pointer";

                // Set max date to today
                const today = new Date().toISOString().split("T")[0];
                dateInput.max = today;

                dateInput.addEventListener("change", (e) => {
                  const selectedDate = e.target.value;
                  displayInput.value = formatDisplayDate(selectedDate);
                  value[conditionalKey][subKey].value = selectedDate;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.follow_up_medications
                  );
                });

                // Trigger date picker when clicking display input
                displayInput.addEventListener("click", () => {
                  dateInput.showPicker();
                });

                dateWrapper.appendChild(displayInput);
                dateWrapper.appendChild(dateInput);
                subFieldContainer.appendChild(dateWrapper);
              } else if (subField.input_type === "select") {
                const select = document.createElement("select");
                select.name = `${key}-${subKey}`;

                // Add default option if no value is set
                if (!subField.value) {
                  const defaultOption = document.createElement("option");
                  defaultOption.value = "";
                  defaultOption.innerText = "Select an option";
                  defaultOption.disabled = true;
                  defaultOption.selected = true;
                  select.appendChild(defaultOption);
                }

                subField.options.forEach((option) => {
                  // Check if option is an object with id and value properties or just a string
                  const isOptionObject =
                    typeof option === "object" && option !== null;
                  const optionValue = isOptionObject ? option.value : option;
                  const optionId = isOptionObject ? option.id : option;

                  const optionElement = document.createElement("option");
                  optionElement.value = optionId;
                  optionElement.innerText = optionValue;
                  if (subField.value && subField.value === optionValue) {
                    optionElement.selected = true;
                  }
                  select.appendChild(optionElement);
                });

                select.addEventListener("change", (e) => {
                  const selectedOpt = subField.options.find((option) => {
                    const optId =
                      typeof option === "object" && option !== null
                        ? option.id
                        : option;
                    return optId === e.target.value;
                  });

                  const newValue = selectedOpt
                    ? typeof selectedOpt === "object"
                      ? selectedOpt.value
                      : selectedOpt
                    : "";

                  value[conditionalKey][subKey].value = newValue;
                  value[conditionalKey][subKey].modified_by = "ABSTRACTOR";
                  modifiedByDisplay.textContent = "ABSTRACTOR";
                  updateContainerHighlight(
                    container,
                    patientData.follow_up_medications
                  );
                });

                subFieldContainer.appendChild(select);
              }

              // Add the sub-field container to the conditional container
              conditionalContainer.appendChild(subFieldContainer);
            }
          );
        }
      };

      // Create radio buttons for each option
      value.options.forEach((option) => {
        // Check if option is an object with id and value properties or just a string
        const isOptionObject = typeof option === "object" && option !== null;
        const optionValue = isOptionObject ? option.value : option;
        // We're not using optionId for radio buttons, but keeping the pattern consistent

        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "inline-flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginRight = "20px";
        radioWrapper.style.whiteSpace = "nowrap";

        const input = document.createElement("input");
        input.type = "radio";
        input.name = key;
        input.value = optionValue;
        input.id = `${key}-${optionValue}`;
        input.checked = value.value === optionValue;

        // Update patientData and render conditional content when a radio is selected
        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            patientData.follow_up_medications[key].value = e.target.value;
            patientData.follow_up_medications[key].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.follow_up_medications
            );

            // Always clear any previously rendered content first
            conditionalContainer.innerHTML = "";
            conditionalContainer.style.display = "none";

            // Render conditional content based on selected option
            renderConditionalContent(e.target.value);
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${optionValue}`);
        optionLabel.innerText = optionValue;
        if (value.description) {
          optionLabel.setAttribute("title", value.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });

      // Apply initial style based on the selected option value
      updateTileStyle(fieldContainer, value.value || "");

      // Add radio container and conditional container to the multi-input container
      multiInputContainer.appendChild(radioContainer);
      multiInputContainer.appendChild(conditionalContainer);
      fieldContainer.appendChild(multiInputContainer);

      // If a value is already selected, render the conditional content
      if (value.value) {
        renderConditionalContent(value.value);
      }
    }

    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);
    container.appendChild(tileWrapper);
  });

  // Add extra padding at the bottom of the container to make room for the verified checkbox
  container.style.paddingBottom = "60px";

  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";
  verifiedContainer.style.marginTop = "20px";
  verifiedContainer.style.paddingTop = "10px";
  verifiedContainer.style.backgroundColor = "white";
  verifiedContainer.style.padding = "8px 16px";
  verifiedContainer.style.borderRadius = "4px";
  verifiedContainer.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";

  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "follow-up-medications-verified-checkbox";
  containerVerifiedCheckbox.checked =
    patientData.follow_up_medications.verified.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";

  containerVerifiedCheckbox.addEventListener("change", (e) => {
    patientData.follow_up_medications.verified.value = e.target.checked
      ? "True"
      : "False";
    patientData.follow_up_medications.verified.modified_by = "ABSTRACTOR";
    updateContainerHighlight(container, patientData.follow_up_medications);
  });

  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "follow-up-medications-verified-checkbox"
  );
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";
  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);

  updateContainerHighlight(container, patientData.follow_up_medications);
}

// Function to render a single medication element
function renderMedicationElement(
  medicationKey,
  medicationValue,
  container,
  patientData
) {
  // Create an outer wrapper to hold the field tile and the modified_by display
  const tileWrapper = document.createElement("div");
  tileWrapper.classList.add("tile-wrapper");
  tileWrapper.style.position = "relative";
  tileWrapper.style.marginBottom = "48px"; // Increased margin for more vertical space between tiles
  tileWrapper.style.width = "100%";
  tileWrapper.style.maxWidth = "100%";
  tileWrapper.style.minWidth = "0";
  tileWrapper.style.height = "100%";
  tileWrapper.style.boxSizing = "border-box";

  // Create the field container for the tile
  const fieldContainer = document.createElement("div");
  fieldContainer.classList.add("field-container");
  fieldContainer.style.display = "flex";
  fieldContainer.style.flexDirection = "column";
  fieldContainer.style.height = "100%";
  fieldContainer.style.boxSizing = "border-box";
  fieldContainer.style.padding = "16px";
  fieldContainer.style.border = "1px solid #dee2e6";
  fieldContainer.style.borderRadius = "4px";
  fieldContainer.style.backgroundColor = "#fff";
  fieldContainer.style.overflow = "hidden";

  // Create and append the label element
  const label = document.createElement("label");
  label.classList.add("label", "cursor-pointer");

  // Truncate the label text if it's too long
  const fullLabelText = `${medicationValue.label} (${medicationValue.field_id})`;
  const maxLabelLength = 25; // Maximum characters to display
  const displayText =
    fullLabelText.length > maxLabelLength
      ? fullLabelText.substring(0, maxLabelLength) + "..."
      : fullLabelText;

  label.textContent = displayText;
  label.style.height = "20px"; // Reduced fixed height for all labels
  label.style.overflow = "hidden";
  label.style.textOverflow = "ellipsis";
  label.style.whiteSpace = "nowrap";
  label.style.display = "block";
  label.style.marginBottom = "8px";
  label.style.fontSize = "14px"; // Smaller font size

  // Add tooltip with description if available
  if (medicationValue.description) {
    label.setAttribute("title", medicationValue.description);
  }

  // Also add the full label text as a tooltip for long labels
  label.setAttribute("title", fullLabelText);
  fieldContainer.appendChild(label);

  // Create the modified_by display element
  const modifiedByDisplay = document.createElement("span");
  modifiedByDisplay.style.display = "block";
  modifiedByDisplay.style.textAlign = "right";
  modifiedByDisplay.style.position = "absolute";
  modifiedByDisplay.style.bottom = "-25px"; // Position below the tile with more space
  modifiedByDisplay.style.right = "0";
  modifiedByDisplay.style.color = "#8143d9";
  modifiedByDisplay.style.fontSize = "12px";

  // Initialize modified_by if not set
  if (!medicationValue.modified_by) {
    medicationValue.modified_by = "";
  }
  modifiedByDisplay.textContent = medicationValue.modified_by;

  // Handle the input based on its type
  if (medicationValue.input_type === "multi_input_field") {
   

    // Create a container for the radio buttons
    const radioContainer = document.createElement("div");
    radioContainer.classList.add("radio-container");
    radioContainer.style.display = "flex";
    radioContainer.style.flexDirection = "column"; // Changed to column to stack options vertically
    radioContainer.style.gap = "10px"; // Reduced gap between vertical options
    radioContainer.style.width = "100%";
    radioContainer.style.marginTop = "10px"; // Add some space between label and options

    // Create radio buttons for each option
    medicationValue.options.forEach((option) => {
      // Check if option is an object with id and value properties or just a string
      const isOptionObject = typeof option === "object" && option !== null;
      const optionValue = isOptionObject ? option.value : option;
      const optionId = isOptionObject ? option.id : option;

      

      const radioWrapper = document.createElement("div");
      radioWrapper.style.display = "flex";
      radioWrapper.style.alignItems = "center";
      radioWrapper.style.marginBottom = "5px"; // Add bottom margin for spacing between options
      radioWrapper.style.whiteSpace = "normal"; // Allow text to wrap if needed

      const input = document.createElement("input");
      input.type = "radio";
      input.name = `radio-${medicationKey}`;
      input.value = optionValue;
      input.id = `${medicationKey}-${optionId}`;
      input.checked = medicationValue.value === optionValue;
      input.style.cursor = "pointer"; // Show pointer cursor on hover
      input.style.width = "18px"; // Slightly larger radio button
      input.style.height = "18px"; // Slightly larger radio button

      const optionLabel = document.createElement("label");
      optionLabel.setAttribute("for", `${medicationKey}-${optionId}`);
      optionLabel.innerText = optionValue;
      optionLabel.style.marginLeft = "10px"; // Increased margin for better spacing
      optionLabel.style.fontWeight = "normal"; // Normal font weight for options
      optionLabel.style.cursor = "pointer"; // Show pointer cursor on hover

      radioWrapper.appendChild(input);
      radioWrapper.appendChild(optionLabel);
      radioContainer.appendChild(radioWrapper);

      // Add event listener to update the data model when the radio button is selected
      input.addEventListener("change", (e) => {
        if (e.target.checked) {
          // Update the data model
          patientData.follow_up_medications.medications.elements[
            medicationKey
          ].value = e.target.value;
          patientData.follow_up_medications.medications.elements[
            medicationKey
          ].modified_by = "ABSTRACTOR";

          // Update the UI
          updateTileStyle(fieldContainer, e.target.value);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.follow_up_medications
          );

          // Check if this is the aspirin field (with either spelling) and it has if_yes content
          if (
            (medicationKey.toLowerCase() === "aspirin" ||
              medicationKey.toLowerCase() === "asprin") &&
            e.target.value === "Yes" &&
            medicationValue.if_yes
          ) {
            // Render the conditional content for aspirin dose
            renderAspirinDose(
              medicationValue.if_yes,
              fieldContainer,
              medicationKey,
              patientData
            );
          } else {
            // Remove any existing conditional content
            const existingConditional = fieldContainer.querySelector(
              ".conditional-content"
            );
            if (existingConditional) {
              existingConditional.remove();
            }
          }
        }
      });
    });

    // Apply initial styling
    updateTileStyle(fieldContainer, medicationValue.value || "");

    // Append the radio container to the field container
    fieldContainer.appendChild(radioContainer);

    // If this is the aspirin field (with either spelling) and it's set to Yes, render the dose field
    if (
      (medicationKey.toLowerCase() === "aspirin" ||
        medicationKey.toLowerCase() === "asprin") &&
      medicationValue.value === "Yes" &&
      medicationValue.if_yes
    ) {
      renderAspirinDose(
        medicationValue.if_yes,
        fieldContainer,
        medicationKey,
        patientData
      );
    }
  } else if (medicationValue.input_type === "select") {
    // Create a select element
    const select = document.createElement("select");
    select.name = medicationKey;

    // Add a default option if no value is selected
    if (!medicationValue.value) {
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.innerText = "Select an option";
      defaultOption.disabled = true;
      defaultOption.selected = true;
      select.appendChild(defaultOption);
    }

    // Add options to the select element
    medicationValue.options.forEach((option) => {
      const optionElement = document.createElement("option");

      // Check if option is an object with id and value properties or just a string
      const isOptionObject = typeof option === "object" && option !== null;
      const optionValue = isOptionObject ? option.value : option;
      const optionId = isOptionObject ? option.id : option;

     

      optionElement.value = optionId;
      optionElement.innerText = optionValue;

      // Mark as selected if it matches the value
      if (medicationValue.value && medicationValue.value === optionValue) {
        optionElement.selected = true;
      }

      select.appendChild(optionElement);
    });

    // Add event listener to update the data model when the selection changes
    select.addEventListener("change", (event) => {
      // Find the selected option to get its display value
      const selectedOption = medicationValue.options.find((option) => {
        // Check if option is an object with id and value properties or just a string
        const isOptionObject = typeof option === "object" && option !== null;
        const optionId = isOptionObject ? option.id : option;
        return optionId.toString() === event.target.value;
      });

    

      // Extract the display value based on the option type
      const selectedValue =
        typeof selectedOption === "object" && selectedOption !== null
          ? selectedOption.value
          : selectedOption;

     

      // Update the data model
      patientData.follow_up_medications.medications.elements[
        medicationKey
      ].value = selectedValue;
      patientData.follow_up_medications.medications.elements[
        medicationKey
      ].modified_by = "ABSTRACTOR";

      // Update the UI
      updateTileStyle(fieldContainer, selectedValue);
      modifiedByDisplay.textContent = "ABSTRACTOR";
      updateContainerHighlight(container, patientData.follow_up_medications);

      // Check if this is the aspirin field (with either spelling) and it has if_yes content
      if (
        (medicationKey.toLowerCase() === "aspirin" ||
          medicationKey.toLowerCase() === "asprin") &&
        selectedOption.value === "Yes" &&
        medicationValue.if_yes
      ) {
        // Render the conditional content for aspirin dose
        renderAspirinDose(
          medicationValue.if_yes,
          fieldContainer,
          medicationKey,
          patientData
        );
      } else {
        // Remove any existing conditional content
        const existingConditional = fieldContainer.querySelector(
          ".conditional-content"
        );
        if (existingConditional) {
          existingConditional.remove();
        }
      }
    });

    // Apply initial styling
    updateTileStyle(fieldContainer, medicationValue.value || "");

    // Append the select element to the field container
    fieldContainer.appendChild(select);

    // If this is the aspirin field (with either spelling) and it's set to Yes, render the dose field
    if (
      (medicationKey.toLowerCase() === "aspirin" ||
        medicationKey.toLowerCase() === "asprin") &&
      medicationValue.value === "Yes" &&
      medicationValue.if_yes
    ) {
      renderAspirinDose(
        medicationValue.if_yes,
        fieldContainer,
        medicationKey,
        patientData
      );
    }
  }

  // Append the field container and modified_by display to the tile wrapper
  tileWrapper.appendChild(fieldContainer);
  tileWrapper.appendChild(modifiedByDisplay);

  // Append the tile wrapper to the container
  container.appendChild(tileWrapper);
}

// Function to render the aspirin dose field
function renderAspirinDose(doseData, parentContainer, parentKey, patientData) {
  // Remove any existing conditional content
  const existingConditional = parentContainer.querySelector(
    ".conditional-content"
  );
  if (existingConditional) {
    existingConditional.remove();
  }

  // Create a container for the conditional content
  const conditionalContainer = document.createElement("div");
  conditionalContainer.classList.add("conditional-content");
  conditionalContainer.style.marginTop = "12px";
  conditionalContainer.style.paddingLeft = "16px";
  conditionalContainer.style.borderLeft = "2px solid #8143d9";
  conditionalContainer.style.display = "block";

  // Get the dose field data
  const doseField = doseData.dose;

  // Create and append the label for the dose field
  const doseLabel = document.createElement("label");
  doseLabel.classList.add("label");
  doseLabel.textContent = `${doseField.label} (${doseField.field_id})`;
  if (doseField.description) {
    doseLabel.setAttribute("title", doseField.description);
  }
  conditionalContainer.appendChild(doseLabel);

  // Create a select element for the dose
  const doseSelect = document.createElement("select");
  doseSelect.name = `${parentKey}-dose`;

  // Add a default option if no value is selected
  if (!doseField.value) {
    const defaultOption = document.createElement("option");
    defaultOption.value = "";
    defaultOption.innerText = "Select a dose";
    defaultOption.disabled = true;
    defaultOption.selected = true;
    doseSelect.appendChild(defaultOption);
  }

  // Add options to the select element
  doseField.options.forEach((option) => {
    const optionElement = document.createElement("option");

    // Check if option is an object with id and value properties or just a string
    const isOptionObject = typeof option === "object" && option !== null;
    const optionValue = isOptionObject ? option.value : option;
    const optionId = isOptionObject ? option.id : option;

   

    optionElement.value = optionId;
    optionElement.innerText = optionValue;

    // Mark as selected if it matches the value
    if (doseField.value && doseField.value === optionValue) {
      optionElement.selected = true;
    }

    doseSelect.appendChild(optionElement);
  });

  // Add event listener to update the data model when the selection changes
  doseSelect.addEventListener("change", (event) => {
    // Find the selected option to get its display value
    const selectedOption = doseField.options.find((option) => {
      // Check if option is an object with id and value properties or just a string
      const isOptionObject = typeof option === "object" && option !== null;
      const optionId = isOptionObject ? option.id : option;
      return optionId.toString() === event.target.value;
    });



    // Extract the display value based on the option type
    const selectedValue =
      typeof selectedOption === "object" && selectedOption !== null
        ? selectedOption.value
        : selectedOption;

   

    // Update the data model
    patientData.follow_up_medications.medications.elements[
      parentKey
    ].if_yes.dose.value = selectedValue;
    patientData.follow_up_medications.medications.elements[
      parentKey
    ].if_yes.dose.modified_by = "ABSTRACTOR";

    // Update the UI
    const parentModifiedBy = parentContainer.parentNode.querySelector("span");
    if (parentModifiedBy) {
      parentModifiedBy.textContent = "ABSTRACTOR";
    }
  });

  // Append the select element to the conditional container
  conditionalContainer.appendChild(doseSelect);

  // Append the conditional container to the parent container
  parentContainer.appendChild(conditionalContainer);
}
