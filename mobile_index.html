<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>CorMetrix Scheduler</title>

    <link rel="stylesheet" href="css/login.css" />
    <link rel="stylesheet" href="css/style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <link rel="icon" href="images/scheduler_logo.png" type="image/x-icon" />
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0" />

    <!-- Flatpickr CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />

    <link rel="stylesheet" href="css/output.css" />
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="js/jquery.min.js"></script>
    <script src="./config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/main.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/techCalendar.js"></script>
    <script src="js/techAllocation.js"></script>
    <script src="js/review.js"></script>
    <script src="js/mobileView.js"></script>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>
  </head>

  <body class="h-screen max-h-screen flex flex-col">
    <header class="bg-[#F1F1FB] px-3 md:px-8 flex flex-col md:flex-row">
      <div
        class="flex flex-row items-center md:justify-start justify-between w-full md:w-auto pt-4"
      >
        <div class="flex justify-start items-center space-x-3">
          <!-- <img src="svg/calendar-icon.svg" class="h-[2rem] w-[2rem] md:h-auto md:w-auto"> -->
          <!-- <h1  class=" output text-lg font-semibold text-black hidden md:block"></h1>
                <h1  class=" output text-lg font-semibold text-black block md:hidden"></h1> -->

          <!-- <button onclick="dataSend()">View my schedule</button> -->
        </div>
        <img
          src="/svg/menu.svg"
          class="block md:hidden cursor-pointer w-6 h-6"
          onclick="event.stopPropagation(); opensidemenu();"
        />
      </div>
      <div class="border md:hidden mt-4"></div>
      <div
        class="justify-between md:flex md:flex-row space-x-3 md:items-end mb-3 mr-10 w-full"
      >
        <div class="flex items-start mb-3 sm:ml-2 md:ml-[5rem] mt-4 md:my-0">
          <h1
            class="ml-4 md:ml-0 text-xl md:text-4xl font-bold text-purple-600 capitalize md:truncate"
            id="page-title"
          >
            assignments
          </h1>
        </div>
        <div
          class="flex w-full md:justify-end items-center md:space-x-2 flex-wrap-reverse my-2 gap-2 md:my-0"
        >
          <div
            id="reviewbuttonsDiv"
            class="relative justify-end flex flex-row space-x-3"
          >
            <button
              id="reviewScheduleButton"
              class="hidden flex justify-center items-center space-x-1 font-bold text-white bg-[#8F58DD] hover:bg-purple-400 rounded-[12px] px-5 py-2 w-20 transition-all duration-300"
            >
              <span class="text-sm">Review</span>
            </button>
          </div>
          <div
            id="buttonsDiv"
            class="relative justify-end flex flex-row space-x-3"
          >
            <button
              id="rerunScheduleButton"
              class="flex justify-center items-center space-x-1 font-bold text-white bg-[#8F58DD] rounded-[12px] px-5 py-2 w-36 transition-all duration-300 hover:bg-purple-400"
              title="Click to reschedule"
            >
              <img src="svg/reschedule.svg" class="w-5 h-6" />
              <span class="text-xs sm:text-sm">Reschedule</span>
            </button>
          </div>
          <div
            id="date-range-section"
            class="text-xs h-9 flex items-center border bg-[#393939] rounded-[12px] md:block"
          >
            <!-- <button id="prev-week" class="text-white mx-2 fa">&larr;</button> -->
            <button id="prev-week" class="text-white mx-[2px] md:mx-2 fa">
              ←
            </button>
            <label for="from-date" class="text-white mx-1">Date:</label>
            <input
              id="from-date"
              class="date-picker-input text-sm w-[14rem] md:w-60 px-2 md:px-4 py-2 text-gray-500 text-center flatpickr-input"
              placeholder="Select date"
              type="text"
              readonly="readonly"
            />
            <button id="next-week" class="text-white mx-2 fa">→</button>
          </div>
        </div>
      </div>
    </header>

    <div class="bg-[#F1F1FB] flex-1 md:overflow-hidden flex flex-row">
      <aside
        id="logo-sidebar"
        class="w-60 h-full max-h-full bg-[#F1F1FB] flex-col justify-between transform transition-transform duration-300 ease-in-out hidden md:flex"
      >
        <nav class="flex-1 px-2.5 overflow-y-auto bg-[#F1F1FB] ml-4 mt-4">
          <ul class="space-y-4 font-[400]">
            <li>
              <a
                id="calendarNav"
                class="nav-link flex items-center p-3 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-3 cursor-pointer px-8"
                data-nav="weekly schedule"
              >
                <img src="/svg/calender.svg" alt="Icon" class="w-6 h-6" />
                <span
                  class="text-center text-xl transition-all ease-in-out duration-200"
                >
                  Weekly Schedule
                </span>
              </a>
            </li>
            <li>
              <a
                id="repallocationsNav"
                class="nav-link flex items-center p-3 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                data-nav="assignments"
              >
                <img src="/svg/schedule.svg" alt="Icon" class="w-6 h-6" />
                <span
                  class="text-center text-xl transition-all ease-in-out duration-200"
                >
                  Assignments
                </span>
              </a>
            </li>
            <li>
              <a
                id="repprovidersNav"
                class="nav-link flex items-center p-3 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                data-nav="settings"
              >
                <img src="/svg/settings.svg" alt="Icon" class="w-6 h-6" />
                <span
                  class="text-center text-xl transition-all ease-in-out duration-200"
                >
                  Settings
                </span>
              </a>
            </li>

            <!-- removed the logout btn -->
            <!-- <li>
                        <a id="logoutNav"
                            class="nav-link flex items-center p-3 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                            onclick="logout()">
                            <img src="/svg/logout.svg" alt="Icon" class="w-6 h-6">
                            <span class="text-center text-xl transition-all ease-in-out duration-200">
                                Logout
                            </span>
                        </a>
                    </li> -->
          </ul>
        </nav>
      </aside>
      <main
        id="content-div"
        class="relative flex-1 md:overflow-hidden transition-all ease-in-out duration-300 bg-slate-50 border rounded-[20px] mb-10 ml-4 mr-4 md:mr-10"
      >
        <div
          id="loader"
          class="w-full h-full flex justify-center items-center overflow-hidden absolute top-0 left-0 bg-opacity-60 z-30 bg-[#F1F1FB]"
        >
          <div class="loader"></div>
        </div>
        <div
          id="techCalendar"
          class="hidden justify-start md:justify-center flex-col md:flex-row p-2 w-full max-w-full md:max-w-full h-full max-h-full overflow-scroll"
        ></div>
        <div
          id="repAllocation"
          class="relative hidden flex-col justify-between p-2 w-full max-w-full h-full max-h-full overflow-hidden"
        >
          <div
            id="scheduleChangesManager"
            class="absolute z-10 top-1 flex w-full h-full items-start justify-center hidden"
          >
            <div class="bg-red-500 rounded-sm p-5">
              <div
                class="flex justify-center my-4 text-white text-sm md:text-lg font-semibold"
              >
                You have unsaved changes
              </div>
              <div
                class="h-9 md:h-12 flex flex-row space-x-3 justify-center items-center"
              >
                <button
                  id="discardChangesButton"
                  class="flex justify-center items-center space-x-1 font-bold bg-white rounded-lg px-4 h-full"
                >
                  <span class="text-sm text-gray-700">Discard</span>
                </button>
                <button
                  id="updateScheduleButton"
                  class="flex justify-center items-center space-x-1 bg-white rounded-lg font-bold px-4 h-full"
                >
                  <span class="text-sm text-gray-700">Save</span>
                </button>
              </div>
            </div>
          </div>
          <div
            id="dateButtonsContainer"
            class="flex flex-row justify-evenly md:gap-4 mt-4 md:mt-4"
          ></div>
          <div
            id="schedulesContainer"
            class="flex-1 hidden overflow-hidden"
          ></div>
          <div
            id="scheduleLoader"
            class="flex flex-1 justify-center items-center"
          >
            <div class="loader"></div>
          </div>
          <div
            id="reschedulereview"
            class="hidden custom-scrollbar-vertical grid grid-cols-1 md:grid-cols-2 h-full justify-between justify-center max-h-full max-w-full md:max-w-full overflow-scroll p-2 w-full"
          >
            <div id="rep_changes" class="flex flex-col border"></div>
            <div
              id="agent_changes"
              class="flex flex-col border bg-[#F5F5F5]"
            ></div>
          </div>
        </div>
        <div id="repproviderList" class="w-full h-full hidden relative">
          <div class="main-techs-container md:static md:w-auto">
            <div
              id="techs"
              class="h-full flex flex-col w-[15rem] md:w-96 p-4 border bg-[#F5F5F5] overflow-y-scroll"
            >
              <div class="flex flex-row mb-3">
                <div class="relative w-full">
                  <input
                    id="search-input-skillset"
                    type="text"
                    placeholder="Search"
                    class="w-full p-2 border bg-gray-200 rounded outline-none focus:outline-none bg-white"
                  />
                  <button
                    id="clear-search"
                    class="absolute top-1/2 right-2 transform -translate-y-1/2 text-gray-500"
                  >
                    <img src="/svg/clear.svg" />
                  </button>
                </div>
              </div>
              <button
                class="bg-[#8F58DD] py-2 rounded-lg"
                onclick="openRepForm(event)"
              >
                <p class="text-white font-bold">Add Rep</p>
              </button>
              <hr class="border" />
              <div id="techsNames" class="space-y-2 mt-4"></div>
              <div id="providerNames" class="space-y-2 mt-4"></div>
            </div>
          </div>
          <div
            id="repproviderSkillSet"
            class="flex-1 h-full max-h-full transition-all ease-in-out duration-300 overflow-y-auto"
          >
            <div class="flex h-full w-full justify-center items-center">
              <p class="p-8 font-semibold text-[#8F58DD]">
                Click the Slider on your left to view their Skill Sets...
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>

    <div
      id="timed-popup"
      class="fixed z-50 left-1/2 transform -translate-x-1/2 -translate-y-full opacity-0 text-white p-4 rounded transition-all duration-500 ease-in-out"
    >
      <p id="popup-message" class="font-bold"></p>
    </div>
    <div
      id="confirmation-popup"
      class="fixed z-50 left-1/2 transform -translate-x-1/2 -translate-y-full opacity-0 text-white p-4 rounded transition-all duration-500 ease-in-out flex flex-col space-y-2"
    >
      <p id="confirmation-popup-message" class="font-bold"></p>
      <div class="flex flex-row justify-center space-x-5">
        <button
          id="skill-set-discard-btn"
          class="flex items-center font-bold text-black bg-white rounded-md px-4 py-1.5"
        >
          <span>Discard</span>
        </button>
        <button
          id="skill-set-update-btn"
          class="flex items-center font-bold text-black bg-white rounded-md px-4 py-1.5"
        >
          <span>Update Skill Set</span>
        </button>
      </div>
    </div>
    <div
      id="rerun-confirmation-popup"
      class="fixed z-50 left-1/2 transform -translate-x-1/2 -translate-y-full opacity-0 text-white p-4 rounded transition-all duration-500 ease-in-out flex flex-col space-y-2"
    >
      <p id="rerun-confirmation-popup-message" class="font-bold"></p>
      <div class="flex flex-row justify-center space-x-5">
        <button
          id="cancel-btn"
          class="flex items-center font-bold text-black bg-white rounded-md px-4 py-1.5"
        >
          <span>Discard</span>
        </button>
        <button
          id="rerun-btn"
          class="flex items-center font-bold text-black bg-white rounded-md px-4 py-1.5"
        >
          <span>Reschedule</span>
        </button>
      </div>
    </div>
    <div
      id="loader-with-opacity"
      class="absolute hidden justify-center items-center z-40 inset-0 w-full h-full bg-gray-200 bg-opacity-50 backdrop-blur-sm transition-all duration-100 ease-in-out"
    >
      <div class="loader"></div>
    </div>
    <div id="side-menu" class="hidden md:hidden">
      <div class="fixed inset-0 bg-black bg-opacity-50 z-40"></div>
      <div
        class="fixed right-0 top-0 h-full w-[15rem] bg-[#F1F1FB] shadow-lg z-50 transform transition-transform duration-300 flex flex-col"
      >
        <img
          src="/svg/clear.svg"
          alt="Close"
          class="absolute m-4 transform w-5 h-5 cursor-pointer"
          onclick="closesidebar()"
        />
        <nav class="px-2.5 overflow-y-auto bg-[#F1F1FB] mx-4 my-10">
          <ul class="space-y-2 font-[400]">
            <li>
              <a
                id="calendarNav-sidemenu"
                class="nav-link flex items-center p-2 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-3 cursor-pointer px-8"
                data-nav="weekly schedule"
              >
                <img src="/svg/calender.svg" alt="Icon" class="w-5 h-5" />
                <span
                  class="text-center text-lg transition-all ease-in-out duration-200"
                >
                  Weekly Schedule
                </span>
              </a>
            </li>
            <li>
              <a
                id="repallocationsNav-sidemenu"
                class="nav-link flex items-center p-2 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                data-nav="assignments"
              >
                <img src="/svg/schedule.svg" alt="Icon" class="w-5 h-5" />
                <span
                  class="text-center text-lg transition-all ease-in-out duration-200"
                >
                  Assignments
                </span>
              </a>
            </li>
            <li>
              <a
                id="repprovidersNav-sidemenu"
                class="nav-link flex items-center p-2 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                data-nav="settings"
              >
                <img src="/svg/settings.svg" alt="Icon" class="w-5 h-5" />
                <span
                  class="text-center text-lg transition-all ease-in-out duration-200"
                >
                  Settings
                </span>
              </a>
            </li>
            <!-- <li>
                        <a id="logoutNav"
                            class="nav-link flex items-center p-2 text-black hover:bg-purple-400 handle-state-change rounded-[12px] space-x-5 cursor-pointer px-8"
                            onclick="logout()">
                            <img src="/svg/logout.svg" alt="Icon" class="w-5 h-5">
                            <span class="text-center text-lg transition-all ease-in-out duration-200">
                                Logout
                            </span>
                        </a>
                    </li> -->
          </ul>
        </nav>
      </div>
    </div>
  </body>
</html>
