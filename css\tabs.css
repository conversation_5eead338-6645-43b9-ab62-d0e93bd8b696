#settings-tabs {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

/* Make Implanting Physician tab take full width on mobile */
@media (max-width: 767px) {
  #settings-tabs {
    flex-direction: column;
  }

  #rep-tab,
  #site-tab {
    flex: 1;
  }

  #physician-tab {
    width: 100%;
  }
}

#settings-tabs button {
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Make tabs responsive in mobile view */
@media (max-width: 767px) {
  #settings-tabs button {
    font-size: 12px;
    padding: 8px 4px;
  }

  /* Specifically adjust the Implanting Physician tab */
  #physician-tab-mobile {
    font-size: 11px;
  }
}

/* Ensure desktop tabs have appropriate sizing */
@media (min-width: 768px) {
  #settings-tabs button {
    font-size: 14px;
    min-width: 0; /* Allow buttons to shrink below content size */
    flex-shrink: 1; /* Allow buttons to shrink */
  }

  /* Specifically adjust the Rep and Site tabs */
  #rep-tab,
  #site-tab {
    flex: 0.5; /* Give much less space to Rep and Site tabs */
    min-width: 70px; /* Ensure minimum width */
  }

  /* Specifically adjust the Implanting Physician tab */
  #physician-tab {
    font-size: 14px;
    padding: 8px 10px;
    flex: 2; /* Give much more space to Implanting Physician tab */
    min-width: 150px; /* Ensure minimum width for Implanting Physician text */
    white-space: nowrap; /* Prevent text from wrapping */
    text-align: left; /* Align text to the left instead of center */
  }
}

#settings-tabs button:hover {
  transform: translateY(-2px);
}

#settings-tabs button:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

#settings-tabs button:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.site-name,
.physician-name {
  transition: all 0.2s ease-in-out;
}

.site-name:hover,
.physician-name:hover {
  transform: translateX(5px);
}

#physician-site-filter {
  background-color: white;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

#physician-site-filter:focus {
  border-color: #8f58dd;
  box-shadow: 0 0 0 3px rgba(143, 88, 221, 0.2);
  outline: none;
}
