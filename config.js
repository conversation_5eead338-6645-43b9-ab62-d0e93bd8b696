// config.js
const currentUrl = window.location.href;
const redirect_url = `${window.location.origin}/callback.html`;

const hostname = window.location.hostname;
let stage;

if (hostname.includes("dev") || hostname.includes("localhost")) {
  stage = "DEV";
} else if (hostname.includes("staging")) {
  stage = "STAGING";
} else {
  stage = "PROD";
}

const ENV = {
  DEV: {
    apiUrl: "https://dev-api.cormetrix.com/api/v2",
    scheduler_apiUrl: "https://dev-retriever.cormetrix.com",
    super_set: "https://dev-superset.cormetrix.com",
    epic_clientid: "e7f1bb30-4958-404f-8169-c4d97e17d9d6",
    epic_iss_url: "https://fhir.epic.com/interconnect-fhir-oauth/api/fhir/R4",
    epic_vendor_clientid: "d63b6e89-0642-455c-9f1b-8c360076d365",
    epic_scope_base: "patient",
  },
  STAGING: {
    apiUrl: "https://staging-api.cormetrix.com/api/v2",
    scheduler_apiUrl: "https://staging-retriever.cormetrix.com",
    super_set: "https://staging-superset.cormetrix.com",
    epic_clientid: "e7f1bb30-4958-404f-8169-c4d97e17d9d6",
    epic_iss_url: "https://fhir.epic.com/interconnect-fhir-oauth/api/fhir/R4",
    epic_vendor_clientid: "bc4fd4e0-2f02-41b0-ad23-a4f7a48df28d",
    epic_scope_base: "user",
  },
  PROD: {
    apiUrl: "https://api.cormetrix.com/api/v2",
    scheduler_apiUrl: "https://retriever.cormetrix.com",
    super_set: "https://superset.cormetrix.com",
    epic_iss_urlclientId: "d63b6e89-0642-455c-9f1b-8c360076d365",
    epic_iss_url:
      "https://vendorservices.epic.com/interconnect-amcurprd-oauth/api/FHIR/R4",
    epic_scope_base: "user",
  },
};

const config = {
  redirect_url,
  apiUrl: ENV[stage]["apiUrl"],
  scheduler_apiUrl: ENV[stage]["scheduler_apiUrl"],
  super_set: ENV[stage]["super_set"],
  epic_clientid: ENV[stage]["epic_clientid"],
  epic_iss_url: ENV[stage]["epic_iss_url"],
  epic_scope_base: ENV[stage]["epic_scope_base"],
  epic_vendor_clientid: ENV[stage]["epic_vendor_clientid"],
  epic_redirect_url: `${window.location.origin}/epic-physician-standalone-app/redirect.html`,
  epic_vendor_redirect_url: `${window.location.origin}/epic-vendor-launch/redirect.html`,
};
