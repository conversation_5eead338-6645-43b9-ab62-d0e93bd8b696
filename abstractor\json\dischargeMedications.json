{"discharge_medications": {"medication": {"verified": "", "note": "Medications prescribed at discharge are not required for patients who expired, discharged to 'Other acute care Hospital', 'AMA', or are receiving Hospice Care.", "field_id": "10200", "label": "Medication", "medications": {"fondaparinux": {"field_id": "10205", "label": "Fondaparinux", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "heparin_derivative": {"field_id": "10205", "label": "Heparin Derivative", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "low_molecular_weight_heparin": {"field_id": "10205", "label": "Low Molecular Weight Heparin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "unfractionated_heparin": {"field_id": "10205", "label": "Unfractionated Heparin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "warfarin": {"field_id": "10205", "label": "Warfarin", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "aspirin": {"field_id": "10205", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "if_yes": {"dose": {"field_id": "10207", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "81 - 100 MG"}, {"id": 2, "value": "101 - 324 MG"}, {"id": 3, "value": "325 MG"}], "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What was the category of the medication dose prescribed at discharge? Please select from the following options: Aspirin 81 to 100 mg or Aspirin 101 to 324 mg or Aspirin 325 mg", "description": "\n**Coding Instruction:**\nIndicate the category of the medication dose prescribed.\n**Target Value:**\nThe value on discharge"}}, "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "aspirin_dipyridamole": {"field_id": "10205", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "vorapaxar": {"field_id": "10205", "label": "Vorapaxar", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "apixaban": {"field_id": "10205", "label": "Apixaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "dabigatran": {"field_id": "10205", "label": "Dabigatran", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "edoxaban": {"field_id": "10205", "label": "Edoxaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "rivaroxaban": {"field_id": "10205", "label": "Rivaroxaban", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "cangrelor": {"field_id": "10205", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "clopidogrel": {"field_id": "10205", "label": "Clopidogrel", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "other_p2y12": {"field_id": "10205", "label": "Other P2Y12", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "prasugrel": {"field_id": "10205", "label": "Prasug<PERSON>", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "ticagrelor": {"field_id": "10205", "label": "Ticagrelor", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}, "ticlopidine": {"field_id": "10205", "label": "Ticlopidine", "options": [{"id": 1, "value": "Yes"}, {"id": 2, "value": "No - No Reason"}, {"id": 3, "value": "No - Medical Reason"}, {"id": 4, "value": "No - Pt. Reason"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Was the medication prescribed at discharge, not prescribed, or not prescribed for either a medical or patient reason?", "description": "\n**Coding Instruction:**\nIndicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.\n**Target Value:**\nThe value on discharge"}}, "q_prompt": "What medications were prescribed to the patient upon discharge, excluding cases where the patient expired, was discharged to another acute care hospital, left against medical advice (AMA), or is receiving hospice care? Please provide the assigned identification numbers associated with each prescribed medication.", "description": "\n**Coding Instruction:**\nIndicate the assigned identification number associated with the medications the patient was prescribed upon discharge.\n**Target Value:**\nN/A"}}}