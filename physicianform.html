<style>
  .container {
    --uib-size: 40px;
    --uib-color: black;
    --uib-speed: 2s;
    --uib-bg-opacity: 0;
    height: var(--uib-size);
    width: var(--uib-size);
    transform-origin: center;
    animation: rotate var(--uib-speed) linear infinite;
    will-change: transform;
    overflow: visible;
  }

  .car {
    fill: none;
    stroke: var(--uib-color);
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: stretch calc(var(--uib-speed) * 0.75) ease-in-out infinite;
    will-change: stroke-dasharray, stroke-dashoffset;
    transition: stroke 0.5s ease;
  }

  .track {
    fill: none;
    stroke: var(--uib-color);
    opacity: var(--uib-bg-opacity);
    transition: stroke 0.5s ease;
  }

  /* Ensure loader appears in front of forms */
  #loader,
  .loader-holder,
  #content-loader {
    z-index: 9999 !important;
    position: absolute !important;
    background-color: rgba(255, 255, 255, 0.7) !important;
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes stretch {
    0% {
      stroke-dasharray: 0, 150;
      stroke-dashoffset: 0;
    }

    50% {
      stroke-dasharray: 75, 150;
      stroke-dashoffset: -25;
    }

    100% {
      stroke-dashoffset: -100;
    }
  }
</style>

<script>
  // Import API functions
  import("/js/api.js")
    .then(() => {
      console.log("API functions loaded successfully");
    })
    .catch((error) => {
      console.error("Error loading API functions:", error);
    });

  // Import validation functions
  import("/abstractor/utils.js")
    .then((module) => {
      window.parseCharInput = module.parseCharInput;
      window.parseIntInput = module.parseIntInput;
      window.parseEmailInput = module.parseEmailInput;
      window.utilsLoaded = true;
      console.log("Utils loaded successfully");
    })
    .catch((error) => {
      console.error("Error loading utils:", error);
    });

  // Function to validate physician fields
  window.validatePhysicianField = function (input, type) {
    if (!window.utilsLoaded) {
      console.warn("Utils not loaded yet, validation skipped");
      return;
    }

    try {
      if (type === "char") {
        // Use parseCharInput for character fields with a callback
        window.parseCharInput(input.value, function (validValue) {
          input.value = validValue;
        });
      } else if (type === "int") {
        // Use parseIntInput for integer fields with a callback
        window.parseIntInput(input.value, function (validValue) {
          input.value = validValue;
        });
      } else if (type === "email") {
        // Use parseEmailInput for email fields with a callback
        window.parseEmailInput(input.value, function (validValue) {
          input.value = validValue;
        });
      }
    } catch (error) {
      console.error("Error validating field:", error);
    }
  };

  // Function to load credentials for dropdown
  window.loadCredentialsForDropdown = async function (forceReload = false) {
    console.log("Loading credentials for dropdown...");

    try {
      // Get the credential dropdown
      const credentialDropdown = document.getElementById("credential");
      if (!credentialDropdown) {
        console.error("Credential dropdown not found!");
        return [];
      }

      // Save the currently selected value if any
      const currentValue = credentialDropdown.value;
      console.log(`Current credential value before reload: ${currentValue}`);

      // Check if we've already loaded credentials (to prevent duplicates)
      if (window.credentialsLoaded && !forceReload) {
        console.log("Credentials already loaded, using cached credentials");

        // If we have a current value, make sure it's selected
        if (currentValue && currentValue !== "") {
          credentialDropdown.value = currentValue;
          console.log(`Restored credential value from cache: ${currentValue}`);
        }

        return window.cachedCredentials || [];
      }

      // Remove all options except the first one (the placeholder)
      while (credentialDropdown.options.length > 1) {
        credentialDropdown.remove(1);
      }

      // Use the hardcoded credentials if API call fails
      const hardcodedCredentials = [
        { id: "1", name: "M.D." },
        { id: "2", name: "D.O." },
        { id: "3", name: "M.B.B.S." },
      ];

      let credentials = [];

      try {
        // Fetch credentials from API
        console.log("Fetching credentials from API...");
        const response = await getCredentials();
        console.log("Credentials API response:", response);

        // Extract the credentials array from the response
        // The API returns {result: [...], message: "...", status: "..."}
        credentials = response && response.result ? response.result : [];
        console.log("Credentials extracted:", credentials);

        if (
          !credentials ||
          !Array.isArray(credentials) ||
          credentials.length === 0
        ) {
          console.warn(
            "Invalid or empty credentials data from API, using hardcoded values"
          );
          credentials = hardcodedCredentials;
        }
      } catch (error) {
        console.error("Error fetching credentials from API:", error);
        console.warn("Using hardcoded credentials as fallback");
        credentials = hardcodedCredentials;
      }

      // Add credential options
      credentials.forEach((credential) => {
        // Check if this credential already exists in the dropdown
        let exists = false;
        for (let i = 0; i < credentialDropdown.options.length; i++) {
          if (credentialDropdown.options[i].value === credential.name) {
            exists = true;
            break;
          }
        }

        // Only add if it doesn't already exist
        if (!exists) {
          const option = document.createElement("option");
          option.value = credential.name; // Using name as the value
          option.textContent = credential.name;
          option.setAttribute("data-id", credential.id); // Store the ID as a data attribute
          credentialDropdown.appendChild(option);
          console.log(`Added credential option: ${credential.name}`);
        }
      });

      console.log(`Added ${credentials.length} credentials to dropdown`);

      // Mark credentials as loaded to prevent duplicates
      window.credentialsLoaded = true;
      window.cachedCredentials = credentials;

      // Store credentials in sessionStorage for persistence across page loads
      try {
        sessionStorage.setItem(
          "cachedCredentials",
          JSON.stringify(credentials)
        );
        console.log("Stored credentials in sessionStorage");
      } catch (error) {
        console.error("Error storing credentials in sessionStorage:", error);
      }

      // Check if we're in edit mode and need to select a credential
      const urlParams = new URLSearchParams(window.location.search);
      const isEdit = urlParams.get("edit") === "true";
      const physicianId = urlParams.get("id");

      if (isEdit && physicianId) {
        console.log(`In edit mode for physician ID: ${physicianId}`);
        // The credential will be set when the physician data is loaded
      }

      // If we had a value before, try to restore it
      if (currentValue && currentValue !== "") {
        // Try to find the value in the dropdown
        let found = false;
        for (let i = 0; i < credentialDropdown.options.length; i++) {
          if (credentialDropdown.options[i].value === currentValue) {
            credentialDropdown.selectedIndex = i;
            found = true;
            console.log(
              `Restored previous credential selection: ${currentValue}`
            );
            break;
          }
        }

        // If not found, try case-insensitive match
        if (!found) {
          const lowerValue = currentValue.toLowerCase();
          for (let i = 0; i < credentialDropdown.options.length; i++) {
            if (
              credentialDropdown.options[i].value.toLowerCase() === lowerValue
            ) {
              credentialDropdown.selectedIndex = i;
              found = true;
              console.log(
                `Restored previous credential selection (case-insensitive): ${currentValue}`
              );
              break;
            }
          }
        }

        // If still not found, add it as a custom option
        if (!found) {
          const option = document.createElement("option");
          option.value = currentValue;
          option.textContent = currentValue;
          credentialDropdown.appendChild(option);
          credentialDropdown.value = currentValue;
          console.log(
            `Added and selected custom credential option: ${currentValue}`
          );
        }
      }

      return credentials; // Return the credentials for further processing
    } catch (error) {
      console.error("Error loading credentials:", error);
      return []; // Return empty array on error
    }
  };

  // Initialize credentials from sessionStorage if available
  try {
    const storedCredentials = sessionStorage.getItem("cachedCredentials");
    if (storedCredentials) {
      window.cachedCredentials = JSON.parse(storedCredentials);
      window.credentialsLoaded = true;
      console.log("Initialized credentials from sessionStorage");
    }
  } catch (error) {
    console.error("Error loading credentials from sessionStorage:", error);
  }

  // Function to set credential in edit mode
  window.setPhysicianCredential = async function (credentialValue) {
    if (!credentialValue) return;

    console.log(`Setting physician credential to: ${credentialValue}`);
    const credentialDropdown = document.getElementById("credential");
    if (!credentialDropdown) {
      console.error("Credential dropdown not found!");
      return;
    }

    // Make sure credentials are loaded first
    if (!window.credentialsLoaded || credentialDropdown.options.length <= 1) {
      console.log("Credentials not loaded yet, loading them now...");
      try {
        // Force reload credentials
        await window.loadCredentialsForDropdown(true);
      } catch (error) {
        console.error("Error loading credentials:", error);
      }
    }

    // Store the credential value in sessionStorage for persistence
    try {
      sessionStorage.setItem("lastSelectedCredential", credentialValue);
      console.log(
        `Stored credential value in sessionStorage: ${credentialValue}`
      );
    } catch (error) {
      console.error("Error storing credential in sessionStorage:", error);
    }

    // Try to find the value in the dropdown
    let found = false;
    for (let i = 0; i < credentialDropdown.options.length; i++) {
      if (credentialDropdown.options[i].value === credentialValue) {
        credentialDropdown.selectedIndex = i;
        found = true;
        console.log(`Selected credential option: ${credentialValue}`);
        break;
      }
    }

    // If not found, try case-insensitive match
    if (!found) {
      const lowerValue = credentialValue.toLowerCase();
      for (let i = 0; i < credentialDropdown.options.length; i++) {
        if (credentialDropdown.options[i].value.toLowerCase() === lowerValue) {
          credentialDropdown.selectedIndex = i;
          found = true;
          console.log(
            `Selected credential option (case-insensitive): ${credentialValue}`
          );
          break;
        }
      }
    }

    // If still not found, add it as a custom option
    if (!found) {
      const option = document.createElement("option");
      option.value = credentialValue;
      option.textContent = credentialValue;
      credentialDropdown.appendChild(option);
      credentialDropdown.value = credentialValue;
      console.log(
        `Added and selected custom credential option: ${credentialValue}`
      );
    }

    // Trigger a change event to ensure any listeners are notified
    const event = new Event("change");
    credentialDropdown.dispatchEvent(event);

    return credentialValue;
  };

  // Initialize credential from sessionStorage if available
  try {
    const lastSelectedCredential = sessionStorage.getItem(
      "lastSelectedCredential"
    );
    if (lastSelectedCredential) {
      console.log(
        `Found last selected credential in sessionStorage: ${lastSelectedCredential}`
      );
      // We'll set this after the dropdown is populated
      window.lastSelectedCredential = lastSelectedCredential;
    }
  } catch (error) {
    console.error("Error loading credential from sessionStorage:", error);
  }

  // Function to load experience options for dropdown
  window.loadExperienceForDropdown = async function (forceReload = false) {
    console.log("Loading experience options for dropdown...");

    try {
      // Get the experience dropdown
      const experienceDropdown = document.getElementById("experience");
      if (!experienceDropdown) {
        console.error("Experience dropdown not found!");
        return [];
      }

      // Save the currently selected value if any
      const currentValue = experienceDropdown.value;
      console.log(`Current experience value before reload: ${currentValue}`);

      // Check if we've already loaded experience options (to prevent duplicates)
      if (window.experienceOptionsLoaded && !forceReload) {
        console.log("Experience options already loaded, using cached options");

        // If we have a current value, make sure it's selected
        if (currentValue && currentValue !== "") {
          experienceDropdown.value = currentValue;
          console.log(`Restored experience value from cache: ${currentValue}`);
        }

        return window.cachedExperienceOptions || [];
      }

      // Remove all options except the first one (the placeholder)
      while (experienceDropdown.options.length > 1) {
        experienceDropdown.remove(1);
      }

      // Use hardcoded experience options if API call fails
      const hardcodedExperienceOptions = [
        { id: "1", name: "0-50 cases", value: 25 },
        { id: "2", name: "51-100 cases", value: 75 },
        { id: "3", name: "101-200 cases", value: 150 },
        { id: "4", name: "201+ cases", value: 250 },
      ];

      let experienceOptions = [];

      try {
        // Fetch experience options from API
        console.log("Fetching experience options from API...");
        const response = await fetchProviderExperience();
        console.log("Experience options API response:", response);

        // Extract the experience options array from the response
        experienceOptions = response && response.result ? response.result : [];
        console.log("Experience options extracted:", experienceOptions);

        if (
          !experienceOptions ||
          !Array.isArray(experienceOptions) ||
          experienceOptions.length === 0
        ) {
          console.warn(
            "Invalid or empty experience options data from API, using hardcoded values"
          );
          experienceOptions = hardcodedExperienceOptions;
        }
      } catch (error) {
        console.error("Error fetching experience options from API:", error);
        console.warn("Using hardcoded experience options as fallback");
        experienceOptions = hardcodedExperienceOptions;
      }

      // Add experience options
      experienceOptions.forEach((option) => {
        // Check if this option already exists in the dropdown
        let exists = false;
        for (let i = 0; i < experienceDropdown.options.length; i++) {
          if (
            experienceDropdown.options[i].value.toString() ===
            option.value.toString()
          ) {
            exists = true;
            break;
          }
        }

        // Only add if it doesn't already exist
        if (!exists) {
          const optionElement = document.createElement("option");
          optionElement.value = option.value; // Using value as the value (not ID)
          optionElement.textContent = option.name;
          optionElement.setAttribute("data-id", option.id); // Store the ID as a data attribute
          experienceDropdown.appendChild(optionElement);
          console.log(
            `Added experience option: ${option.name} (ID: ${option.id}, Value: ${option.value})`
          );
        }
      });

      console.log(
        `Added ${experienceOptions.length} experience options to dropdown`
      );

      // Mark experience options as loaded to prevent duplicates
      window.experienceOptionsLoaded = true;
      window.cachedExperienceOptions = experienceOptions;

      // Store experience options in sessionStorage for persistence across page loads
      try {
        sessionStorage.setItem(
          "cachedExperienceOptions",
          JSON.stringify(experienceOptions)
        );
        console.log("Stored experience options in sessionStorage");
      } catch (error) {
        console.error(
          "Error storing experience options in sessionStorage:",
          error
        );
      }

      // Check if we're in edit mode and need to select an experience option
      const urlParams = new URLSearchParams(window.location.search);
      const isEdit = urlParams.get("edit") === "true";
      const physicianId = urlParams.get("id");

      if (isEdit && physicianId) {
        console.log(`In edit mode for physician ID: ${physicianId}`);
        // The experience will be set when the physician data is loaded
      }

      // If we had a value before, try to restore it
      if (currentValue && currentValue !== "") {
        // Try to find the value in the dropdown
        let found = false;
        for (let i = 0; i < experienceDropdown.options.length; i++) {
          if (experienceDropdown.options[i].value === currentValue) {
            experienceDropdown.selectedIndex = i;
            found = true;
            console.log(
              `Restored previous experience selection: ${currentValue}`
            );
            break;
          }
        }

        if (!found) {
          console.log(
            `Could not find experience option with value: ${currentValue}`
          );
        }
      }

      return experienceOptions; // Return the experience options for further processing
    } catch (error) {
      console.error("Error loading experience options:", error);
      return []; // Return empty array on error
    }
  };

  // Function to set experience in edit mode
  window.setPhysicianExperience = async function (experienceValue) {
    if (!experienceValue) return;

    console.log(`Setting physician experience to: ${experienceValue}`);
    const experienceDropdown = document.getElementById("experience");
    if (!experienceDropdown) {
      console.error("Experience dropdown not found!");
      return;
    }

    // Make sure experience options are loaded first
    if (
      !window.experienceOptionsLoaded ||
      experienceDropdown.options.length <= 1
    ) {
      console.log("Experience options not loaded yet, loading them now...");
      try {
        // Force reload experience options
        await window.loadExperienceForDropdown(true);
      } catch (error) {
        console.error("Error loading experience options:", error);
      }
    }

    // Store the experience value in sessionStorage for persistence
    try {
      sessionStorage.setItem("lastSelectedExperience", experienceValue);
      console.log(
        `Stored experience value in sessionStorage: ${experienceValue}`
      );
    } catch (error) {
      console.error("Error storing experience in sessionStorage:", error);
    }

    // Try to find the value in the dropdown
    let found = false;

    // First try direct match (in case the value is already a value)
    for (let i = 0; i < experienceDropdown.options.length; i++) {
      if (
        experienceDropdown.options[i].value.toString() ===
        experienceValue.toString()
      ) {
        experienceDropdown.selectedIndex = i;
        found = true;
        console.log(
          `Selected experience option by direct value match: ${experienceValue}`
        );
        break;
      }
    }

    // If not found and we have cached experience options, try to find by matching the ID or value
    // with the cached experience options
    if (
      !found &&
      window.cachedExperienceOptions &&
      Array.isArray(window.cachedExperienceOptions)
    ) {
      console.log(
        "Trying to match experience by ID or value in cached options"
      );

      // Find the experience option that matches the ID or value
      const matchingOption = window.cachedExperienceOptions.find(
        (option) =>
          option.id === experienceValue ||
          option.value.toString() === experienceValue.toString()
      );

      if (matchingOption) {
        console.log(
          `Found matching experience option: ${JSON.stringify(matchingOption)}`
        );

        // Now find this option in the dropdown by value
        for (let i = 0; i < experienceDropdown.options.length; i++) {
          if (
            experienceDropdown.options[i].value.toString() ===
            matchingOption.value.toString()
          ) {
            experienceDropdown.selectedIndex = i;
            found = true;
            console.log(
              `Selected experience option by cached match: ${matchingOption.name} (Value: ${matchingOption.value})`
            );
            break;
          }
        }
      }
    }

    if (!found) {
      console.log(
        `Could not find experience option with value: ${experienceValue}`
      );
    }

    // Trigger a change event to ensure any listeners are notified
    const event = new Event("change");
    experienceDropdown.dispatchEvent(event);

    return experienceValue;
  };

  // Initialize experience from sessionStorage if available
  try {
    const lastSelectedExperience = sessionStorage.getItem(
      "lastSelectedExperience"
    );
    if (lastSelectedExperience) {
      console.log(
        `Found last selected experience in sessionStorage: ${lastSelectedExperience}`
      );
      // We'll set this after the dropdown is populated
      window.lastSelectedExperience = lastSelectedExperience;
    }
  } catch (error) {
    console.error("Error loading experience from sessionStorage:", error);
  }

  // Function to load sites for dropdown (only in add mode)
  window.loadSitesForDropdown = async function () {
    console.log("Loading sites for dropdown...");

    try {
      // Check if we're in edit mode
      const urlParams = new URLSearchParams(window.location.search);
      const isEdit = urlParams.get("edit") === "true";

      // Get the site dropdown container
      const siteContainer = document.getElementById("site-dropdown-container");
      if (!siteContainer) {
        console.error("Site dropdown container not found!");
        return;
      }

      // If in edit mode, completely remove the site dropdown container
      if (isEdit) {
        console.log("In edit mode, removing site dropdown completely");
        siteContainer.remove(); // Remove the entire container from the DOM
        return;
      }

      // In add mode, show the site dropdown
      siteContainer.style.display = "block";

      // Get the site dropdown
      const siteDropdown = document.getElementById("physician_site_id");
      if (!siteDropdown) {
        console.error("Site dropdown not found!");
        return;
      }

      // Clear existing options except the first one
      while (siteDropdown.options.length > 1) {
        siteDropdown.remove(1);
      }

      // Make sure getAllSites is available
      if (typeof window.getAllSites !== "function") {
        console.error("getAllSites function not found!");

        // Try to get it from the parent window if it's not available directly
        if (typeof window.parent.getAllSites === "function") {
          console.log("Using getAllSites from parent window");
          window.getAllSites = window.parent.getAllSites;
        } else {
          console.error(
            "getAllSites function not found in parent window either!"
          );

          // Add some sample sites as a fallback
          const sampleSites = [
            { id: "sample1", name: "Sample Hospital 1" },
            { id: "sample2", name: "Sample Hospital 2" },
            { id: "sample3", name: "Sample Hospital 3" },
          ];

          sampleSites.forEach((site) => {
            const option = document.createElement("option");
            option.value = site.id;
            option.textContent = site.name;
            siteDropdown.appendChild(option);
          });

          console.log("Added sample sites as fallback");
          return;
        }
      }

      // Fetch sites from API
      console.log("Calling getAllSites function...");
      const sites = await window.getAllSites();
      console.log("Sites loaded:", sites);

      if (!sites || !Array.isArray(sites)) {
        console.error("Invalid sites data:", sites);
        return;
      }

      // Sort sites alphabetically
      const sortedSites = [...sites].sort((a, b) =>
        a.name.localeCompare(b.name)
      );

      // Add site options
      sortedSites.forEach((site) => {
        const option = document.createElement("option");
        option.value = site.id;
        option.textContent = site.name;
        siteDropdown.appendChild(option);
      });

      console.log(`Added ${sortedSites.length} sites to dropdown`);

      // Check if we have a site_id in the URL
      const siteIdFromUrl = urlParams.get("site_id");
      if (siteIdFromUrl) {
        siteDropdown.value = siteIdFromUrl;
        console.log(`Set site dropdown to value from URL: ${siteIdFromUrl}`);
      }
    } catch (error) {
      console.error("Error loading sites:", error);
    }
  };

  // Call the functions when the DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    window.loadCredentialsForDropdown();
    window.loadExperienceForDropdown();
  });

  // Also call them now in case DOMContentLoaded already fired
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    setTimeout(function () {
      window.loadCredentialsForDropdown();
      window.loadExperienceForDropdown();
    }, 100);
  }
</script>

<div class="flex w-full h-full mb-9 mt-3 justify-center">
  <!-- Using main loader instead of form loader -->

  <div
    id="PhysicianSuccessModal"
    class="fixed w-full h-full bg-black bg-opacity-50 items-center justify-center"
    style="display: none; top: 0; left: 0; z-index: 99999"
  >
    <div class="bg-white rounded-lg shadow-lg max-w-sm w-full z-10">
      <div
        id="header"
        class="flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg"
      >
        <span id="headertitle"></span>
      </div>
      <div class="p-4 text-center">
        <p class="text-gray-600" id="desc"></p>
      </div>
      <div class="p-4 flex justify-center">
        <button
          id="physicianModalOkBtn"
          class="px-4 py-2 bg-green-500 text-white font-medium rounded"
        >
          OK
        </button>
      </div>
    </div>
  </div>

  <form
    id="physicianFormElement"
    class="space-y-4 w-[80%] mb-4"
    onsubmit="console.log('Form submit event triggered'); return createNewPhysician(event);"
  >
    <div>
      <h1 class="font-bold text-2xl mb-2">Implanting Physician Info</h1>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Site dropdown (only shown in add mode) -->
        <div id="site-dropdown-container" style="display: none">
          <label
            for="physician_site_id"
            class="block text-sm font-medium text-gray-700"
            >Hospital Site</label
          >
          <select
            id="physician_site_id"
            name="site_id"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select a site</option>
            <!-- Sites will be populated from API -->
          </select>
        </div>

        <div>
          <label
            for="experience"
            class="block text-sm font-medium text-gray-700"
            >LAAO Caseload Experience <span class="text-red-500">*</span></label
          >
          <select
            id="experience"
            name="experience"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            required
          >
            <option value="">Select experience level</option>
            <!-- Experience options will be populated from API -->
          </select>
        </div>

        <div>
          <label
            for="first_name"
            class="block text-sm font-medium text-gray-700"
            >First Name <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="first_name"
            name="first_name"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            required
            oninput="validatePhysicianField(this, 'char')"
          />
        </div>

        <div>
          <label for="last_name" class="block text-sm font-medium text-gray-700"
            >Last Name <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="last_name"
            name="last_name"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            required
            oninput="validatePhysicianField(this, 'char')"
          />
        </div>

        <div>
          <label
            for="middle_name"
            class="block text-sm font-medium text-gray-700"
            >Middle Name</label
          >
          <input
            type="text"
            id="middle_name"
            name="middle_name"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'char')"
          />
        </div>

        <div>
          <label
            for="credential"
            class="block text-sm font-medium text-gray-700"
            >Credential</label
          >
          <select
            id="credential"
            name="credential"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            required
          >
            <option value="">Select a credential</option>
            <!-- Credentials will be populated from API or hardcoded values -->
          </select>
        </div>

        <div>
          <label
            for="npi_number"
            class="block text-sm font-medium text-gray-700"
            >NPI Number</label
          >
          <input
            type="text"
            id="npi_number"
            name="npi_number"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'int')"
          />
        </div>

        <div>
          <label for="email_id" class="block text-sm font-medium text-gray-700"
            >Email</label
          >
          <input
            type="email"
            id="email_id"
            name="email_id"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'email')"
          />
        </div>

        <div>
          <label
            for="phone_number"
            class="block text-sm font-medium text-gray-700"
            >Phone Number</label
          >
          <input
            type="text"
            id="phone_number"
            name="phone_number"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'int')"
          />
        </div>

        <div>
          <label for="address" class="block text-sm font-medium text-gray-700"
            >Address</label
          >
          <input
            type="text"
            id="address"
            name="address"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
          />
        </div>

        <div>
          <label for="city" class="block text-sm font-medium text-gray-700"
            >City</label
          >
          <input
            type="text"
            id="city"
            name="city"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'char')"
          />
        </div>

        <div>
          <label for="state" class="block text-sm font-medium text-gray-700"
            >State</label
          >
          <input
            type="text"
            id="state"
            name="state"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'char')"
          />
        </div>

        <div>
          <label for="zip_code" class="block text-sm font-medium text-gray-700"
            >Zip Code</label
          >
          <input
            type="text"
            id="zip_code"
            name="zip_code"
            class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            oninput="validatePhysicianField(this, 'int')"
          />
        </div>
      </div>
    </div>
    <button
      type="submit"
      style="margin-bottom: 1rem"
      class="w-full mb-4 bg-[#8F58DD] text-white py-2 px-4 rounded-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
    >
      Submit
    </button>
  </form>
</div>

<script src="js/settings.js"></script>

<script>
  // Function to handle site dropdown visibility
  function handleSiteDropdown() {
    // Check if we're in edit mode
    const urlParams = new URLSearchParams(window.location.search);
    const isEdit = urlParams.get("edit") === "true";
    const siteContainer = document.getElementById("site-dropdown-container");

    if (!siteContainer) {
      console.log("Site dropdown container not found");
      return;
    }

    if (isEdit) {
      // In edit mode, remove the site dropdown completely
      console.log("In edit mode, removing site dropdown");
      siteContainer.remove();
    } else {
      // In add mode, show the site dropdown and load sites
      console.log("In add mode, showing site dropdown");
      siteContainer.style.display = "block";

      // Make the site dropdown required in add mode
      const siteDropdown = document.getElementById("physician_site_id");
      if (siteDropdown) {
        siteDropdown.setAttribute("required", "required");
      }

      // Load sites if the function exists
      if (typeof window.getAllSites === "function") {
        console.log("Loading sites for dropdown");
        window
          .getAllSites()
          .then((sites) => {
            if (!sites || !Array.isArray(sites)) {
              console.error("Invalid sites data:", sites);
              return;
            }

            // Sort sites alphabetically
            const sortedSites = [...sites].sort((a, b) =>
              a.name.localeCompare(b.name)
            );

            // Add site options
            const siteDropdown = document.getElementById("physician_site_id");
            if (siteDropdown) {
              // Clear existing options except the first one
              while (siteDropdown.options.length > 1) {
                siteDropdown.remove(1);
              }

              sortedSites.forEach((site) => {
                const option = document.createElement("option");
                option.value = site.id;
                option.textContent = site.name;
                siteDropdown.appendChild(option);
              });

              console.log(`Added ${sortedSites.length} sites to dropdown`);

              // Check if we have a site_id in the URL
              const siteIdFromUrl = urlParams.get("site_id");
              if (siteIdFromUrl) {
                siteDropdown.value = siteIdFromUrl;
                console.log(
                  `Set site dropdown to value from URL: ${siteIdFromUrl}`
                );
              }
            }
          })
          .catch((error) => {
            console.error("Error loading sites:", error);
          });
      } else {
        console.error("getAllSites function not found");
      }
    }
  }

  // Function to load credentials and experience options
  async function loadCredentials() {
    // Load credentials
    if (window.loadCredentialsForDropdown) {
      console.log("Loading credentials for dropdown");
      try {
        const credentials = await window.loadCredentialsForDropdown();
        console.log(`Loaded ${credentials.length} credentials`);

        // If we have a last selected credential, set it now
        if (window.lastSelectedCredential) {
          console.log(
            `Setting last selected credential: ${window.lastSelectedCredential}`
          );
          await window.setPhysicianCredential(window.lastSelectedCredential);
          delete window.lastSelectedCredential; // Clear it after use
        } else {
          // Check if we're in edit mode and have a credential in the URL
          const urlParams = new URLSearchParams(window.location.search);
          const isEdit = urlParams.get("edit") === "true";
          const physicianId = urlParams.get("id");

          if (isEdit && physicianId) {
            console.log(
              `In edit mode for physician ID: ${physicianId}, credential will be set when data is loaded`
            );
          }
        }
      } catch (error) {
        console.error("Error loading credentials:", error);
      }
    }

    // Load experience options
    if (window.loadExperienceForDropdown) {
      console.log("Loading experience options for dropdown");
      try {
        const experienceOptions = await window.loadExperienceForDropdown();
        console.log(`Loaded ${experienceOptions.length} experience options`);

        // If we have a last selected experience, set it now
        if (window.lastSelectedExperience) {
          console.log(
            `Setting last selected experience: ${window.lastSelectedExperience}`
          );
          await window.setPhysicianExperience(window.lastSelectedExperience);
          delete window.lastSelectedExperience; // Clear it after use
        } else {
          // Check if we're in edit mode and have an experience in the URL
          const urlParams = new URLSearchParams(window.location.search);
          const isEdit = urlParams.get("edit") === "true";
          const physicianId = urlParams.get("id");

          if (isEdit && physicianId) {
            console.log(
              `In edit mode for physician ID: ${physicianId}, experience will be set when data is loaded`
            );
          }
        }
      } catch (error) {
        console.error("Error loading experience options:", error);
      }
    }
  }

  // Call these functions when the DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    handleSiteDropdown();
    loadCredentials();
  });

  // Also call them now in case DOMContentLoaded already fired
  setTimeout(function () {
    handleSiteDropdown();
    loadCredentials();
  }, 500);

  // Add a final check after a longer delay to ensure everything is loaded
  setTimeout(function () {
    // Check if the credential dropdown has options
    const credentialDropdown = document.getElementById("credential");
    if (credentialDropdown && credentialDropdown.options.length <= 1) {
      console.log(
        "Final check: Credential dropdown has no options, reloading..."
      );
      loadCredentials();
    }

    // Check if the experience dropdown has options
    const experienceDropdown = document.getElementById("experience");
    if (experienceDropdown && experienceDropdown.options.length <= 1) {
      console.log(
        "Final check: Experience dropdown has no options, reloading..."
      );
      if (window.loadExperienceForDropdown) {
        window.loadExperienceForDropdown(true);
      }
    }

    // Check if we're in edit mode and need to restore values
    const urlParams = new URLSearchParams(window.location.search);
    const isEdit = urlParams.get("edit") === "true";
    if (isEdit) {
      // Check for credential
      const lastSelectedCredential = sessionStorage.getItem(
        "lastSelectedCredential"
      );
      if (
        lastSelectedCredential &&
        credentialDropdown &&
        credentialDropdown.value !== lastSelectedCredential
      ) {
        console.log(
          `Final check: Restoring credential from sessionStorage: ${lastSelectedCredential}`
        );
        window.setPhysicianCredential(lastSelectedCredential);
      }

      // Check for experience
      const lastSelectedExperience = sessionStorage.getItem(
        "lastSelectedExperience"
      );
      if (
        lastSelectedExperience &&
        experienceDropdown &&
        experienceDropdown.value !== lastSelectedExperience
      ) {
        console.log(
          `Final check: Restoring experience from sessionStorage: ${lastSelectedExperience}`
        );
        window.setPhysicianExperience(lastSelectedExperience);
      }
    }
  }, 2000);
</script>
