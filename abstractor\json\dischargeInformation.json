{"discharge": {"surgery": {"field_id": "14835", "label": "Surgery", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "percutaneous_coronary_interventions": {"field_id": "14836", "label": "Percutaneous Coronary Interventions", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "discharge_date": {"field_id": "10100", "label": "Discharge Date", "input_type": "string", "value": ""}, "discharge_status": {"field_id": "10105", "label": "Discharge Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": ""}, "if_alive_discharge_location": {"field_id": "10110", "label": "If Alive, Discharge Location", "options": [{"id": "1", "value": "Home"}, {"id": "2", "value": "Skilled Nursing facility"}, {"id": "3", "value": "Extended care/TCU/rehab"}, {"id": "4", "value": "Other"}, {"id": "5", "value": "Other acute care hospital"}, {"id": "6", "value": "Left against medical advice (AMA)"}], "input_type": "select", "value": "id"}, "if_alive_hospice_care": {"field_id": "10115", "label": "If Alive, Hospice Care", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "if_deceased_death_during_the_procedure": {"field_id": "10120", "label": "If Deceased, Death During the Procedure", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "if_deceased_cause_of_death": {"field_id": "10125", "label": "If Deceased, Cause of Death", "options": [{"id": "1", "value": "Acute myocardial infarction"}, {"id": "2", "value": "Pulmonary"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Sudden cardiac death"}, {"id": "5", "value": "Renal"}, {"id": "6", "value": "Non-cardiovascular procedure or surgery"}, {"id": "7", "value": "Heart failure"}, {"id": "8", "value": "Gastrointestinal"}, {"id": "9", "value": "<PERSON>rauma"}, {"id": "10", "value": "Stroke"}, {"id": "11", "value": "Hepatobiliary"}, {"id": "12", "value": "Suicide"}, {"id": "13", "value": "Cardiovascular procedure"}, {"id": "14", "value": "Pancreatic"}, {"id": "15", "value": "Neurological"}, {"id": "16", "value": "Cardiovascular hemorrhage"}, {"id": "17", "value": "Infection"}, {"id": "18", "value": "Malignancy"}, {"id": "19", "value": "Other cardiovascular reason"}, {"id": "20", "value": "Inflammatory/Immunologic"}, {"id": "21", "value": "Other non-cardiovascular reason"}], "input_type": "select", "value": "id"}}}