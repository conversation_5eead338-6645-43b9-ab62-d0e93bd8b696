<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="../config.js"></script>
</head>

<body class="bg-gray-100" id="login">

    <!-- Login Form -->
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white p-8 rounded-lg shadow-md w-96 font-serif">
            <h2 class="text-2xl font-semibold mb-6 text-center">Login</h2>
            <form>
                <button type="submit"
                    class="w-full py-2 bg-gray-600 text-white rounded-md focus:outline-none focus:ring focus:ring-blue-300">
                    Login with Keycloak
                </button>
            </form>
        </div>
    </div>

    <!-- Loader Overlay -->
    <div id="loader-overlay"
        class="fixed inset-0 z-50 bg-gray-900 hidden bg-opacity-50 flex items-center justify-center">
        <div class="w-12 h-12 border-4 rounded-full animate-spin"></div>
    </div>
    <script src="../js/api.js"></script>
    <script>

        // Show loader
        function showLoader() {
            document.getElementById('loader-overlay').classList.remove('hidden');
        }

        // Hide loader
        function hideLoader() {
            document.getElementById('loader-overlay').classList.add('hidden');
        }

        document.getElementById("login").addEventListener('submit', async (event) => {
            event.preventDefault();
            const redirect_url = `${window.location.origin}/dashboard/callback.html`;
            const url = `${config.apiUrl}/auth?redirect_url=${redirect_url}`
            window.location.href = url;
            // await fetchGuestTokenFromBackend();
        });
    </script>
</body>

</html>