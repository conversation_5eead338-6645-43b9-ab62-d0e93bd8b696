async function tasks() {
  // Get the loader element and show while fetching
  const loader = document.getElementById("loader");
  if (loader) {
    loader.classList.remove("hidden");
  }

  // Remove 'selected' class from all navigation links
  document.querySelectorAll(".task-nav-link").forEach((link) => {
    link.classList.remove("selected");
  });

  // Add 'selected' class to the clicked link
  document.getElementById("tasksNav").classList.add("selected");

  const backButton = document.createElement("button");
  backButton.classList.add(
    "mt-4",
    "ml-4",
    "flex",
    "items-center",
    "gap-2",
    "text-[#8143d9]",
    "p-2",
    "rounded",
    "px-2.5",
    "py-1",
    "mb-5"
  );
  backButton.style.position = "absolute";
  backButton.style.top = "10px";
  backButton.style.left = "10px";
  backButton.style.zIndex = "9999";

  // Create SVG icon container
  const svgIcon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  svgIcon.setAttribute("xmlns", "http://www.w3.org/2000/svg");
  svgIcon.setAttribute("fill", "none");
  svgIcon.setAttribute("viewBox", "0 0 24 24");
  svgIcon.setAttribute("stroke-width", "2");
  svgIcon.classList.add("w-6", "h-6");

  // Create the circle inside the SVG
  const circle = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "circle"
  );
  circle.setAttribute("cx", "12");
  circle.setAttribute("cy", "12");
  circle.setAttribute("r", "10");
  circle.setAttribute("stroke", "#8143d9");
  circle.setAttribute("stroke-width", "2");
  circle.setAttribute("fill", "white");

  // Create the arrow inside the SVG
  const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path.setAttribute("stroke", "#8143d9");
  path.setAttribute("stroke-linecap", "round");
  path.setAttribute("stroke-linejoin", "round");
  path.setAttribute("d", "M14 16l-4-4m0 0l4-4");

  // Append the SVG elements together
  svgIcon.appendChild(circle);
  svgIcon.appendChild(path);

  // Create the "Back" text
  const backText = document.createElement("span");
  backText.textContent = "Back";

  // Append icon and text to the button
  backButton.appendChild(svgIcon);
  backButton.appendChild(backText);
  try {
    const calendarContainer = document.getElementById("abstractorCalendar");
    const taskContainer = document.getElementById("abstractorTasks");
    const taskListContainer = document.getElementById("taskList");

    if (!taskListContainer) {
      console.error('Error: "taskList" container not found.');
      return;
    }

    // Ensure the task container is positioned relative for absolute positioning of the back button
    if (taskContainer) {
      taskContainer.style.position = "relative";

      // Set the onclick event to refresh the page
      backButton.onclick = () => location.reload();

      // Prepend the back button so it stays on top left
      taskContainer.prepend(backButton);
    }

    // Hide the calendar and show the task container
    if (calendarContainer) {
      calendarContainer.style.display = "none";
    }
    if (taskContainer) {
      taskContainer.style.display = "flex";
    }

    // Clear previous task items and apply styling classes
    taskListContainer.innerHTML = "";
    taskListContainer.classList.add("flex", "flex-wrap", "gap-4");

    // Fetch task data using fetchAbstractorTasks()
    const tasksData = await fetchAbstractorTasks();
    console.log("Fetched Tasks:", tasksData);

    if (!Array.isArray(tasksData) && tasksData.length > 0) {
      tasksData.forEach((task) => {
        const taskItem = document.createElement("div");
        taskItem.classList.add(
          "p-5",
          "bg-white",
          "rounded-lg",
          "shadow-sm",
          "mb-4",
          "h-[280px]",
          "w-[300px]"
        );

        // Header section: task type and status badge
        const header = document.createElement("div");
        header.classList.add("flex", "justify-between", "items-center", "mb-4");

        const taskType = document.createElement("span");
        taskType.textContent = task.task_type?.sub_type?.name || "Unknown Task";
        taskType.classList.add("text-purple-600", "font-semibold", "text-lg");

        const statusBadge = document.createElement("div");
        statusBadge.classList.add(
          "p-1",
          "rounded-md",
          "text-center",
          "border-2"
        );

        const status = task.task_status?.toLowerCase();
        if (status === "pending") {
          statusBadge.textContent = "Pending";
          statusBadge.classList.add(
            "bg-red-100",
            "border-red-400",
            "text-red-600"
          );
        } else if (status === "in_progress") {
          statusBadge.textContent = "In Progress";
          statusBadge.classList.add(
            "bg-yellow-100",
            "border-yellow-400",
            "text-yellow-600"
          );
        } else if (status === "completed") {
          statusBadge.textContent = "Completed";
          statusBadge.classList.add(
            "bg-green-100",
            "border-green-400",
            "text-green-600"
          );
        } else {
          statusBadge.textContent = "Unknown";
        }

        header.appendChild(taskType);
        header.appendChild(statusBadge);

        // Separator line between header and details
        const separator = document.createElement("hr");
        separator.classList.add("my-4", "border-gray-300");

        // Details section for task
        const details = document.createElement("div");
        details.classList.add("flex", "flex-col", "gap-2");

        // Utility: Create a detail row with a label and value
        const createDetailRow = (label, value) => {
          const row = document.createElement("div");
          row.classList.add("flex", "justify-between");

          const labelEl = document.createElement("span");
          labelEl.textContent = label;
          labelEl.classList.add("font-semibold");

          const valueEl = document.createElement("span");
          valueEl.textContent = value || "N/A";

          row.appendChild(labelEl);
          row.appendChild(valueEl);
          return row;
        };

        details.appendChild(createDetailRow("Name:", task.patient?.name));
        details.appendChild(createDetailRow("Age:", task.patient?.age));
        details.appendChild(
          createDetailRow("Assigned By:", task.assigner?.name)
        );
        details.appendChild(
          createDetailRow("Assigned Date:", task.assigned_date)
        );
        details.appendChild(createDetailRow("Due Date:", task.due_date));

        // Assemble the task card
        taskItem.appendChild(header);
        taskItem.appendChild(separator);
        taskItem.appendChild(details);

        // Append the task card to the task list container
        taskListContainer.appendChild(taskItem);
      });
    } else {
      taskListContainer.textContent = "No tasks available to display.";
      taskListContainer.classList.add(
        "mt-4",
        "text-[#8143d9]",
        "flex",
        "justify-center",
        "items-center"
      );
    }
  } catch (error) {
    console.error("Error fetching tasks:", error);
    // Optionally, display an error message to the user
    const taskListContainer = document.getElementById("taskList");
    if (taskListContainer) {
      taskListContainer.textContent = "An error occurred while fetching tasks.";
    }
  } finally {
    // Hide the loader once tasks have been fetched or an error occurs
    const loader = document.getElementById("loader");
    if (loader) {
      loader.classList.add("hidden");
    }
  }
}
