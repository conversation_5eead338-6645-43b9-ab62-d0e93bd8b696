const renderCalendar = () => {
  const calendarContainer = document.getElementById("abstractorCalendar");
  const patientListContainer = document.getElementById("patientList");
  const weekSlider = document.createElement("div");
  const loader = document.getElementById("loader");
  const currentDate = new Date().toISOString().split("T")[0];
  let selectedDate = currentDate;
  let selectedWeekStart = new Date(currentDate);
  let selectedMonthStart = new Date(currentDate);
  let patientsData = [];

  // Loading indicator functions
  function showLoadingIndicator() {
    // Check if loading indicator already exists
    if (document.getElementById('loading-indicator')) {
      return;
    }

    // Create loading indicator container
    const loadingContainer = document.createElement('div');
    loadingContainer.id = 'loading-indicator';
    loadingContainer.style.position = 'fixed';
    loadingContainer.style.top = '0';
    loadingContainer.style.left = '0';
    loadingContainer.style.width = '100%';
    loadingContainer.style.height = '100%';
    loadingContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    loadingContainer.style.display = 'flex';
    loadingContainer.style.justifyContent = 'center';
    loadingContainer.style.alignItems = 'center';
    loadingContainer.style.zIndex = '9999';

    // Create spinner
    const spinner = document.createElement('div');
    spinner.style.border = '5px solid #f3f3f3';
    spinner.style.borderTop = '5px solid #8143d9';
    spinner.style.borderRadius = '50%';
    spinner.style.width = '50px';
    spinner.style.height = '50px';
    spinner.style.animation = 'spin 1s linear infinite';

    // Add animation keyframes
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    // Assemble and add to document
    loadingContainer.appendChild(spinner);
    document.body.appendChild(loadingContainer);
  }

  // Remove the loading indicator
  function hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.style.opacity = '0';
      loadingIndicator.style.transition = 'opacity 0.5s';
      setTimeout(() => {
        loadingIndicator.remove();
      }, 500);
    }
  }

  const handlePress = (patient) => {
    window.location.href = `/abstractor/patientDetails.html?case_id=${patient.case_id}`;
  };

  // Utility Functions
  const generateWeek = (startDate, weekStart = 0) => {
    const startOfWeek = new Date(startDate);
    const dayOfWeek = startOfWeek.getDay();

    // Adjust start date based on weekStart (0 = Sunday, 1 = Monday, etc.)
    startOfWeek.setDate(
      startOfWeek.getDate() - ((dayOfWeek + 7 - weekStart) % 7)
    );

    return Array.from({ length: 7 }, (_, i) => {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      return day;
    });
  };

  const formatDateIdGenerator = (date) => {
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
      .toISOString()
      .split("T")[0];
  };

  const fetchAppointmentsForDate = (patients, date) =>
    patients.filter((record) => record.procedure_date === date);

  const renderAppointments = (appointments, fromMonthlyView = false) => {
    patientListContainer.innerHTML = ""; // Clear previous content

    const headerContainer = document.createElement("div");
    headerContainer.className = "flex items-center mb-4"; // Flexbox for alignment

    // Back Button
    if (fromMonthlyView) {
      const backButton = document.createElement("button");
      backButton.classList.add(
        "flex",
        "items-center",
        "gap-2",
        "text-[#8143d9]",
        "p-2",
        "rounded",
        "px-2.5",
        "py-1",
        "mb-5"
      );

      // Create SVG icon
      const svgIcon = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "svg"
      );
      svgIcon.setAttribute("xmlns", "http://www.w3.org/2000/svg");
      svgIcon.setAttribute("fill", "none");
      svgIcon.setAttribute("viewBox", "0 0 24 24");
      svgIcon.setAttribute("stroke-width", "2");
      svgIcon.classList.add("w-6", "h-6");

      // Create circle in SVG
      const circle = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "circle"
      );
      circle.setAttribute("cx", "12");
      circle.setAttribute("cy", "12");
      circle.setAttribute("r", "10");
      circle.setAttribute("stroke", "#8143d9");
      circle.setAttribute("stroke-width", "2");
      circle.setAttribute("fill", "white");

      // Create arrow path in SVG
      const path = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "path"
      );
      path.setAttribute("stroke", "#8143d9");
      path.setAttribute("stroke-linecap", "round");
      path.setAttribute("stroke-linejoin", "round");
      path.setAttribute("d", "M14 16l-4-4m0 0l4-4");

      // Append SVG elements
      svgIcon.appendChild(circle);
      svgIcon.appendChild(path);

      // Create "Back" text
      const backText = document.createElement("span");
      backText.textContent = "Back";

      // Append icon and text to button
      backButton.appendChild(svgIcon);
      backButton.appendChild(backText);

      backButton.addEventListener("click", () => {
        // Set view mode to monthly in localStorage to maintain monthly view state
        localStorage.setItem("view_mode", "monthly");
        renderMonthlyView(patientsData);
      });

      headerContainer.appendChild(backButton);
    }

    // Search Bar Container
    const searchBarContainer = document.createElement("div");
    searchBarContainer.classList.add("relative", "w-96"); // Push search bar to the right

    // Search Input
    const searchBar = document.createElement("input");
    searchBar.type = "search";
    searchBar.placeholder = "Search patient by name";
    searchBar.classList.add(
      "search-bar",
      "w-full",
      "px-4",
      "py-2",
      "border",
      "rounded",
      "pl-9"
    ); // Adjust padding-left

    // Search Icon
    const searchIcon = document.createElement("i");
    searchIcon.classList.add(
      "fas",
      "fa-search",
      "absolute",
      "top-2",
      "left-3",
      "text-[#8143d9]",
      "text-lg"
    );

    searchBarContainer.appendChild(searchIcon);
    searchBarContainer.appendChild(searchBar);

    headerContainer.appendChild(searchBarContainer);
    patientListContainer.appendChild(headerContainer);

    // If there are no appointments, show "None"
    if (appointments.length === 0) {
      const noneMessage = document.createElement("p");
      noneMessage.className = "text-[#8143d9] mt-12 text-center";
      noneMessage.textContent = "None";
      patientListContainer.appendChild(noneMessage);
      return;
    }

    // Create a container for patient cards
    const cardsContainer = document.createElement("div");
    cardsContainer.className = "flex flex-wrap gap-4";

    // Render patient cards
    appointments.forEach((patient) => {
      // Create card container
      const card = document.createElement("div");
      card.classList.add(
        "mb-4",
        "w-[350px]",
        "card-container",
        "shadow-md",
        "rounded-lg",
        "p-3",
        "hover:cursor-pointer"
      );

      // Create inner card wrapper
      const cardInner = document.createElement("div");
      cardInner.classList.add("card");

      // Create patient info container
      const patientInfo = document.createElement("div");
      patientInfo.classList.add("flex", "items-center", "justify-between");

      // Create patient details wrapper
      const detailsWrapper = document.createElement("div");
      detailsWrapper.classList.add("flex", "flex-col", "p-1");

      // Create name and DOB row
      const nameDobRow = document.createElement("div");
      nameDobRow.classList.add("flex", "p-1");

      const nameContainer = document.createElement("div");
      nameContainer.classList.add("flex", "items-center");

      // Create patient name
      const name = document.createElement("span");
      name.classList.add("text-[#8143d9]", "font-semibold", "text-lg", "mr-2");
      name.textContent = patient.patient_name;

      // Create separator line
      const separator = document.createElement("div");
      separator.classList.add(
        "h-6",
        "border-l-2",
        "rounded-full",
        "border-[#8143d9]",
        "mx-2"
      );

      // Create DOB
      const dob = document.createElement("span");
      dob.classList.add("font-bold", "text-lg", "text-[#8143d9]");
      dob.textContent = patient.dob;

      // Append elements to name row
      nameContainer.appendChild(name);
      nameContainer.appendChild(separator);
      nameContainer.appendChild(dob);
      nameDobRow.appendChild(nameContainer);

      // Procedure Date
      const procedureRow = document.createElement("div");
      procedureRow.classList.add("flex", "gap-2", "p-1");

      const procedureLabel = document.createElement("span");
      procedureLabel.classList.add("text-md", "text-black", "font-semibold");
      procedureLabel.textContent = "Procedure Date:";

      const procedureDate = document.createElement("span");
      procedureDate.classList.add("text-black");
      procedureDate.textContent = patient.procedure_date;

      procedureRow.appendChild(procedureLabel);
      procedureRow.appendChild(procedureDate);

      // Referring Provider
      const providerRow = document.createElement("div");
      providerRow.classList.add("flex", "gap-2", "p-1");

      const providerLabel = document.createElement("span");
      providerLabel.classList.add("text-md", "text-black", "font-semibold");
      providerLabel.textContent = "Referring Provider:";

      const providerName = document.createElement("span");
      providerName.classList.add("text-black");
      providerName.textContent = patient.referring_provider
        ? patient.referring_provider
        : "N/A";

      providerRow.appendChild(providerLabel);
      providerRow.appendChild(providerName);

      // Append all elements together
      detailsWrapper.appendChild(nameDobRow);
      detailsWrapper.appendChild(procedureRow);
      detailsWrapper.appendChild(providerRow);
      patientInfo.appendChild(detailsWrapper);
      cardInner.appendChild(patientInfo);
      card.appendChild(cardInner);

      // Add click event
      card.addEventListener("click", () => handlePress(patient));

      // Append to container
      cardsContainer.appendChild(card);
      patientListContainer.appendChild(cardsContainer);
    });

    // Add search functionality
    searchBar.addEventListener("input", (e) => {
      const query = e.target.value.toLowerCase();
      let hasMatches = false;

      document.querySelectorAll(".card-container").forEach((card) => {
        const patientName = card
          .querySelector("span.text-lg")
          .textContent.toLowerCase();
        if (patientName.includes(query)) {
          card.style.display = "block";
          hasMatches = true;
        } else {
          card.style.display = "none";
        }
      });

      // Remove any existing "None" message
      let noneMessage = document.getElementById("no-results");
      if (noneMessage) {
        noneMessage.remove();
      }

      // Show "None" if no matches found
      if (!hasMatches) {
        noneMessage = document.createElement("p");
        noneMessage.id = "no-results";
        noneMessage.className = "text-[#8143d9] mt-12 text-center";
        noneMessage.textContent = "None";
        patientListContainer.appendChild(noneMessage);
      }
    });
  };

  const renderWeek = (week, patients) => {
    weekSlider.innerHTML = "";
    week.forEach((date) => {
      const dayCard = document.createElement("div");
      const formattedDate = formatDateIdGenerator(date);
      dayCard.className =
        "border border-[#8143d9] day-card flex flex-col items-center w-16 p-2 rounded-lg cursor-pointer";
      dayCard.id = formattedDate;

      // Check if the current date is the selected date
      const isSelected = formattedDate === selectedDate;

      if (isSelected) {
        dayCard.classList.add("selected-day", "bg-[#8143d9]", "text-white");
      }

      // Check if there are any patients for this date
      const hasPatients = patients.some(
        (p) => p.procedure_date === formattedDate
      );

      // Create date container
      const dateText = document.createElement("p");
      dateText.textContent = date.getDate();

      // Create weekday container
      const weekdayText = document.createElement("p");
      weekdayText.textContent = date.toLocaleDateString("en-US", {
        weekday: "short",
      });

      // Create indicator for patients (if applicable)
      let patientIndicator = null;
      if (hasPatients) {
        patientIndicator = document.createElement("div");
        patientIndicator.classList.add(
          "w-2",
          "h-2",
          "rounded-full",
          "mt-1",
          isSelected ? "bg-white" : "bg-[#8143d9]"
        );
      }

      // Append elements to the day card
      dayCard.appendChild(dateText);
      dayCard.appendChild(weekdayText);
      if (patientIndicator) {
        dayCard.appendChild(patientIndicator);
      }

      // Add click event listener
      dayCard.addEventListener("click", () => {
        selectedDate = formattedDate;
        renderAppointments(fetchAppointmentsForDate(patients, selectedDate));

        // Update all day cards to reflect the new selection
        document.querySelectorAll(".day-card").forEach(card => {
          if (card.id === formattedDate) {
            card.classList.add("selected-day", "bg-[#8143d9]", "text-white");
            // Update indicator color if it exists
            const indicator = card.querySelector(".rounded-full");
            if (indicator) indicator.classList.add("bg-white");
          } else {
            card.classList.remove("selected-day", "bg-[#8143d9]", "text-white");
            // Update indicator color if it exists
            const indicator = card.querySelector(".rounded-full");
            if (indicator) indicator.classList.remove("bg-white");
          }
        });
      });

      weekSlider.appendChild(dayCard);
    });

    // Render appointments for the selected date after week is displayed
    renderAppointments(fetchAppointmentsForDate(patients, selectedDate));
  };

  // Render Monthly View
  const renderMonthlyView = (patients) => {
    patientListContainer.innerHTML = ""; // Clear previous content

    const monthContainer = document.createElement("div");
    monthContainer.className = "grid grid-cols-7 gap-2 p-4"; // 7 columns for the days

    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    // Create a row for day names
    const dayHeaderContainer = document.createElement("div");
    dayHeaderContainer.className =
      "grid grid-cols-7 gap-2 p-2 font-bold text-center";

    dayNames.forEach((day) => {
      const dayHeader = document.createElement("div");
      dayHeader.className = "text-[#8143d9]";
      dayHeader.textContent = day;
      dayHeaderContainer.appendChild(dayHeader);
    });

    // Append the day header before the dates
    patientListContainer.appendChild(dayHeaderContainer);

    const today = new Date(selectedMonthStart);
    today.setDate(1);
    const firstDay = today.getDay(); // Get the weekday of the first day
    const daysInMonth = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      0
    ).getDate(); // Get number of days in the month

    // Add empty cells before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      const emptyCell = document.createElement("div");
      emptyCell.className = "p-2"; // Ensure consistent spacing
      monthContainer.appendChild(emptyCell);
    }

    // Generate date cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dayCell = document.createElement("div");
      dayCell.className =
        "border h-20 p-2 text-center rounded-md cursor-pointer hover:bg-gray-200";

      const dateObj = new Date(today.getFullYear(), today.getMonth(), day);
      const dateStr = formatDateIdGenerator(dateObj); // Format date to YYYY-MM-DD for API

      const patientCount = patients.filter(
        (p) => p.procedure_date === dateStr
      ).length;

      // Create elements for day and patient count
      const dayText = document.createElement("p");
      dayText.classList.add("font-bold");
      dayText.textContent = day;

      const patientCountText = document.createElement("p");
      patientCountText.classList.add("text-sm");
      patientCountText.textContent = `${
        patientCount > 0 ? patientCount : "0"
      } Patients`;

      // Append elements to the day cell
      dayCell.appendChild(dayText);
      dayCell.appendChild(patientCountText);

      // Highlight the day if there are patients
      if (patientCount > 0) {
        dayCell.classList.add(
          "bg-[#8143d9]",
          "text-white",
          "hover:bg-gray-500"
        );
      } else {
        dayCell.style.border = "none";
      }

      // Handle day click
      dayCell.addEventListener("click", () => {
        selectedDate = dateStr;
        patientListContainer.innerHTML = ""; // Clear patient list
        renderAppointments(
          fetchAppointmentsForDate(patients, selectedDate),
          true
        ); // Show appointments for selected day
      });

      monthContainer.appendChild(dayCell);
    }

    patientListContainer.appendChild(monthContainer); // Add the grid to the container
  };

  const navigateWeek = (offset, patients) => {
    showLoadingIndicator();
    selectedWeekStart.setDate(selectedWeekStart.getDate() + offset * 7);
    const newWeek = generateWeek(selectedWeekStart);

    // Update the week display immediately for better UX
    updateWeekMonthYearDisplay();

    // API call when navigating weeks
    updatePatientsList(
      formatDateIdGenerator(newWeek[0]),
      formatDateIdGenerator(newWeek[6])
    );
  };
  const navigateMonth = async (offset, patients) => {
    showLoadingIndicator();
    selectedMonthStart.setDate(1); // Ensure we're at the first day of the month
    selectedMonthStart.setMonth(selectedMonthStart.getMonth() + offset);

    // Update displayed month immediately for better UX
    updateMonthYearDisplay();

    // Fetch new patient data for the new month
    const startDate = formatDateIdGenerator(selectedMonthStart);
    const endDate = formatDateIdGenerator(
      new Date(
        selectedMonthStart.getFullYear(),
        selectedMonthStart.getMonth() + 1,
        0
      )
    );

    // updatePatientsList will handle the loading indicator
    await updatePatientsList(startDate, endDate);
  };

  const updatePatientsList = async (startDate, endDate) => {
    // Show loading indicator while fetching data
    showLoadingIndicator();

    try {
      // Attempt to fetch patient data and store it globally
      patientsData = await fetchAbstractorData(startDate, endDate);
    } catch (error) {
      console.error("Error fetching patient data: ", error);
      // Fallback to an empty dataset if fetch fails
      patientsData = [];
    } finally {
      // Hide loader and render calendar components based on current view
      const currentView = localStorage.getItem("view_mode") || "weekly";

      if (currentView === "weekly") {
        const week = generateWeek(selectedWeekStart);
        renderWeek(week, patientsData);
        renderAppointments(fetchAppointmentsForDate(patientsData, selectedDate));
      } else {
        renderMonthlyView(patientsData);
      }

      // Hide the loading indicator after rendering is complete
      hideLoadingIndicator();
    }
  };

  // Swipe gesture support
  let touchStartX = 0;
  let touchEndX = 0;

  weekSlider.addEventListener("touchstart", (e) => {
    touchStartX = e.changedTouches[0].screenX;
  });

  weekSlider.addEventListener("touchend", (e) => {
    touchEndX = e.changedTouches[0].screenX;
    const swipeDistance = touchEndX - touchStartX;
    const currentView = localStorage.getItem("view_mode") || "weekly";

    if (swipeDistance > 50) {
      if (currentView === "weekly") {
        navigateWeek(-1, patientsData);
      } else {
        navigateMonth(-1, patientsData);
      }
    } else if (swipeDistance < -50) {
      if (currentView === "weekly") {
        navigateWeek(1, patientsData);
      } else {
        navigateMonth(1, patientsData);
      }
    }
  });

  const getWeekRange = (date) => {
    const day = new Date(date);
    const start = new Date(day);
    start.setDate(day.getDate() - day.getDay());
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    return [formatDateIdGenerator(start), formatDateIdGenerator(end)];
  };

  // Add Month and Year Display at the top
  const monthYearDisplay = document.createElement("h2");
  monthYearDisplay.className = "text-xl font-bold text-center";

  const updateWeekMonthYearDisplay = () => {
    const monthYear = new Date(selectedWeekStart);
    monthYearDisplay.textContent = monthYear.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });
  };

  const updateMonthYearDisplay = () => {
    const monthYear = new Date(selectedMonthStart);
    monthYearDisplay.textContent = monthYear.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });
  };

  // Dropdown for selecting Weekly or Monthly View
  document.addEventListener("DOMContentLoaded", () => {
    const storedView = localStorage.getItem("view_mode");
    if (storedView && viewSelector) {
      viewSelector.value = storedView;
      // Trigger change event to use the stored view
      viewSelector.dispatchEvent(new Event("change"));
    }
  });

  // Create the select element
  const viewSelector = document.createElement("select");
  viewSelector.className = "w-32 border p-2 rounded";

  // Create the "Weekly" option
  const weeklyOption = document.createElement("option");
  weeklyOption.value = "weekly";
  weeklyOption.textContent = "Weekly";

  // Create the "Monthly" option
  const monthlyOption = document.createElement("option");
  monthlyOption.value = "monthly";
  monthlyOption.textContent = "Monthly";

  // Append options to the select element
  viewSelector.appendChild(weeklyOption);
  viewSelector.appendChild(monthlyOption);
  // Event listener for viewSelector to handle view changes and store the selected value in local storage
  viewSelector.addEventListener("change", async () => {
    const selectedView = viewSelector.value;
    // Save the selected view mode to local storage
    localStorage.setItem("view_mode", selectedView);

    // Show loading indicator
    showLoadingIndicator();

    if (selectedView === "weekly") {
      // Weekly view
      weekSlider.classList.remove("hidden"); // Show week slider
      patientListContainer.innerHTML = ""; // Clear Monthly View

      // Update the week display
      updateWeekMonthYearDisplay();

      // Get the current week range and fetch data
      const [start, end] = getWeekRange(selectedDate);

      try {
        patientsData = await fetchAbstractorData(start, end);
      } catch (error) {
        console.error("Error fetching patient data: ", error);
        patientsData = [];
      } finally {
        renderWeek(generateWeek(selectedWeekStart, 0), patientsData);
        renderAppointments(fetchAppointmentsForDate(patientsData, selectedDate));
        // Hide loading indicator
        hideLoadingIndicator();
      }
    } else {
      // Monthly view
      weekSlider.classList.add("hidden"); // Hide week slider
      patientListContainer.innerHTML = ""; // Clear Weekly View

      // Ensure selectedMonthStart is updated based on selectedWeekStart
      selectedMonthStart = new Date(selectedWeekStart);
      selectedMonthStart.setDate(1); // Reset to first day of the month

      updateMonthYearDisplay(); // Update displayed month name

      // Fetch new patient data for the correct month
      const startDate = formatDateIdGenerator(
        new Date(
          selectedMonthStart.getFullYear(),
          selectedMonthStart.getMonth(),
          1
        )
      );
      const endDate = formatDateIdGenerator(
        new Date(
          selectedMonthStart.getFullYear(),
          selectedMonthStart.getMonth() + 1,
          0
        )
      );

      try {
        patientsData = await fetchAbstractorData(startDate, endDate);
      } catch (error) {
        console.error("Error fetching patient data: ", error);
        patientsData = [];
      } finally {
        renderMonthlyView(patientsData); // Render Monthly View with new data
        // Hide loading indicator
        hideLoadingIndicator();
      }
    }
  });

  // Modify Navigation Buttons Placement
  const navigationContainer = document.createElement("div");
  navigationContainer.className =
    "flex justify-center items-center gap-20 mb-8";

  const prevWeekButton = document.createElement("button");
  prevWeekButton.textContent = "<";
  prevWeekButton.className = "btn-nav";
  prevWeekButton.addEventListener("click", () => {
    const storedView = localStorage.getItem("view_mode") || "weekly";
    if (storedView === "weekly") {
      navigateWeek(-1, patientsData);
      updateWeekMonthYearDisplay();
    } else {
      navigateMonth(-1, patientsData);
    }
  });

  const nextWeekButton = document.createElement("button");
  nextWeekButton.textContent = ">";
  nextWeekButton.className = "btn-nav";
  nextWeekButton.addEventListener("click", () => {
    const storedView = localStorage.getItem("view_mode") || "weekly";
    if (storedView === "weekly") {
      navigateWeek(1, patientsData);
      updateWeekMonthYearDisplay();
    } else {
      navigateMonth(1, patientsData);
    }
  });

  navigationContainer.appendChild(prevWeekButton);
  navigationContainer.appendChild(monthYearDisplay);
  navigationContainer.appendChild(nextWeekButton);

  const initializeCalendar = () => {
    // Show loading indicator during initialization
    showLoadingIndicator();

    const currentView = localStorage.getItem("view_mode") || "weekly";

    if (currentView === "weekly") {
      const [start, end] = getWeekRange(selectedDate);
      updatePatientsList(start, end);
      updateWeekMonthYearDisplay();
    } else {
      // For monthly view, get the full month range
      const startDate = formatDateIdGenerator(
        new Date(selectedMonthStart.getFullYear(), selectedMonthStart.getMonth(), 1)
      );
      const endDate = formatDateIdGenerator(
        new Date(selectedMonthStart.getFullYear(), selectedMonthStart.getMonth() + 1, 0)
      );
      updatePatientsList(startDate, endDate);
      updateMonthYearDisplay();
    }
  };

  weekSlider.className = "flex justify-around mb-4";
  calendarContainer.prepend(viewSelector, navigationContainer, weekSlider);
  initializeCalendar();
};

document.addEventListener("DOMContentLoaded", renderCalendar);
