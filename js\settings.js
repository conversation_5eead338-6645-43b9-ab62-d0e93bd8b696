// Global variables for tracking selected items
let lastSelectedSiteId = null; // Last selected site ID
let lastSelectedPhysicianId = null; // Last selected physician ID

// Function to set the current site and physician IDs
function setCurrentSelections(siteId, physicianId) {
  if (siteId) {
    lastSelectedSiteId = siteId;
    sessionStorage.setItem("lastSelectedSiteId", siteId);
    sessionStorage.setItem("currentSiteId", siteId);
    console.log(`Set current site ID: ${siteId}`);
  }

  if (physicianId) {
    lastSelectedPhysicianId = physicianId;
    sessionStorage.setItem("lastSelectedPhysicianId", physicianId);
    console.log(`Set current physician ID: ${physicianId}`);
  }
}

// Safety function to ensure loaders are always hidden
function ensureLoadersHidden() {
  console.log("Safety function: Ensuring all loaders are hidden");
  // Hide the content loader
  toggleContentLoader(false);

  // Hide the loader in the loader div if it exists
  const loader = document.getElementById("loader");
  if (loader) {
    toggleLoader(loader, false);
  }

  // Hide the form loader if it exists
  const formLoader = document.getElementById("formLoader");
  if (formLoader) {
    formLoader.classList.add("hidden");
  }
}

function showPopup(
  techName = "",
  message = "",
  bgColorClass = "bg-yellow-600"
) {
  const popup = document.getElementById("timed-popup");
  const popupMessage = document.getElementById("popup-message");

  popupMessage.textContent = `${techName} ${message}`;
  popup.className = popup.className.replace(/bg-\w+-\d+/g, "").trim();
  toggleClass(
    popup,
    [bgColorClass, "translate-y-6", "opacity-100"],
    ["-translate-y-full", "opacity-0"]
  );

  setTimeout(() => {
    toggleClass(
      popup,
      ["-translate-y-full", "opacity-0"],
      ["translate-y-6", "opacity-100"]
    );
  }, 4000);
}

let updateSkillSetBtn = "";
async function editTech() {
  document
    .querySelectorAll(".tech-name")
    .forEach((card) => card.classList.add("pointer-events-none"));

  const repproviderSkillSet = document.getElementById("repproviderSkillSet");
  repproviderSkillSet.innerHTML = `
        <div class="relative flex flex-col p-4 w-full h-full">
            <h2 class="text-lg md:text-2xl min-h-14 max-h-14 font-medium md:font-semibold flex items-center">
                <span class="p-2 text-[#8F58DD]">
                    ${
                      activerepprovider.rep_name
                        ? "Clinical Specialist:"
                        : "Provider:"
                    }
                </span>
                ${
                  activerepprovider.rep_name ||
                  activerepprovider.physician_name ||
                  ""
                }
            </h2>
            <hr class="border"></hr>
            <div class="flex flex-1 justify-center items-center loader-holder">
                <div class="loader"></div>
            </div>
        </div>
    `;

  if (activerole == "Clinical Specialist") {
    activeTechInitialSkillSet = await fetchRepSkillSet(activerepprovider.id);
    sectionNames = ["procedures", "unavailability_date", "additional_values"];
  } else {
    activeTechInitialSkillSet = await fetchProviderSkillSet(
      activerepprovider.id
    );
    sectionNames = ["procedures", "sites"];
  }

  document
    .querySelectorAll(".tech-name")
    .forEach((card) => card.classList.remove("pointer-events-none"));

  const contentDiv = repproviderSkillSet.querySelector("div");
  contentDiv.querySelector(".loader-holder").remove();

  updateSkillSetBtn = document.createElement("button");
  updateSkillSetBtn.id = "updateSkillSetDiv";
  updateSkillSetBtn.className = `flex justify-center items-center space-x-1 font-medium md:font-bold text-white bg-[#8F58DD] rounded-[12px] px-5 py-2.5 w-30 transition-all duration-300 hover:bg-purple-400`;

  const buttonText = document.createElement("span");
  buttonText.textContent = "Save Skill";
  buttonText.className = "text-sm";

  updateSkillSetBtn.appendChild(buttonText);

  document.getElementById("buttonsDiv").appendChild(updateSkillSetBtn);

  maincontainer = document.createElement("div");
  gridDiv = document.createElement("div");
  gridDiv.className = "text-xs flex flex-wrap gap-6 m-4";
  maincontainer.appendChild(gridDiv);
  contentDiv.appendChild(maincontainer);

  const flexContainer = document.createElement("div");
  flexContainer.className = "flex justify-between mt-6 gap-5";

  const splitContentDiv = document.createElement("div");
  splitContentDiv.className = "flex justify-between mt-6 gap-5";

  sectionNames.forEach((sectionName) => {
    const sectionDiv = createPreferenceSection(
      sectionName,
      activeTechInitialSkillSet[sectionName]
    );

    document.body.appendChild(sectionDiv);
    gridDiv.appendChild(sectionDiv);
  });

  ["procedures", "unavailability", "sites"].forEach((type) => {
    initializeSortable(
      `.sortable-container-${type}`,
      `.${type}-cards`,
      `.${type}-cards`
    );
  });

  function initializeSortable(selector, handleClass, draggableClass) {
    document.querySelectorAll(selector).forEach((column) => {
      new Sortable(column, {
        animation: 150,
        handle: handleClass,
        draggable: draggableClass,
      });
    });
  }

  updateSkillSetBtn.addEventListener("click", async (event) => {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
    updateButtonAction(activerole);
  });
}

function resetDesignToDefault() {
  document.getElementById("repproviderSkillSet").innerHTML =
    skillSetPlaceholderText;
  const updateSkillSetDiv = document.getElementById("updateSkillSetDiv");
  updateSkillSetDiv.remove();

  document.querySelectorAll(".tech-name").forEach((card) => {
    toggleClass(card, ["bg-[#F5F5F5]"], ["bg-gray-300"]);
  });
}

async function updateButtonAction(activerole) {
  toggleLoader(document.getElementById("loader"), true);
  isSkillSetChangedFunction();

  if (isSkillSetChanged) {
    try {
      if (activerole == "Clinical Specialist") {
        result = await updaterepskillset(activeTechCurrentSkillSet);
      } else {
        result = await updateproviderskillset(activeTechCurrentSkillSet);
      }
      toggleLoader(document.getElementById("loader"), false);

      if (result.status === "success") {
        showPopup(
          `${
            activerepprovider.rep_name || activerepprovider.physician_name
          }'s `,
          "Skill-Set is Updated...",
          "bg-[#8F58DD]"
        );
        setVariablesToDefault();
        resetDesignToDefault();
      } else {
        showPopup(
          "",
          `An error occurred while updating the ${
            activerepprovider.rep_name || activerepprovider.physician_name
          }'s Skill-Set...`,
          "bg-red-600"
        );
      }
    } catch (error) {
      showPopup(
        "",
        "An error occurred during the update process...",
        "bg-red-600"
      );
    }
  } else {
    toggleLoader(document.getElementById("loader"), false);
    noChangesFlow();
  }
  return true;
}

function noChangesFlow() {
  showPopup("", "No SkillSet Changes Made...", "bg-[#8F58DD]");
  setVariablesToDefault();
  resetDesignToDefault();
}

let additionalListDiv = "";
let overlay = "";
let tempDiv = "";
let additionalOptions = [];
// function createPreferenceSection(title, items) {
//   if (title == "procedures") {
//     title = "Procedure";
//   } else if (title == "sites") {
//     title = "Sites";
//   } else if (title == "additional_values") {
//     title = "Location";
//   } else {
//     title = "Unavailability";
//   }
//   const sectionDiv = document.createElement("div");
//   sectionDiv.className = "bg-white rounded-[12px] shadow-md w-full md:w-[40%]";

//   let sectionHeader = "";
//   let rowsHTML = "";
//   if (title == "Procedure") {
//     sectionHeader = `
//     <div class="relative bg-[#8F58DD] p-4 rounded-t-[12px]">
//         <div class="grid grid-cols-2 gap-4 items-center">
//             <div class="font-medium md:font-bold text-white">Procedure</div>
//             <div class="font-medium md:font-bold text-white">Experience</div>
//         </div>
//         <button
//             class="absolute top-2 p-2 right-2 hover:scale-110 transition-all duration-300 ease-in-out"
//             title="Add more ${title}">
//             <img src="/svg/add.svg" alt="Add Icon" class="w-4 h-4 sm:w-8 sm:h-8"  />
//         </button>
//     </div>
// `;

//     rowsHTML = items
//       .map(
//         (procedure) => `
//         <div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${title.toLowerCase()}-cards">
//             <div class="text-gray-900 text-sm md:text-lg font-medium" procedure-type-id="${
//               procedure.procedure_type_id
//             }">
//                 ${procedure.name}
//             </div>
//             <div class="text-gray-600 flex items-center justify-between">
//             <select
//                 class="bg-white text-gray-600 rounded-md text-[12px] md:text-lg"
//                 value="${procedure.experience_name}"
//             >
//                 <option id=0 value="NA" ${
//                   procedure.experience_name === "NA" ? "selected" : ""
//                 }>
//                     NA
//                 </option>
//                 ${JSON.parse(localStorage.getItem("all-experience"))
//                   .map(
//                     (experience) => `
//                             <option id="${experience.id}" value="${
//                       experience.name
//                     }" ${
//                       experience.name === procedure.experience_name
//                         ? "selected"
//                         : ""
//                     }>
//                                 ${experience.name}
//                             </option>
//                         `
//                   )
//                   .join("")}
//             </select>
//                 <button
//                     class="text-black rounded-md hover:text-red-500 ml-3"
//                     title="Remove item ${title}">
//                     <img src="/svg/delete.svg" alt="Delete Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//                 </button>
//             </div>
//         </div>`
//       )
//       .join("");
//   } else if (title == "Sites") {
//     sectionHeader = `
//         <div class="relative bg-[#8F58DD] p-4  rounded-t-[12px]">
//             <div class="grid grid-cols-1 gap-4 items-center">
//                 <div class="font-medium md:font-bold text-white">Site Name</div>
//             </div>
//             <button
//                 class="absolute top-2 p-2 right-2 hover:scale-110 transition-all duration-300 ease-in-out"
//                 title="Add more ${title}">
//                 <img src="/svg/add.svg" alt="Add Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//             </button>
//         </div>
//     `;

//     rowsHTML = items
//       .map(
//         (site) => `
//         <div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${title.toLowerCase()}-cards">
//             <div class="text-sm md:text-lg text-gray-900 font-medium" site-id="${
//               site.id
//             }">
//                 ${site.name}
//             </div>
//             <div class="text-gray-600 flex justify-end">
//                 <button
//                     class="text-black rounded-md hover:text-red-500 ml-3"
//                     title="Remove item ${title}">
//                     <img src="/svg/delete.svg" alt="Delete Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//                 </button>
//             </div>
//         </div>`
//       )
//       .join("");
//   } else if (title == "Location") {
//     sectionHeader = `
//         <div class="relative bg-[#8F58DD] p-4 rounded-t-[12px]">
//             <div class="grid grid-cols-2 gap-4 items-center">
//                 <div class="font-medium md:font-bold text-white">${title}</div>
//                 <div class="font-medium md:font-bold text-white">Zip Code</div>
//             </div>
//         </div>
//         `;
//     rowsHTML = Object.entries(items)
//       .map(([key, value]) => {
//         if (key === "home_zip_code") {
//           return `
//             <div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${key.toLowerCase()}-cards">
//                 <div class="text-gray-900 font-medium" location-id="${key}">
//                     Home
//                 </div>
//                 <div class="text-gray-600 flex items-center justify-between">
//                     <input
//                         type="text"
//                         value="${value}"
//                         class="border border-gray-300 rounded px-2 py-1 w-full"
//                         placeholder="Enter Zip Code"
//                         data-zip-code="${value}"
//                     />
//                 </div>
//             </div>`;
//         }
//         return "";
//       })
//       .join("");
//   } else {
//     sectionHeader = `
//             <div class="relative bg-[#8F58DD] p-4 rounded-t-[12px]">
//                 <div class="grid grid-cols-2 gap-4 items-center">
//                     <div class="font-medium md:font-bold text-white">${title}</div>
//                     <div class="font-medium md:font-bold text-white">Reason</div>
//                 </div>
//                 <div id="calendar-icon" class="absolute bg-white top-2 rounded-[20px] p-2 right-2 cursor-pointer">
//                     <img src="/svg/calendar-icon.svg" alt="Calendar Icon" class="w-5 h-5" />
//                 </div>
//                 <input type="date" id="date-picker" class="hidden absolute top-2 right-2 border border-gray-300 rounded" />
//             </div>
//             `;

//     items = Object.entries(items);
//     rowsHTML = items
//       .map(
//         ([date, reason]) => `
//         <div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${title.toLowerCase()}-cards">
//             <div class="text-gray-900 font-medium" unavailability-date-id="${date}">
//                 ${date}
//             </div>
//             <div class="text-gray-600 flex items-center justify-between">
//                 <input
//                     type="text"
//                     value="${reason}"
//                     class="border border-gray-300 rounded px-2 py-1 w-full text-sm md:text-lg"
//                     placeholder="Enter Reason"
//                     data-date="${date}"
//                     />
//                 <button
//                     class="text-black rounded-md hover:text-red-500 ml-2"
//                     title="Remove item ${title}">
//                     <img src="/svg/delete.svg" alt="Delete Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//                 </button>
//             </div>
//         </div>`
//       )
//       .join("");
//   }
//   sectionDiv.innerHTML = `
//     ${sectionHeader}
//     <div class="flex flex-col divide-y divide-gray-300">
//         ${rowsHTML}
//     </div>
// `;

//   if (title != "Unavailability") {
//     const addButtons = sectionDiv.querySelectorAll(
//       `button[title="Add more ${title}"]`
//     );
//     addButtons.forEach((button) => {
//       button.addEventListener("click", () => {
//         sidediv(title);
//         tempDiv.classList.remove("hidden");
//         openOverlay();
//         updateOptions();
//       });
//     });
//   } else {
//     const calendarIcon = sectionDiv.querySelector("#calendar-icon");
//     const datePicker = sectionDiv.querySelector("#date-picker");

//     if (calendarIcon) {
//       calendarIcon.addEventListener("click", () => {
//         datePicker.classList.remove("hidden");
//         calendarIcon.classList.add("hidden");
//         datePicker.focus();
//       });
//       datePicker.addEventListener("focusout", () => {
//         calendarIcon.classList.remove("hidden");
//         datePicker.classList.add("hidden");
//       });

//       datePicker.addEventListener("fullscreenchange", () => {
//         datePicker.classList.add("hidden");
//         calendarIcon.classList.remove("hidden");
//       });
//     }

//     if (datePicker) {
//       datePicker.addEventListener("change", (event) => {
//         const selectedDate = event.target.value;

//         const data_picker_div = `<div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${title.toLowerCase()}-cards">
//             <div class="text-gray-900 font-medium" unavailability-date-id="${selectedDate}">
//                 ${selectedDate}
//             </div>
//             <div class="text-gray-600 flex items-center justify-between">
//                 <input
//                     type="text"
//                     value=""
//                     class="border border-gray-300 rounded px-2 py-1 w-full"
//                     placeholder="Enter Reason"
//                     data-date="${selectedDate}"
//                     />
//                 <button
//                     class="text-black rounded-md hover:text-red-500 ml-3"
//                     title="Remove item ${title}">
//                     <img src="/svg/delete.svg" alt="Delete Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//                 </button>
//             </div>
//         </div>`;

//         listDiv.innerHTML += data_picker_div;

//         const newRemoveButton = sectionDiv.querySelectorAll(
//           `button[title="Remove item ${title}"]`
//         );
//         newRemoveButton.forEach((button) => {
//           button.addEventListener("click", () => {
//             button.closest("div.grid").remove();
//           });
//         });
//         datePicker.classList.add("hidden");
//         calendarIcon.classList.remove("hidden");
//       });
//     }
//   }
//   const removeButtons = sectionDiv.querySelectorAll(
//     `button[title="Remove item ${title}"]`
//   );
//   removeButtons.forEach((button) => {
//     button.addEventListener("click", (event) => {
//       button.closest("div.grid").remove();
//       updateOptions(event);
//     });
//   });

//   const listDiv = sectionDiv.querySelector(`.flex-col`);

//   function sidediv(title) {
//     overlay = document.createElement("div");
//     overlay.className = "fixed inset-0 bg-black bg-opacity-50 z-40 hidden";

//     const parentDiv = document.getElementById("content-div");
//     parentDiv.appendChild(overlay);

//     tempDiv = document.createElement("div");
//     tempDiv.className =
//       "w-[14rem] md:w-80 fixed flex flex-col right-0 top-0 h-full bg-[#F1F1FB] shadow-lg z-50 transform transition-transform duration-300 hidden";
//     parentDiv.appendChild(tempDiv);

//     tempDiv.innerHTML = `<div class="bg-white m-4 rounded-[12px]">
//         <div class="text-sm md:text-lg flex font-medium md:font-bold ml-4 py-4 relative text-black">
//             Additions for ${title}
//             <button class="close-btn text-black hover:scale-110 transform transition-transform absolute top-4 right-4">
//                 <i class="fa-solid fa-xmark"></i>
//             </button>
//         </div>
//         <hr class="border">
//         <div class="overflow-y-auto flex flex-wrap gap-4 p-4"></div></div>
//     `;

//     requestAnimationFrame(() => {
//       parentDiv.classList.remove("translate-x-full");
//       parentDiv.classList.add("translate-x-0");
//     });

//     additionalListDiv = tempDiv.querySelector("div.flex-wrap");

//     switch (title) {
//       case "Experience":
//         additionalOptions = JSON.parse(localStorage.getItem("all-experience"));
//         break;
//       case "Procedure":
//         additionalOptions = JSON.parse(localStorage.getItem("all-procedure"));
//         break;
//       case "Sites":
//         additionalOptions = JSON.parse(localStorage.getItem("all-sites"));
//       default:
//         break;
//     }

//     const closeButton = tempDiv.querySelector(".close-btn");
//     closeButton.addEventListener("click", closeOverlay);

//     overlay.addEventListener("click", closeOverlay);
//   }

//   if (title != "Unavailability") {
//     sidediv(title);
//   }

//   function closeOverlay() {
//     toggleClass(overlay, ["hidden"], ["inset-0"]);
//     toggleClass(tempDiv, ["translate-x-full"], ["translate-x-0"]);
//     tempDiv.classList.add("hidden");
//   }

//   function openOverlay() {
//     toggleClass(overlay, ["inset-0"], ["hidden"]);
//     toggleClass(tempDiv, ["translate-x-0"], ["translate-x-full"]);
//   }

//   function updateOptions() {
//     additionalListDiv.innerHTML = additionalOptions
//       .filter((option) => {
//         return !Array.from(listDiv.children).some((child) => {
//           const procedureDiv = child.querySelector("[procedure-type-id]");
//           const siteDiv = child.querySelector("[site-id]");

//           const nameToCompare = procedureDiv
//             ? procedureDiv.textContent.trim()
//             : siteDiv
//             ? siteDiv.textContent.trim()
//             : null;

//           return nameToCompare === option.name;
//         });
//       })
//       .map(
//         (option) => `
//         <div class="text-sm md:text-lg px-6 py-2.5 shadow-md cursor-pointer rounded-3xl bg-[#F6F6F6] hover:bg-gray-300 flex justify-center items-center" data-id="${option.id}">
//             ${option.name}
//         </div>
//         `
//       )
//       .join("");

//     additionalListDiv.querySelectorAll("div").forEach((optionDiv) => {
//       optionDiv.addEventListener("click", () => {
//         let newItemHTML = "";
//         if (title == "Procedure") {
//           newItemHTML = `<div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${title.toLowerCase()}-cards">
//                     <div class="text-gray-900 text-sm md:text-lg font-medium" procedure-type-id="${optionDiv.getAttribute(
//                       "data-id"
//                     )}">
//                         ${optionDiv.textContent}
//                     </div>
//                     <div class="text-gray-600 flex items-center justify-between">
//                         <select class="bg-white text-gray-600 rounded-md text-[12px] md:text-lg">
//             <option id=0 value="NA">
//                 NA
//             </option>
//             ${JSON.parse(localStorage.getItem("all-experience"))
//               .map(
//                 (experience) => `
//                         <option id="${experience.id}" value="${
//                   experience.name
//                 }" ${experience.name === "Novice" ? "selected" : ""}>
//                             ${experience.name}
//                         </option>
//                     `
//               )
//               .join("")}
//         </select>
//                         <button
//                             class="text-black rounded-md hover:text-red-500 ml-3"
//                             title="Remove item ${title}">
//                             <img src="/svg/delete.svg" alt="Delete Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//                         </button>
//                     </div>
//                 </div>`;
//         } else {
//           newItemHTML = `
//                     <div class="grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${title.toLowerCase()}-cards">
//                         <div class="text-gray-900 font-medium" site-id="${optionDiv.getAttribute(
//                           "data-id"
//                         )}">
//                             ${optionDiv.textContent}
//                         </div>
//                         <div class="text-gray-600 flex justify-end">
//                             <button
//                                 class="text-black rounded-md hover:text-red-500 ml-3"
//                                 title="Remove item ${title}">
//                                 <img src="/svg/delete.svg" alt="Delete Icon" class="w-4 h-4 sm:w-8 sm:h-8" />
//                             </button>
//                         </div>
//                     </div>`;
//         }

//         listDiv.innerHTML += newItemHTML;

//         const newRemoveButton = sectionDiv.querySelectorAll(
//           `button[title="Remove item ${title}"]`
//         );
//         newRemoveButton.forEach((button) => {
//           button.addEventListener("click", () => {
//             button.closest("div.grid").remove();
//             updateOptions();
//           });
//         });

//         closeOverlay();
//         updateOptions();
//       });
//     });
//   }

//   return sectionDiv;
// }

function createPreferenceSection(title, items) {
  // Adjust the title based on type
  let formattedTitle;
  if (title === "procedures") {
    formattedTitle = "Procedure";
  } else if (title === "sites") {
    formattedTitle = "Sites";
  } else if (title === "additional_values") {
    formattedTitle = "Location";
  } else {
    formattedTitle = "Unavailability";
  }

  // Create the main section div
  const sectionDiv = document.createElement("div");
  sectionDiv.className = "bg-white rounded-[12px] shadow-md w-full md:w-[40%]";

  // Create header container
  const headerContainer = document.createElement("div");
  headerContainer.className = "relative bg-[#8F58DD] p-4 rounded-t-[12px]";

  // Build header based on section type
  if (formattedTitle === "Procedure") {
    const gridDiv = document.createElement("div");
    gridDiv.className = "grid grid-cols-2 gap-4 items-center";

    const procedureTitle = document.createElement("div");
    procedureTitle.className = "font-medium md:font-bold text-white";
    procedureTitle.textContent = "Procedure";
    gridDiv.appendChild(procedureTitle);

    const experienceTitle = document.createElement("div");
    experienceTitle.className = "font-medium md:font-bold text-white";
    experienceTitle.textContent = "Experience";
    gridDiv.appendChild(experienceTitle);

    headerContainer.appendChild(gridDiv);

    const addButton = document.createElement("button");
    addButton.className =
      "absolute top-2 p-2 right-2 hover:scale-110 transition-all duration-300 ease-in-out";
    addButton.title = `Add more ${formattedTitle}`;
    const addImg = document.createElement("img");
    addImg.src = "/svg/add.svg";
    addImg.alt = "Add Icon";
    addImg.className = "w-4 h-4 sm:w-8 sm:h-8";
    addButton.appendChild(addImg);
    headerContainer.appendChild(addButton);
  } else if (formattedTitle === "Sites") {
    const gridDiv = document.createElement("div");
    gridDiv.className = "grid grid-cols-1 gap-4 items-center";

    const siteTitle = document.createElement("div");
    siteTitle.className = "font-medium md:font-bold text-white";
    siteTitle.textContent = "Site Name";
    gridDiv.appendChild(siteTitle);

    headerContainer.appendChild(gridDiv);

    const addButton = document.createElement("button");
    addButton.className =
      "absolute top-2 p-2 right-2 hover:scale-110 transition-all duration-300 ease-in-out";
    addButton.title = `Add more ${formattedTitle}`;
    const addImg = document.createElement("img");
    addImg.src = "/svg/add.svg";
    addImg.alt = "Add Icon";
    addImg.className = "w-4 h-4 sm:w-8 sm:h-8";
    addButton.appendChild(addImg);
    headerContainer.appendChild(addButton);
  } else if (formattedTitle === "Location") {
    const gridDiv = document.createElement("div");
    gridDiv.className = "grid grid-cols-2 gap-4 items-center";

    const locTitle = document.createElement("div");
    locTitle.className = "font-medium md:font-bold text-white";
    locTitle.textContent = formattedTitle;
    gridDiv.appendChild(locTitle);

    const zipTitle = document.createElement("div");
    zipTitle.className = "font-medium md:font-bold text-white";
    zipTitle.textContent = "Zip Code";
    gridDiv.appendChild(zipTitle);

    headerContainer.appendChild(gridDiv);
  } else {
    // Unavailability
    const gridDiv = document.createElement("div");
    gridDiv.className = "grid grid-cols-2 gap-4 items-center";

    const unavailTitle = document.createElement("div");
    unavailTitle.className = "font-medium md:font-bold text-white";
    unavailTitle.textContent = formattedTitle;
    gridDiv.appendChild(unavailTitle);

    const reasonTitle = document.createElement("div");
    reasonTitle.className = "font-medium md:font-bold text-white";
    reasonTitle.textContent = "Reason";
    gridDiv.appendChild(reasonTitle);

    headerContainer.appendChild(gridDiv);

    // Add calendar icon and hidden date picker input.
    const calendarIcon = document.createElement("div");
    calendarIcon.id = "calendar-icon";
    calendarIcon.className =
      "absolute bg-white top-2 rounded-[20px] p-2 right-2 cursor-pointer";
    const calendarImg = document.createElement("img");
    calendarImg.src = "/svg/calendar-icon.svg";
    calendarImg.alt = "Calendar Icon";
    calendarImg.className = "w-5 h-5";
    calendarIcon.appendChild(calendarImg);
    headerContainer.appendChild(calendarIcon);

    const datePicker = document.createElement("input");
    datePicker.type = "date";
    datePicker.id = "date-picker";
    datePicker.className =
      "hidden absolute top-2 right-2 border border-gray-300 rounded";
    headerContainer.appendChild(datePicker);
  }

  sectionDiv.appendChild(headerContainer);

  // Create container for rows/cards
  const rowsContainer = document.createElement("div");
  rowsContainer.className = "flex flex-col divide-y divide-gray-300";

  // Build rows/cards based on the type of section
  if (formattedTitle === "Procedure") {
    items.forEach((procedure) => {
      if (procedure.procedure_type_id) {
        const rowDiv = document.createElement("div");
        rowDiv.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${formattedTitle.toLowerCase()}-cards`;

        // Procedure name column
        const procDiv = document.createElement("div");
        procDiv.className = "text-gray-900 text-sm md:text-lg font-medium";
        procDiv.setAttribute("procedure-type-id", procedure.procedure_type_id);
        procDiv.textContent = procedure.name;
        rowDiv.appendChild(procDiv);

        // Experience select and delete button
        const expDiv = document.createElement("div");
        expDiv.className = "text-gray-600 flex items-center justify-between";

        const select = document.createElement("select");
        select.className =
          "bg-white text-gray-600 rounded-md text-[12px] md:text-lg";
        // Option NA
        const optionNA = document.createElement("option");
        optionNA.id = "0";
        optionNA.value = "NA";
        optionNA.textContent = "NA";
        if (procedure.experience_name === "NA") {
          optionNA.selected = true;
        }
        select.appendChild(optionNA);
        // Other experience options
        const allExperience =
          JSON.parse(localStorage.getItem("all-experience")) || [];
        allExperience.forEach((experience) => {
          const option = document.createElement("option");
          option.id = experience.id;
          option.value = experience.name;
          option.textContent = experience.name;
          if (experience.name === procedure.experience_name) {
            option.selected = true;
          }
          select.appendChild(option);
        });
        expDiv.appendChild(select);

        // Delete button
        const delButton = document.createElement("button");
        delButton.className = "text-black rounded-md hover:text-red-500 ml-3";
        delButton.title = `Remove item ${formattedTitle}`;
        const delImg = document.createElement("img");
        delImg.src = "/svg/delete.svg";
        delImg.alt = "Delete Icon";
        delImg.className = "w-4 h-4 sm:w-8 sm:h-8";
        delButton.appendChild(delImg);
        expDiv.appendChild(delButton);

        rowDiv.appendChild(expDiv);
        rowsContainer.appendChild(rowDiv);
      }
    });
  } else if (formattedTitle === "Sites") {
    items.forEach((site) => {
      const rowDiv = document.createElement("div");
      rowDiv.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${formattedTitle.toLowerCase()}-cards`;

      const siteDiv = document.createElement("div");
      siteDiv.className = "text-sm md:text-lg text-gray-900 font-medium";
      siteDiv.setAttribute("site-id", site.id);
      siteDiv.textContent = site.name;
      rowDiv.appendChild(siteDiv);

      const btnDiv = document.createElement("div");
      btnDiv.className = "text-gray-600 flex justify-end";
      const delButton = document.createElement("button");
      delButton.className = "text-black rounded-md hover:text-red-500 ml-3";
      delButton.title = `Remove item ${formattedTitle}`;
      const delImg = document.createElement("img");
      delImg.src = "/svg/delete.svg";
      delImg.alt = "Delete Icon";
      delImg.className = "w-4 h-4 sm:w-8 sm:h-8";
      delButton.appendChild(delImg);
      btnDiv.appendChild(delButton);
      rowDiv.appendChild(btnDiv);
      rowsContainer.appendChild(rowDiv);
    });
  } else if (formattedTitle === "Location") {
    Object.entries(items).forEach(([key, value]) => {
      if (key === "home_zip_code") {
        const rowDiv = document.createElement("div");
        rowDiv.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${key.toLowerCase()}-cards`;

        const labelDiv = document.createElement("div");
        labelDiv.className = "text-gray-900 font-medium";
        labelDiv.setAttribute("location-id", key);
        labelDiv.textContent = "Home";
        rowDiv.appendChild(labelDiv);

        const inputDiv = document.createElement("div");
        inputDiv.className = "text-gray-600 flex items-center justify-between";
        const inputField = document.createElement("input");
        inputField.type = "text";
        inputField.value = value;
        inputField.className =
          "border border-gray-300 rounded px-2 py-1 w-full";
        inputField.placeholder = "Enter Zip Code";
        inputField.dataset.zipCode = value;
        inputDiv.appendChild(inputField);
        rowDiv.appendChild(inputDiv);
        rowsContainer.appendChild(rowDiv);
      }
    });
  } else {
    // Unavailability: items is assumed to be an object with date keys and reason values.
    Object.entries(items).forEach(([date, reason]) => {
      const rowDiv = document.createElement("div");
      rowDiv.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${formattedTitle.toLowerCase()}-cards`;

      const dateDiv = document.createElement("div");
      dateDiv.className = "text-gray-900 font-medium";
      dateDiv.setAttribute("unavailability-date-id", date);
      dateDiv.textContent = date;
      rowDiv.appendChild(dateDiv);

      const inputDiv = document.createElement("div");
      inputDiv.className = "text-gray-600 flex items-center justify-between";
      const inputField = document.createElement("input");
      inputField.type = "text";
      inputField.value = reason;
      inputField.className =
        "border border-gray-300 rounded px-2 py-1 w-full text-sm md:text-lg";
      inputField.placeholder = "Enter Reason";
      inputField.dataset.date = date;
      inputDiv.appendChild(inputField);

      const delButton = document.createElement("button");
      delButton.className = "text-black rounded-md hover:text-red-500 ml-2";
      delButton.title = `Remove item ${formattedTitle}`;
      const delImg = document.createElement("img");
      delImg.src = "/svg/delete.svg";
      delImg.alt = "Delete Icon";
      delImg.className = "w-4 h-4 sm:w-8 sm:h-8";
      delButton.appendChild(delImg);
      inputDiv.appendChild(delButton);

      rowDiv.appendChild(inputDiv);
      rowsContainer.appendChild(rowDiv);
    });
  }

  sectionDiv.appendChild(rowsContainer);

  // Event listeners for add and remove buttons
  if (formattedTitle !== "Unavailability") {
    const addButtons = headerContainer.querySelectorAll(
      `button[title="Add more ${formattedTitle}"]`
    );
    addButtons.forEach((button) => {
      button.addEventListener("click", () => {
        sidediv(formattedTitle);
        tempDiv.classList.remove("hidden");
        openOverlay();
        updateOptions();
      });
    });
  } else {
    const calendarIcon = headerContainer.querySelector("#calendar-icon");
    const datePicker = headerContainer.querySelector("#date-picker");
    if (calendarIcon && datePicker) {
      calendarIcon.addEventListener("click", () => {
        datePicker.classList.remove("hidden");
        calendarIcon.classList.add("hidden");
        datePicker.focus();
      });
      datePicker.addEventListener("focusout", () => {
        calendarIcon.classList.remove("hidden");
        datePicker.classList.add("hidden");
      });
      datePicker.addEventListener("fullscreenchange", () => {
        datePicker.classList.add("hidden");
        calendarIcon.classList.remove("hidden");
      });
      datePicker.addEventListener("change", (event) => {
        const selectedDate = event.target.value;
        // Create a new row for the selected unavailability date
        const dataPickerRow = document.createElement("div");
        dataPickerRow.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${formattedTitle.toLowerCase()}-cards`;

        const dateDiv = document.createElement("div");
        dateDiv.className = "text-gray-900 font-medium";
        dateDiv.setAttribute("unavailability-date-id", selectedDate);
        dateDiv.textContent = selectedDate;
        dataPickerRow.appendChild(dateDiv);

        const inputDiv = document.createElement("div");
        inputDiv.className = "text-gray-600 flex items-center justify-between";
        const inputField = document.createElement("input");
        inputField.type = "text";
        inputField.value = "";
        inputField.className =
          "border border-gray-300 rounded px-2 py-1 w-full";
        inputField.placeholder = "Enter Reason";
        inputField.dataset.date = selectedDate;
        inputDiv.appendChild(inputField);

        const delButton = document.createElement("button");
        delButton.className = "text-black rounded-md hover:text-red-500 ml-3";
        delButton.title = `Remove item ${formattedTitle}`;
        const delImg = document.createElement("img");
        delImg.src = "/svg/delete.svg";
        delImg.alt = "Delete Icon";
        delImg.className = "w-4 h-4 sm:w-8 sm:h-8";
        delButton.appendChild(delImg);
        inputDiv.appendChild(delButton);

        dataPickerRow.appendChild(inputDiv);
        rowsContainer.appendChild(dataPickerRow);

        // Delete event listener for the new row
        delButton.addEventListener("click", () => {
          dataPickerRow.remove();
        });

        datePicker.classList.add("hidden");
        calendarIcon.classList.remove("hidden");
      });
    }
  }

  // Global event listeners for all remove buttons in this section
  const removeButtons = sectionDiv.querySelectorAll(
    `button[title="Remove item ${formattedTitle}"]`
  );
  removeButtons.forEach((button) => {
    button.addEventListener("click", (event) => {
      const gridRow = button.closest("div.grid");
      if (gridRow) {
        gridRow.remove();
        updateOptions(event);
      }
    });
  });

  // Overlay related variables and functions
  let overlay, tempDiv, additionalListDiv, additionalOptions;

  function sidediv(title) {
    overlay = document.createElement("div");
    overlay.className = "fixed inset-0 bg-black bg-opacity-50 z-40 hidden";
    const parentDiv = document.getElementById("content-div");
    parentDiv.classList.add("translate-x-0");
    parentDiv.appendChild(overlay);

    tempDiv = document.createElement("div");
    tempDiv.className =
      "w-[14rem] md:w-80 fixed flex flex-col right-0 top-0 h-full bg-[#F1F1FB] shadow-lg z-50 transform transition-transform duration-300 hidden";
    parentDiv.appendChild(tempDiv);

    const containerDiv = document.createElement("div");
    containerDiv.className =
      "bg-white m-4 rounded-[12px] flex flex-col overflow-y-auto";

    const headerDiv = document.createElement("div");
    headerDiv.className =
      "text-sm md:text-lg flex font-medium md:font-bold ml-4 py-4 relative text-black";
    headerDiv.textContent = `Additions for ${title}`;

    const closeBtn = document.createElement("button");
    closeBtn.className =
      "close-btn text-black hover:scale-110 transform transition-transform absolute top-4 right-4";
    const closeIcon = document.createElement("i");
    closeIcon.className = "fa-solid fa-xmark";
    closeBtn.appendChild(closeIcon);
    headerDiv.appendChild(closeBtn);

    containerDiv.appendChild(headerDiv);
    const hr = document.createElement("hr");
    hr.className = "border";
    containerDiv.appendChild(hr);

    additionalListDiv = document.createElement("div");
    additionalListDiv.className = "overflow-y-auto flex flex-wrap gap-4 p-4";
    containerDiv.appendChild(additionalListDiv);

    tempDiv.appendChild(containerDiv);

    closeBtn.addEventListener("click", closeOverlay);
    overlay.addEventListener("click", closeOverlay);

    // Load additional options based on title
    switch (title) {
      case "Experience":
        additionalOptions =
          JSON.parse(localStorage.getItem("all-experience")) || [];
        break;
      case "Procedure":
        additionalOptions =
          JSON.parse(localStorage.getItem("all-procedure")) || [];
        break;
      case "Sites":
        additionalOptions = JSON.parse(localStorage.getItem("all-sites")) || [];
        break;
      default:
        additionalOptions = [];
        break;
    }
  }

  function closeOverlay() {
    if (overlay) overlay.classList.add("hidden");
    if (tempDiv) tempDiv.classList.add("hidden");
  }

  function openOverlay() {
    if (overlay) overlay.classList.remove("hidden");
    if (tempDiv) tempDiv.classList.remove("hidden");
  }

  function updateOptions() {
    if (!additionalListDiv) return;
    // Clear any current options
    additionalListDiv.innerHTML = "";
    // Filter out options that already exist in the rows
    additionalOptions
      .filter((option) => {
        return !Array.from(rowsContainer.children).some((child) => {
          let name = "";
          const procedureDiv = child.querySelector("[procedure-type-id]");
          const siteDiv = child.querySelector("[site-id]");
          if (procedureDiv) {
            name = procedureDiv.textContent.trim();
          } else if (siteDiv) {
            name = siteDiv.textContent.trim();
          }
          return name === option.name;
        });
      })
      .forEach((option) => {
        const optDiv = document.createElement("div");
        optDiv.className =
          "text-sm md:text-lg px-6 py-2.5 shadow-md cursor-pointer rounded-3xl bg-[#F6F6F6] hover:bg-gray-300 flex justify-center items-center";
        optDiv.textContent = option.name;
        optDiv.dataset.id = option.id;
        additionalListDiv.appendChild(optDiv);
        optDiv.addEventListener("click", () => {
          let newItemDiv;
          if (formattedTitle === "Procedure") {
            newItemDiv = document.createElement("div");
            newItemDiv.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${formattedTitle.toLowerCase()}-cards`;
            const procDiv = document.createElement("div");
            procDiv.className = "text-gray-900 text-sm md:text-lg font-medium";
            procDiv.setAttribute("procedure-type-id", option.id);
            procDiv.textContent = option.name;
            newItemDiv.appendChild(procDiv);

            const expDiv = document.createElement("div");
            expDiv.className =
              "text-gray-600 flex items-center justify-between";
            const select = document.createElement("select");
            select.className =
              "bg-white text-gray-600 rounded-md text-[12px] md:text-lg";
            const naOption = document.createElement("option");
            naOption.id = "0";
            naOption.value = "NA";
            naOption.textContent = "NA";
            select.appendChild(naOption);
            const expList =
              JSON.parse(localStorage.getItem("all-experience")) || [];
            expList.forEach((exp) => {
              const optionElem = document.createElement("option");
              optionElem.id = exp.id;
              optionElem.value = exp.name;
              optionElem.textContent = exp.name;
              if (exp.name === "Novice") {
                optionElem.selected = true;
              }
              select.appendChild(optionElem);
            });
            expDiv.appendChild(select);
            const delButton = document.createElement("button");
            delButton.className =
              "text-black rounded-md hover:text-red-500 ml-3";
            delButton.title = `Remove item ${formattedTitle}`;
            const delImg = document.createElement("img");
            delImg.src = "/svg/delete.svg";
            delImg.alt = "Delete Icon";
            delImg.className = "w-4 h-4 sm:w-8 sm:h-8";
            delButton.appendChild(delImg);
            expDiv.appendChild(delButton);
            newItemDiv.appendChild(expDiv);
          } else {
            newItemDiv = document.createElement("div");
            newItemDiv.className = `grid grid-cols-2 gap-4 items-center py-3 relative p-4 ${formattedTitle.toLowerCase()}-cards`;
            const siteDiv = document.createElement("div");
            siteDiv.className = "text-gray-900 text-sm md:text-lg font-medium";
            siteDiv.setAttribute("site-id", option.id);
            siteDiv.textContent = option.name;
            newItemDiv.appendChild(siteDiv);

            const btnDiv = document.createElement("div");
            btnDiv.className = "text-gray-600 flex justify-end";
            const delButton = document.createElement("button");
            delButton.className =
              "text-black rounded-md hover:text-red-500 ml-3";
            delButton.title = `Remove item ${formattedTitle}`;
            const delImg = document.createElement("img");
            delImg.src = "/svg/delete.svg";
            delImg.alt = "Delete Icon";
            delImg.className = "w-4 h-4 sm:w-8 sm:h-8";
            delButton.appendChild(delImg);
            btnDiv.appendChild(delButton);
            newItemDiv.appendChild(btnDiv);
          }
          rowsContainer.appendChild(newItemDiv);
          // Bind event to the new remove button
          const newRemoveBtn = newItemDiv.querySelector(
            `button[title="Remove item ${formattedTitle}"]`
          );
          if (newRemoveBtn) {
            newRemoveBtn.addEventListener("click", () => {
              newItemDiv.remove();
              updateOptions();
            });
          }
          closeOverlay();
          updateOptions();
        });
      });
  }

  return sectionDiv;
}

function generateCombinedJson() {
  const procedures = [];
  document.querySelectorAll(".procedure-cards").forEach((card) => {
    const procedureTypeId = card
      .querySelector("[procedure-type-id]")
      .getAttribute("procedure-type-id");
    const name = card.querySelector("[procedure-type-id]").textContent.trim();
    const selectElement = card.querySelector("select");
    const experience = selectElement ? selectElement.value.trim() : "NA";
    const experienceId = selectElement
      ? selectElement.options[selectElement.selectedIndex]?.id
      : null;

    procedures.push({
      procedure_type_id: procedureTypeId,
      name: name,
      experience: parseInt(experienceId, 10),
      experience_name: experience,
    });
  });

  if (procedures.length == 0) {
    procedures.push({
      procedure_type_id: null,
      name: null,
      experience: null,
      experience_name: null,
    });
  }

  let combined = {};
  const { id, rep_name, physician_name } = activerepprovider;
  const { last_name, first_name, middle_name, additional_values } =
    activeTechInitialSkillSet || {};

  if (rep_name) {
    const unavailability = Array.from(
      document.querySelectorAll(".unavailability-cards")
    ).reduce((acc, card) => {
      const date = card
        .querySelector("[unavailability-date-id]")
        .getAttribute("unavailability-date-id")
        .trim();
      const reason = card.querySelector("input[type='text']").value.trim();
      acc[date] = reason;
      return acc;
    }, {});

    const zipCodeData = Array.from(
      document.querySelectorAll(".home_zip_code-cards")
    ).reduce((acc, card) => {
      const zipCode = card.querySelector("input[type='text']").value.trim();
      acc["home_zip_code"] = zipCode;
      return acc;
    }, {});

    const updated_values = JSON.parse(JSON.stringify(additional_values));

    Object.keys(zipCodeData).forEach((key) => {
      if (updated_values.hasOwnProperty(key)) {
        updated_values[key] = zipCodeData[key];
      }
    });

    combined = {
      id,
      rep_name,
      last_name,
      first_name,
      additional_values: updated_values,
      procedures,
      unavailability_date: unavailability,
    };
  } else {
    const sites = Array.from(document.querySelectorAll(".sites-cards")).map(
      (card) => ({
        id: card.querySelector("[site-id]").getAttribute("site-id"),
        name: card.querySelector("[site-id]").textContent.trim(),
      })
    );

    combined = {
      id,
      physician_name,
      last_name,
      first_name,
      middle_name,
      procedures,
      sites,
    };
  }
  return combined;
}

function areObjectsEqual(obj1, obj2) {
  if (obj1 === obj2) {
    return true;
  }
  if (
    typeof obj1 !== "object" ||
    obj1 === null ||
    typeof obj2 !== "object" ||
    obj2 === null
  ) {
    return false;
  }
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let key of keys1) {
    if (!keys2.includes(key)) {
      return false; // Key mismatch
    }
    if (!areObjectsEqual(obj1[key], obj2[key])) {
      return false;
    }
  }
  return true;
}

function isSkillSetChangedFunction() {
  const techCopy = { ...activeTechInitialSkillSet };
  activeTechCurrentSkillSet = generateCombinedJson();
  isSkillSetChanged = !areObjectsEqual(activeTechCurrentSkillSet, techCopy);
}

// Global variables for tab management
let activeTab = "rep"; // Default tab

// Function to handle toggle button clicks for collapsible sections
function handleToggleClick(event) {
  // Stop event propagation to prevent it from bubbling up to the container
  event.stopPropagation();

  const button = event.target;
  button.classList.toggle("rotated");

  const sectionId = button.getAttribute("data-section-id");
  const sectionName = button.getAttribute("data-section-name");

  if (sectionName) {
    const section = document.getElementById(sectionName);
    if (section) {
      section.classList.toggle("hidden");
    }
  } else if (sectionId) {
    const sections = document.querySelectorAll(
      `[data-section-id="${sectionId}"]`
    );
    sections.forEach((section) => {
      if (section !== button) {
        section.classList.toggle("hidden");
      }
    });
  }
}

async function loadTechSkillSet() {
  setActiveNav("settings");
  setVariablesToDefault();
  setVisibilityForContents();

  // Make sure the Rep tab is active by default
  activeTab = "rep";

  // Initialize tab event listeners
  initializeTabEventListeners();

  // Make sure to switch to the default tab
  switchTab(activeTab);

  const techsNames = document.getElementById("techsNames");
  const providerNames = document.getElementById("providerNames");
  const repproviderList = document.getElementById("repproviderList");
  const searchInput = document.getElementById("search-input-skillset");
  const activeReps = await fetchActiveReps();
  const activeProviders = await fetchActiveProviders();

  const getproviderandexperience = await fetchproviderandexperience();

  const allExperience = [
    ...new Set(getproviderandexperience.map((rep) => rep.experience)),
  ].sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

  const allProcedure = [
    ...new Set(getproviderandexperience.map((rep) => rep.procedure)),
  ].sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

  const allSites = [
    ...new Set(getproviderandexperience.map((rep) => rep.sites)),
  ].sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

  localStorage.setItem("all-experience", JSON.stringify(allExperience[0]));
  localStorage.setItem("all-procedure", JSON.stringify(allProcedure[0]));
  localStorage.setItem("all-sites", JSON.stringify(allSites[0]));

  const handleToggleClick = (event) => {
    const button = event.target;
    button.classList.toggle("rotated");
    const sectionId = button.getAttribute("data-section-id");
    const sectionname = button.getAttribute("data-section-name");

    const maindiv = document.getElementById(sectionname);
    if (button.classList.contains("rotated")) {
      maindiv.classList.remove("flex-1");
    } else {
      maindiv.classList.add("flex-1");
    }
    const contentDiv = document.querySelector(
      `.section-content[data-section-id="${sectionId}"]`
    );

    if (contentDiv) {
      if (contentDiv.classList.contains("translate-x-full")) {
        contentDiv.classList.remove("hidden");
        setTimeout(() => {
          contentDiv.classList.remove("translate-x-full", "scale-0");
          contentDiv.classList.add("translate-x-0", "scale-100");
          button.textContent = "+";
        }, 300);
      } else {
        contentDiv.classList.remove("translate-x-0", "scale-100");
        contentDiv.classList.add("translate-x-full", "scale-0");
        button.textContent = "-";
        setTimeout(() => {
          contentDiv.classList.add("hidden");
        }, 300);
      }
    }
  };

  const updateTechList = (div_id, role, data) => {
    const div_table = document.getElementById(div_id);
    div_table.classList.add("flex-1");
    const searchTerm = searchInput.value.toLowerCase();

    const filteredTechs = data.filter((tech) => {
      const repName = tech.rep_name ? tech.rep_name.toLowerCase() : "";
      const physicianName = tech.physician_name
        ? tech.physician_name.toLowerCase()
        : "";
      const matchesSearch =
        repName.includes(searchTerm) || physicianName.includes(searchTerm);
      return matchesSearch;
    });

    const active = document.querySelectorAll(".tech-name");
    active.forEach((element) => {
      element.classList.remove("bg-gray-300");
    });

    active_div = `<div class="flex flex-col">
                        <div class="bg-[#E7E7E7]">
                       <div class="flex flex-row justify-between m-3">
                        <h3 class="text-sm md:text-lg font-medium md:font-semibold text-center mb-2 text-black">${role}</h3>
                            <img src="/svg/arrow.svg" alt="image" class="toggle-button-${role.replace(
                              /\s+/g,
                              "_"
                            )} cursor-pointer rotate-image" data-section-id=${role} data-section-name=${div_id}></div>
                            </div>
                            <div id="data-div" class="space-y-2 section-content my-2" data-section-id=${role}>
                        ${filteredTechs
                          .map((tech) => createTechCard(tech, role))
                          .join("")}
                        </div>
                    </div>`;

    div_table.innerHTML = active_div;

    function attachToggleListener(role) {
      const toggleButtons = document.querySelectorAll(
        `.toggle-button-${role.replace(/\s+/g, "_")}`
      );
      toggleButtons.forEach((button) => {
        button.addEventListener("click", handleToggleClick);
      });
    }

    attachToggleListener(role);

    searchInput.placeholder = `Search`;

    if (filteredTechs.length === 0) {
      const specificDiv = div_table.querySelector("#data-div");
      specificDiv.innerHTML = "No Match Found";
      specificDiv.classList.add("text-primary");
    }

    div_table.querySelectorAll(".tech-name").forEach((card) => {
      card.addEventListener("click", async (event) => {
        event.preventDefault();
        event.stopImmediatePropagation();
        const id = card.getAttribute("data-id");
        const choosenTech = data.find((t) => t.id == id);

        if (activerepprovider) {
          isSkillSetChangedFunction();

          if (isSkillSetChanged) {
            const result = await showStaticPopup(
              "You have some unsaved Changes, Do you want to update the skillset?",
              "bg-red-500"
            );

            if (result) {
              document.getElementById("updateSkillSetDiv").remove();
              activerepprovider = choosenTech;
              activerole = role;
              updateTechList(div_id, role, data);
              editTech();
            }
          } else {
            document.getElementById("updateSkillSetDiv").remove();
            activerepprovider = choosenTech;
            activerole = role;
            updateTechList(div_id, role, data);
            editTech();
          }
        } else {
          activerepprovider = choosenTech;
          activerole = role;
          updateTechList(div_id, role, data);
          editTech();
        }
      });
    });
  };

  const createTechCard = (tech, role) => `
    <hr class="border"></hr>
        <div class="tech-name relative transition-all duration-300 ease-in-out
            ${
              activerepprovider && activerepprovider.id === tech.id
                ? "bg-gray-300"
                : "hover:bg-gray-200 hover:text-black"
            } text-black p-2 rounded cursor-pointer"
            data-employee-name="${
              tech.rep_name || tech.physician_name || ""
            }" role="${role}"
            data-id="${tech.id}">
            <div class="text-container">
                <div class="font-medium md:font-semibold">${
                  tech.rep_name || tech.physician_name || ""
                }</div>
            </div>
        </div>
    `;

  // Use a function that fetches fresh data for search
  searchInput.addEventListener("input", async () => {
    // For rep search, always fetch fresh data to include newly created reps
    const freshReps = await fetchActiveReps();
    updateTechList("techsNames", "Clinical Specialist", freshReps);
    updateTechList("providerNames", "Providers", activeProviders);
  });
  const clearSearchButton = document.getElementById("clear-search");
  [searchInput, clearSearchButton].forEach((element) => {
    element.addEventListener("click", (e) => {
      e.stopPropagation();
    });
  });

  document
    .getElementById("clear-search")
    .addEventListener("click", async () => {
      searchInput.value = "";
      // Always fetch fresh data when clearing search to include newly created reps
      const freshReps = await fetchActiveReps();
      updateTechList("techsNames", "Clinical Specialist", freshReps);
      updateTechList("providerNames", "Providers", activeProviders);
      searchInput.focus();
    });

  // Always fetch fresh data for initial load to include newly created reps
  fetchActiveReps()
    .then((freshReps) => {
      updateTechList("techsNames", "Clinical Specialist", freshReps);
      updateTechList("providerNames", "Providers", activeProviders);
      removeLoader();
      toggleClass(repproviderList, ["flex", "flex-row"], ["hidden"]);
    })
    .catch((error) => {
      console.error("Error fetching fresh reps:", error);
      // Fallback to using the already fetched reps
      updateTechList("techsNames", "Clinical Specialist", activeReps);
      updateTechList("providerNames", "Providers", activeProviders);
      removeLoader();
      toggleClass(repproviderList, ["flex", "flex-row"], ["hidden"]);
    });
  techsNames.classList.remove("hidden");
  providerNames.classList.remove("hidden");
}

function clearSelectPerson() {
  updateTechList("techsNames", "Clinical Specialist", activeReps);
  updateTechList("providerNames", "Providers", activeProviders);
}

async function showStaticPopup(message, bgColorClass) {
  return new Promise((resolve) => {
    const popup = document.getElementById("confirmation-popup");
    const popupMessage = document.getElementById("confirmation-popup-message");
    const discardBtn = document.getElementById("skill-set-discard-btn");
    const updateBtn = document.getElementById("skill-set-update-btn");

    popupMessage.textContent = message;

    popup.className = popup.className.replace(/bg-\w+-\d+/g, "").trim();
    popup.classList.add(bgColorClass, "translate-y-6", "opacity-100");
    popup.classList.remove("-translate-y-full", "opacity-0");

    const closePopup = () => {
      popup.classList.remove("translate-y-6", "opacity-100");
      popup.classList.add("-translate-y-full", "opacity-0");
    };

    discardBtn.replaceWith(discardBtn.cloneNode(true));
    const newDiscardBtn = document.getElementById("skill-set-discard-btn");

    newDiscardBtn.addEventListener("click", () => {
      closePopup();
      resolve(true);
    });

    updateBtn.replaceWith(updateBtn.cloneNode(true));
    const newUpdateBtn = document.getElementById("skill-set-update-btn");

    newUpdateBtn.addEventListener("click", async () => {
      await updateButtonAction(activerole);
      closePopup();
      resolve(true);
    });
  });
}

//rep form handler

async function openRepForm(event) {
  if (event) event.preventDefault();

  try {
    loadTechSkillSet();
    const rep = document.getElementById("repproviderSkillSet");
    rep.innerHTML = "";
    $(rep).load("repform.html");
    const procedureType = await getprocedureType();
    const experienceType = await getExperienceType();
    const procedureEle = document.getElementById("proceduretype");
    const experienceEle = document.getElementById("experience");
    procedureEle.innerHTML = "";
    experienceEle.innerHTML = "";
    procedureType.map((procedure) => {
      const option = document.createElement("option");
      option.value = procedure.id;
      option.textContent = procedure.name;
      procedureEle.appendChild(option);
    });
    experienceType.map((experience) => {
      const option = document.createElement("option");
      option.value = experience.id;
      option.textContent = experience.name;
      experienceEle.appendChild(option);
    });
    const passwordInput = document.getElementById("password");

    // Ensure the password input exists before proceeding
    if (!passwordInput) {
      console.error("Password input element with ID 'password' not found.");
      return;
    }

    // Create the wrapper div and set it up with relative positioning
    const passwordWrapper = document.createElement("div");
    passwordWrapper.style.position = "relative";
    passwordWrapper.style.display = "inline-block";
    passwordWrapper.style.width = "100%"; // Ensuring it matches input width

    // Insert the wrapper before the password input in its parent node
    passwordInput.parentNode.insertBefore(passwordWrapper, passwordInput);

    // Move the password input into the wrapper
    passwordWrapper.appendChild(passwordInput);

    // Adjust the password input's style to fit inside the wrapper
    passwordInput.style.paddingRight = "35px"; // Ensuring space for the icon

    // Create the toggle element (eye icon)
    const togglePassword = document.createElement("span");
    togglePassword.innerHTML = "👁";
    togglePassword.style.cursor = "pointer";
    togglePassword.style.position = "absolute";
    togglePassword.style.right = "10px";
    togglePassword.style.top = "50%";
    togglePassword.style.transform = "translateY(-50%)";
    togglePassword.style.fontSize = "18px";
    togglePassword.style.color = "#555";

    // Append the toggle button to the wrapper
    passwordWrapper.appendChild(togglePassword);

    // Add an event listener to toggle the password visibility
    togglePassword.addEventListener("click", function () {
      if (passwordInput.type === "password") {
        passwordInput.type = "text";
        togglePassword.innerHTML = "🚫";
      } else {
        passwordInput.type = "password";
        togglePassword.innerHTML = "👁️";
      }
    });
  } catch (err) {
    console.log(err);
  } finally {
    document.getElementById("formLoader").classList.add("hidden");
  }
}

// document.addEventListener("DOMContentLoaded", function () {
//   // Retrieve necessary elements by their IDs

// });

function validateForm() {
  let isValid = true;

  const firstName = document.getElementById("first_name");
  const lastName = document.getElementById("last_name");
  const email = document.getElementById("email");
  const zipCode = document.getElementById("home_zip_code");

  document.querySelectorAll(".error-message").forEach((el) => el.remove());

  if (!/^[a-zA-Z]+$/.test(firstName.value)) {
    showError(firstName, "First name can only contain letters");
    isValid = false;
  }

  if (!/^[a-zA-Z]+$/.test(lastName.value)) {
    showError(lastName, "Last name can only contain letters");
    isValid = false;
  }

  if (!/^[\w.-]+@[\w.-]+\.\w+$/.test(email.value)) {
    showError(email, "Enter a valid email address");
    isValid = false;
  }

  if (!/^\d{1,10}$/.test(zipCode.value)) {
    showError(zipCode, "Zip code must be up to 10 digit numbers");
    isValid = false;
  }

  return isValid;
}

function showError(input, message) {
  const error = document.createElement("p");
  error.className = "error-message text-red-500 text-sm mt-1";
  error.textContent = message;
  input.parentNode.appendChild(error);
}

async function createNewRep(event) {
  event.preventDefault();
  if (!validateForm()) return;

  try {
    document.getElementById("formLoader").classList.remove("hidden");
    const form = document.getElementById("repFormElement");
    const formData = new FormData(form);
    const formObject = {};
    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    // Create the rep via API
    const result = await createRepApiCall(formObject);
    console.log("Rep created successfully:", result);

    // Show success modal
    repModal();

    // Immediately refresh the rep list to show the newly added rep
    // This ensures the rep appears in the search results without waiting for the modal to close
    await refreshRepList();

    // For mobile view, we need an additional step to ensure the rep list is updated
    if (window.innerWidth < 768) {
      console.log("Mobile view detected, ensuring rep list is refreshed");
      // Force a reload of the active reps
      window.mobileViewNeedsRefresh = true;
    }
  } catch (err) {
    console.error("Error creating rep:", err);
    repModal(false, err);
  } finally {
    document.getElementById("formLoader").classList.add("hidden");
  }
}

const repModal = (success = true, data = {}) => {
  const repSuccessModal = document.getElementById("RepSuccessModal");
  const header = document.getElementById("header");
  const headerTitle = document.getElementById("headertitle");
  const desc = document.getElementById("desc");
  const modalOkBtn = document.getElementById("repModalOkBtn");
  if (success) {
    headerTitle.textContent = "REP added Successfully";
    desc.textContent = "The REP has been successfully added to the system.";
    header.classList.remove("bg-red-500");
    modalOkBtn.classList.remove("bg-red-500");
    header.classList.add("bg-green-500");
    modalOkBtn.classList.add("bg-green-500");
    repSuccessModal.classList.remove("hidden");
    modalOkBtn.addEventListener("click", async (event) => {
      event.preventDefault();
      try {
        document.getElementById("repFormElement").reset();
        document.getElementById("RepSuccessModal").classList.add("hidden");
        // Refresh the rep list to show the newly added rep
        await refreshRepList();
      } catch (err) {
        console.log(err);
      }
    });
    return;
  }
  // {"errorMessage":"User exists with same username"}
  headerTitle.textContent = "Error While Adding the REP";
  desc.textContent =
    data && data.message ? data.message : "Please Enter the valid data";
  header.classList.remove("bg-green-500");
  modalOkBtn.classList.remove("bg-green-500");
  header.classList.add("bg-red-500");
  modalOkBtn.classList.add("bg-red-500");
  repSuccessModal.classList.remove("hidden");
  modalOkBtn.addEventListener("click", (event) => {
    event.preventDefault();
    try {
      document.getElementById("RepSuccessModal").classList.add("hidden");
    } catch (err) {
      console.log(err);
    }
  });
  return;
};

function closeSuccessModal() {
  document.getElementById("repFormElement").reset();
  document.getElementById("RepSuccessModal").classList.add("hidden");
}

// Function to refresh the rep list after a new rep is created
async function refreshRepList() {
  try {
    // Show loader
    toggleContentLoader(true);

    // Fetch the updated list of reps
    const activeReps = await fetchActiveReps();

    // Update the rep list in the UI
    const techsNames = document.getElementById("techsNames");
    if (techsNames) {
      // Clear the search input to show all reps
      const searchInput = document.getElementById("search-input-skillset");
      if (searchInput) {
        searchInput.value = "";
      }

      // Update the rep list
      updateTechList("techsNames", "Clinical Specialist", activeReps);
    }

    // For mobile view, we need to force a reload of the settings tab
    // This ensures the mobile sidebar shows the updated rep list
    if (window.innerWidth < 768) {
      // If we're in mobile view, reload the settings tab
      console.log("Refreshing rep list in mobile view");

      // Force reload the settings tab content
      await loadTechSkillSet();

      // Make sure the sidebar is updated
      const sideMenu = document.getElementById("side-menu");
      if (sideMenu && !sideMenu.classList.contains("hidden")) {
        // If the sidebar is open, close and reopen it to refresh the content
        closesidebar();
        setTimeout(() => {
          opensidemenu();
        }, 100);
      }
    }
  } catch (error) {
    console.error("Error refreshing rep list:", error);
  } finally {
    // Hide loader
    toggleContentLoader(false);
  }
}

// Function to open rep form
async function openRepForm(event) {
  if (event) event.preventDefault();

  try {
    const skillSetArea = document.getElementById("repproviderSkillSet");
    skillSetArea.innerHTML = "";
    $(skillSetArea).load("repform.html", async () => {
      // Fetch procedure types and experience types
      const procedureType = await getprocedureType();
      const experienceType = await getExperienceType();

      // Populate dropdowns
      const procedureEle = document.getElementById("proceduretype");
      const experienceEle = document.getElementById("experience");

      // Clear existing options
      procedureEle.innerHTML = '<option value="">Select a procedure</option>';
      experienceEle.innerHTML = '<option value="">Select experience</option>';

      // Add procedure options
      procedureType.forEach((procedure) => {
        const option = document.createElement("option");
        option.value = procedure.id;
        option.textContent = procedure.name;
        procedureEle.appendChild(option);
      });

      // Add experience options
      experienceType.forEach((experience) => {
        const option = document.createElement("option");
        option.value = experience.id;
        option.textContent = experience.name;
        experienceEle.appendChild(option);
      });

      document.getElementById("formLoader").classList.add("hidden");

      // Add event listener to success modal OK button
      document
        .getElementById("repModalOkBtn")
        .addEventListener("click", closeSuccessModal);
    });
  } catch (err) {
    console.error(err);
    document.getElementById("formLoader").classList.add("hidden");
  }
}

// Function to initialize tab event listeners
function initializeTabEventListeners() {
  // Get all tabs (both mobile and desktop)
  const tabs = document.querySelectorAll("#settings-tabs button[data-tab]");

  // Add click event listeners to tabs
  tabs.forEach((tab) => {
    // Remove any existing event listeners by cloning and replacing
    const newTab = tab.cloneNode(true);
    tab.parentNode.replaceChild(newTab, tab);

    // Add new event listener
    newTab.addEventListener("click", () => {
      const tabValue = newTab.getAttribute("data-tab");
      switchTab(tabValue);
    });
  });

  // Initialize the Add Rep button
  const addRepButton = document.getElementById("add-rep-button");
  if (addRepButton) {
    // Remove any existing event listeners
    const newAddButton = addRepButton.cloneNode(true);
    addRepButton.parentNode.replaceChild(newAddButton, addRepButton);
    newAddButton.addEventListener("click", (e) => openRepForm(e));
  }

  // Make sure containers for site and physician lists exist
  if (!document.getElementById("siteNames")) {
    const sitesDiv = document.createElement("div");
    sitesDiv.id = "siteNames";
    sitesDiv.className = "space-y-2 mt-4 hidden";
    document.getElementById("techs").appendChild(sitesDiv);
  }

  if (!document.getElementById("physicianNames")) {
    const physiciansDiv = document.createElement("div");
    physiciansDiv.id = "physicianNames";
    physiciansDiv.className = "space-y-2 mt-4 hidden";
    document.getElementById("techs").appendChild(physiciansDiv);
  }

  // Make sure provider names container exists
  if (!document.getElementById("providerNames")) {
    const providersDiv = document.createElement("div");
    providersDiv.id = "providerNames";
    providersDiv.className = "space-y-2 mt-4";
    document.getElementById("techs").appendChild(providersDiv);
  }

  // Initialize the active tab
  switchTab(activeTab);
}

// Function to switch between tabs
async function switchTab(tabValue) {
  activeTab = tabValue;

  // Show content loader
  toggleContentLoader(true);

  // Update tab styling for both mobile and desktop tabs
  document
    .querySelectorAll("#settings-tabs button[data-tab]")
    .forEach((btn) => {
      if (btn.dataset.tab === tabValue) {
        btn.classList.remove(
          "bg-gray-100",
          "text-gray-700",
          "hover:bg-gray-200"
        );
        btn.classList.add("bg-[#8F58DD]", "text-white");
      } else {
        btn.classList.remove("bg-[#8F58DD]", "text-white");
        btn.classList.add("bg-gray-100", "text-gray-700", "hover:bg-gray-200");
      }
    });

  // Update button text and functionality
  const addButtonContainer = document.getElementById("add-button-container");
  if (addButtonContainer) {
    // Clear existing button
    addButtonContainer.innerHTML = "";

    // Create new button based on active tab
    const addButton = document.createElement("button");
    addButton.className = "bg-[#8F58DD] py-2 rounded-lg w-full";

    if (tabValue === "rep") {
      addButton.id = "add-rep-button";
      addButton.innerHTML = '<p class="text-white font-bold">Add Rep</p>';
      addButton.addEventListener("click", (e) => openRepForm(e));
    } else if (tabValue === "site") {
      addButton.id = "add-site-button";
      addButton.innerHTML = '<p class="text-white font-bold">Add Site</p>';
      addButton.addEventListener("click", (e) => openSiteForm(e));
    } else if (tabValue === "physician") {
      addButton.id = "add-physician-button";
      addButton.innerHTML =
        '<p class="text-white font-bold">Add Implanting Physician</p>';
      addButton.addEventListener("click", (e) => openPhysicianForm(e));
    }

    addButtonContainer.appendChild(addButton);
  }

  // Show/hide appropriate content
  const techsNames = document.getElementById("techsNames");
  const providerNames = document.getElementById("providerNames");
  const siteNames = document.getElementById("siteNames");
  const physicianNames = document.getElementById("physicianNames");

  // Hide all first
  [techsNames, providerNames, siteNames, physicianNames].forEach((el) => {
    if (el) el.classList.add("hidden");
  });

  // Show appropriate content based on active tab
  if (tabValue === "rep") {
    if (techsNames) techsNames.classList.remove("hidden");
    if (providerNames) providerNames.classList.remove("hidden");

    // Reset search placeholder
    const searchInput = document.getElementById("search-input-skillset");
    if (searchInput) searchInput.placeholder = "Search Reps";
  } else if (tabValue === "site") {
    if (siteNames) {
      siteNames.classList.remove("hidden");
      await loadSites();
    }

    // Update search placeholder
    const searchInput = document.getElementById("search-input-skillset");
    if (searchInput) searchInput.placeholder = "Search Sites";
  } else if (tabValue === "physician") {
    if (physicianNames) {
      physicianNames.classList.remove("hidden");
      await loadPhysicians();
    }

    // Update search placeholder
    const searchInput = document.getElementById("search-input-skillset");
    if (searchInput) searchInput.placeholder = "Search Physicians";
  }

  // Clear the skill set display area
  const skillSetArea = document.getElementById("repproviderSkillSet");
  if (skillSetArea) {
    skillSetArea.innerHTML = `
      <div class="flex h-full w-full justify-center items-center">
        <p class="font-semibold text-[#8F58DD]">
          Click to view details...
        </p>
      </div>
    `;
  }

  // Hide content loader
  toggleContentLoader(false);
}

// Function to load sites
async function loadSites() {
  const siteNames = document.getElementById("siteNames");
  if (!siteNames) return;

  try {
    toggleContentLoader(true);

    // Fetch sites
    const sites = await getAllSites();

    // Update search functionality for sites
    const searchInput = document.getElementById("search-input-skillset");
    if (searchInput) {
      // Remove any existing event listeners
      const newSearchInput = searchInput.cloneNode(true);
      searchInput.parentNode.replaceChild(newSearchInput, searchInput);

      // Add new event listener
      newSearchInput.addEventListener("input", () => {
        const searchTerm = newSearchInput.value.toLowerCase();
        updateSiteList(sites, searchTerm);
      });

      // Add clear button functionality
      const clearButton = document.getElementById("clear-search");
      if (clearButton) {
        clearButton.addEventListener("click", () => {
          newSearchInput.value = "";
          updateSiteList(sites, "");
        });
      }
    }

    // Initial load of sites
    updateSiteList(sites, "");
  } catch (error) {
    console.error("Error loading sites:", error);
    showPopup("", "Error loading sites", "bg-red-600");
  } finally {
    toggleContentLoader(false);
  }
}

// Function to update site list based on search
function updateSiteList(sites, searchTerm = "") {
  const siteNames = document.getElementById("siteNames");
  if (!siteNames) return;

  // Filter sites based on search term
  const filteredSites = sites.filter((site) =>
    site.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort sites alphabetically
  const sortedSites = [...filteredSites].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  // Create HTML for sites
  let sitesHTML = `
    <div class="flex flex-col">
      <div class="bg-[#E7E7E7] rounded-lg">
        <div class="flex flex-row justify-between m-3">
          <h3 class="text-sm md:text-lg font-medium md:font-semibold text-center mb-2 text-black">Sites</h3>
          <img src="/svg/arrow.svg" alt="image" class="toggle-button-Sites cursor-pointer rotate-image" data-section-id="Sites" data-section-name="sites-data-div">
        </div>
      </div>
      <div id="sites-data-div" class="space-y-2 section-content my-2" data-section-id="Sites">
  `;

  if (sortedSites.length === 0) {
    sitesHTML += `<div class="text-primary p-2 text-center">No Match Found</div>`;
  } else {
    sortedSites.forEach((site) => {
      sitesHTML += `
        <hr class="border"></hr>
        <div class="site-name relative transition-all duration-300 ease-in-out
          hover:bg-gray-200 hover:text-black text-black p-2 rounded cursor-pointer"
          data-site-name="${site.name}" role="Site"
          data-id="${site.id}">
          <div class="text-container">
            <div class="font-medium md:font-semibold">${site.name}</div>
            <div class="text-xs text-gray-600">${site.city || ""} ${
        site.state || ""
      }</div>
          </div>
        </div>
      `;
    });
  }

  sitesHTML += `</div></div>`;

  // Update the DOM
  siteNames.innerHTML = sitesHTML;

  // Add click event listeners to site names
  document.querySelectorAll(".site-name").forEach((siteElement) => {
    siteElement.addEventListener("click", async (event) => {
      event.preventDefault();
      event.stopImmediatePropagation();

      // Show content loader
      toggleContentLoader(true);

      const siteId = siteElement.getAttribute("data-id");
      const site = sites.find((s) => s.id == siteId);

      if (site) {
        try {
          // Directly open the edit form for the site
          await openSiteForm(null, site);

          // Ensure the content loader is hidden after form is loaded
          setTimeout(() => {
            toggleContentLoader(false);
          }, 500); // Add a small delay to ensure the form is loaded

          // Highlight selected site
          document.querySelectorAll(".site-name").forEach((el) => {
            el.classList.remove("bg-gray-300");
          });
          siteElement.classList.add("bg-gray-300");

          // Store the selected site ID
          const siteId = siteElement.getAttribute("data-id");
          setCurrentSelections(siteId, null);
          console.log(`Site selected: ${siteId}`);
        } catch (error) {
          console.error("Error opening site form:", error);
          // Hide content loader in case of error
          toggleContentLoader(false);
        }
      } else {
        // Hide content loader if site not found
        toggleContentLoader(false);
      }
    });
  });

  // Add toggle functionality
  const toggleButtons = document.querySelectorAll(".toggle-button-Sites");
  toggleButtons.forEach((button) => {
    button.addEventListener("click", handleToggleClick);
  });
}

// Function to open site form
async function openSiteForm(event, site = null) {
  if (event) event.preventDefault();

  try {
    // Show content loader
    toggleContentLoader(true);

    const skillSetArea = document.getElementById("repproviderSkillSet");
    skillSetArea.innerHTML = "";
    // Add multiple timeouts to ensure the loader is hidden
    const loaderTimeouts = [];

    // First timeout - quick check after 500ms
    loaderTimeouts.push(
      setTimeout(() => {
        toggleContentLoader(false);
      }, 500)
    );

    // Second timeout - backup check after 2 seconds
    loaderTimeouts.push(
      setTimeout(() => {
        toggleContentLoader(false);
      }, 2000)
    );

    // Third timeout - final check after 5 seconds
    loaderTimeouts.push(
      setTimeout(() => {
        toggleContentLoader(false);
      }, 5000)
    );

    $(skillSetArea).load("siteform.html", async () => {
      // Clear all timeouts since the load completed
      loaderTimeouts.forEach(clearTimeout);
      // If editing an existing site, populate the form
      if (site) {
        document.querySelector("#siteFormElement h1").textContent = "Edit Site";
        document.querySelector(
          '#siteFormElement button[type="submit"]'
        ).textContent = "Save Changes";

        // Populate form fields
        document.getElementById("name").value = site.name || "";
        document.getElementById("address").value = site.address || "";
        document.getElementById("city").value = site.city || "";
        document.getElementById("state").value = site.state || "";
        document.getElementById("zip_code").value = site.zip_code || "";

        // Set account_id field
        const accountIdField = document.getElementById("account_id");
        if (accountIdField) {
          // Use the 'account' property from the site data
          accountIdField.value = site.account || "";
        } else {
          console.log("Account ID field not found in the form");
        }

        // Add a hidden field for the site ID
        let idField = document.querySelector(
          '#siteFormElement input[name="id"]'
        );
        if (!idField) {
          idField = document.createElement("input");
          idField.type = "hidden";
          idField.name = "id";
          document.getElementById("siteFormElement").appendChild(idField);
        }
        idField.value = site.id;

        // Change form submission handler
        const form = document.getElementById("siteFormElement");
        form.onsubmit = (e) => updateExistingSite(e, site.id);
      } else {
        // New site form
        document.querySelector("#siteFormElement h1").textContent =
          "Add New Site";
        document.querySelector(
          '#siteFormElement button[type="submit"]'
        ).textContent = "Create Site";
      }

      toggleContentLoader(false);

      // Add event listener to success modal OK button
      document
        .getElementById("siteModalOkBtn")
        .addEventListener("click", closeSiteSuccessModal);
    });
  } catch (err) {
    console.error("Error in openSiteForm:", err);

    toggleContentLoader(false);
  }
}

// Function to open physician form
async function openPhysicianForm(event, physician = null) {
  if (event) event.preventDefault();

  try {
    const skillSetArea = document.getElementById("repproviderSkillSet");
    skillSetArea.innerHTML = "";
    $(skillSetArea).load("physicianform.html", async () => {
      // Fetch procedure types and experience types
      const procedureType = await getprocedureType();
      const experienceType = await getExperienceType();
      const sites = await getAllSites();

      // Populate dropdowns
      const procedureEle = document.getElementById("proceduretype");
      const experienceEle = document.getElementById("experience");
      // Site dropdown has been removed
      // const siteEle = document.getElementById("physician_site_id");

      // Clear existing options
      procedureEle.innerHTML = '<option value="">Select a procedure</option>';
      experienceEle.innerHTML = '<option value="">Select experience</option>';
      siteEle.innerHTML = '<option value="">Select a site</option>';

      // Add procedure options
      procedureType.forEach((procedure) => {
        const option = document.createElement("option");
        option.value = procedure.id;
        option.textContent = procedure.name;
        procedureEle.appendChild(option);
      });

      // Add experience options
      experienceType.forEach((experience) => {
        const option = document.createElement("option");
        option.value = experience.id;
        option.textContent = experience.name;
        experienceEle.appendChild(option);
      });

      // Add site options
      sites.forEach((site) => {
        const option = document.createElement("option");
        option.value = site.id;
        option.textContent = site.name;
        siteEle.appendChild(option);
      });

      // If editing an existing physician, populate the form
      if (physician) {
        document.querySelector("#physicianFormElement h1").textContent =
          "Edit Implanting Physician";
        document.querySelector(
          '#physicianFormElement button[type="submit"]'
        ).textContent = "Save Changes";

        // Populate form fields
        document.getElementById("first_name").value =
          physician.first_name || "";
        document.getElementById("last_name").value = physician.last_name || "";
        document.getElementById("middle_name").value =
          physician.middle_name || "";
        document.getElementById("credential").value =
          physician.credential || "";
        document.getElementById("npi_number").value =
          physician.npi_number || "";
        document.getElementById("email_id").value = physician.email_id || "";
        document.getElementById("phone_number").value =
          physician.phone_number || "";
        document.getElementById("fax_number").value =
          physician.fax_number || "";
        document.getElementById("address").value = physician.address || "";
        document.getElementById("city").value = physician.city || "";
        document.getElementById("state").value = physician.state || "";
        document.getElementById("zip_code").value = physician.zip_code || "";

        // Set selected options
        // Site dropdown has been removed from the form
        // if (physician.site_id) {
        //   document.getElementById("physician_site_id").value = physician.site_id;
        // }

        if (physician.procedure_type_id) {
          document.getElementById("proceduretype").value =
            physician.procedure_type_id;
        }

        if (physician.experience_id) {
          document.getElementById("experience").value = physician.experience_id;
        }

        // Add a hidden field for the physician ID
        let idField = document.querySelector(
          '#physicianFormElement input[name="id"]'
        );
        if (!idField) {
          idField = document.createElement("input");
          idField.type = "hidden";
          idField.name = "id";
          document.getElementById("physicianFormElement").appendChild(idField);
        }
        idField.value = physician.id;

        // Change form submission handler
        const form = document.getElementById("physicianFormElement");
        form.onsubmit = (e) => updateExistingPhysician(e, physician.id);
      } else {
        // New physician form
        document.querySelector("#physicianFormElement h1").textContent =
          "Add New Implanting Physician";
        document.querySelector(
          '#physicianFormElement button[type="submit"]'
        ).textContent = "Create Physician";
      }

      document.getElementById("formLoader").classList.add("hidden");

      // Add event listener to success modal OK button
      document
        .getElementById("physicianModalOkBtn")
        .addEventListener("click", closePhysicianSuccessModal);
    });
  } catch (err) {
    console.error(err);
    document.getElementById("formLoader").classList.add("hidden");
  }
}

// Function to display site details - Removed in favor of direct editing

// Function to load physicians
async function loadPhysicians() {
  const physicianNames = document.getElementById("physicianNames");
  if (!physicianNames) return;

  try {
    toggleContentLoader(true);

    // Fetch sites first
    const sites = await getAllSites();

    // Sort sites alphabetically
    const sortedSites = [...sites].sort((a, b) => a.name.localeCompare(b.name));

    // Create site dropdown HTML
    let siteDropdownHTML = `
      <div class="mb-4 bg-white p-4 rounded-lg shadow-sm">
        <label class="block text-sm font-medium text-gray-700 mb-2">Select Site</label>
        <select id="physician-site-filter" class="w-full p-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
          <option value="">Select a Site</option>
    `;

    sortedSites.forEach((site) => {
      siteDropdownHTML += `<option value="${site.id}">${site.name}</option>`;
    });

    siteDropdownHTML += `</select></div>`;

    // Add site dropdown to physician names container
    physicianNames.innerHTML =
      siteDropdownHTML +
      `<div id="physicians-list" class="mt-4">
        <div class="text-center p-4 text-gray-600">Please select a site to view physicians</div>
      </div>`;

    // Add event listener to site dropdown
    const siteFilter = document.getElementById("physician-site-filter");
    if (siteFilter) {
      siteFilter.addEventListener("change", async () => {
        const selectedSiteId = siteFilter.value;
        if (selectedSiteId) {
          toggleContentLoader(true);
          await loadPhysiciansBySite(selectedSiteId);
          toggleContentLoader(false);
        } else {
          // Clear the physicians list if no site is selected
          const physiciansListDiv = document.getElementById("physicians-list");
          if (physiciansListDiv) {
            physiciansListDiv.innerHTML = `<div class="text-center p-4 text-gray-600">Please select a site to view physicians</div>`;
          }
        }
      });
    }

    // Don't load physicians initially - wait for site selection

    // Update search functionality for physicians
    const searchInput = document.getElementById("search-input-skillset");
    if (searchInput) {
      // Remove any existing event listeners
      const newSearchInput = searchInput.cloneNode(true);
      searchInput.parentNode.replaceChild(newSearchInput, searchInput);

      // Add new event listener
      newSearchInput.addEventListener("input", () => {
        const searchTerm = newSearchInput.value.toLowerCase();
        const selectedSiteId =
          document.getElementById("physician-site-filter")?.value || "";
        filterPhysicians(searchTerm, selectedSiteId);
      });

      // Add clear button functionality
      const clearButton = document.getElementById("clear-search");
      if (clearButton) {
        clearButton.addEventListener("click", () => {
          newSearchInput.value = "";
          const selectedSiteId =
            document.getElementById("physician-site-filter")?.value || "";
          filterPhysicians("", selectedSiteId);
        });
      }
    }
  } catch (error) {
    console.error("Error loading physicians:", error);
    showPopup("", "Error loading physicians", "bg-red-600");
  } finally {
    toggleContentLoader(false);
  }
}

// Global variable to store all physicians
let allPhysicians = [];

// Function to create a new site
async function createNewSite(event) {
  event.preventDefault();

  try {
    document.getElementById("formLoader").classList.remove("hidden");

    const form = document.getElementById("siteFormElement");
    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    const result = await createSite(formObject);

    if (result.status === "success") {
      siteModal(true, { name: formObject.name });
      // Reload sites after successful creation
      await loadSites();
    } else {
      siteModal(false, result);
    }
  } catch (err) {
    console.error(err);
    siteModal(false, err);
  } finally {
    document.getElementById("formLoader").classList.add("hidden");
  }
}

// Function to validate site form fields
function validateSiteField(input, type) {
  validateField(input, type);
}

// Function to validate physician form fields
function validatePhysicianField(input, type) {
  validateField(input, type);
}

// Generic field validation function
function validateField(input, type) {
  const value = input.value;

  // Define validation callback
  const updateValue = (newValue) => {
    input.value = newValue;
  };

  // Load the utils.js script if it's not already loaded
  if (!window.utilsLoaded) {
    // Create a script element
    const script = document.createElement("script");
    script.type = "module";
    script.src = "/abstractor/utils.js";
    script.onload = () => {
      window.utilsLoaded = true;
    };
    script.onerror = (error) => {
      console.error("Error loading utils.js:", error);
    };
    document.head.appendChild(script);
  }

  // Apply appropriate validation based on field type
  if (type === "char") {
    try {
      // Try to use the imported parseCharInput function
      if (window.parseCharInput) {
        window.parseCharInput(value, updateValue);
      } else {
        // Fallback to direct import
        import("/abstractor/utils.js")
          .then((module) => {
            // Save the function for future use
            window.parseCharInput = module.parseCharInput;
            module.parseCharInput(value, updateValue);
          })
          .catch((error) => {
            console.error("Error importing utils.js:", error);
            // Fallback validation if import fails
            const validInput = /^[a-zA-Z\s]*$/; // Allow letters and spaces
            if (!validInput.test(value)) {
              input.value = value.replace(/[^a-zA-Z\s]/g, "");
            }
          });
      }
    } catch (error) {
      console.error("Error in char validation:", error);
      // Fallback validation
      const validInput = /^[a-zA-Z\s]*$/; // Allow letters and spaces
      if (!validInput.test(value)) {
        input.value = value.replace(/[^a-zA-Z\s]/g, "");
      }
    }
  } else if (type === "int") {
    try {
      // Try to use the imported parseIntInput function
      if (window.parseIntInput) {
        window.parseIntInput(value, updateValue);
      } else {
        // Fallback to direct import
        import("/abstractor/utils.js")
          .then((module) => {
            // Save the function for future use
            window.parseIntInput = module.parseIntInput;
            module.parseIntInput(value, updateValue);
          })
          .catch((error) => {
            console.error("Error importing utils.js:", error);
            // Fallback validation if import fails
            input.value = value.replace(/[^0-9]/g, "");
          });
      }
    } catch (error) {
      console.error("Error in int validation:", error);
      // Fallback validation
      input.value = value.replace(/[^0-9]/g, "");
    }
  }
}

// Function to update an existing site
async function updateExistingSite(event, siteId) {
  event.preventDefault();

  try {
    // Show the loader
    toggleLoader(document.getElementById("loader"), true);

    const form = document.getElementById("siteFormElement");
    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    // Make sure the phone field is included if it exists
    if (phoneField) {
      if (!formObject.phone) {
        console.log("Phone field missing from FormData, adding it manually");
        formObject.phone = phoneField.value;
      }
    } else {
      console.log(
        "Phone field not found in the form, not including in request"
      );
    }

    // Log the final object being sent
    console.log(
      "Final form object being sent to API:",
      JSON.stringify(formObject, null, 2)
    );

    // Log the API endpoint and method
    console.log(`API call: PUT /sites/${siteId}`);

    const result = await updateSite(siteId, formObject);
    console.log("API response:", JSON.stringify(result, null, 2));

    if (result.status === "success") {
      // Store the updated site data for reference
      const updatedSite = {
        ...formObject,
        id: siteId,
      };
      // Use sessionStorage instead of a global variable
      sessionStorage.setItem("lastUpdatedSite", JSON.stringify(updatedSite));

      siteModal(true, { name: formObject.name, isUpdate: true });
      // Reload sites after successful update
      await loadSites();
    } else {
      siteModal(false, result);
    }
  } catch (err) {
    console.error(err);
    siteModal(false, err);
  } finally {
    // Hide the loader
    toggleLoader(document.getElementById("loader"), false);
  }
}

// Function to create a new physician
async function createNewPhysician(event) {
  event.preventDefault();

  try {
    document.getElementById("formLoader").classList.remove("hidden");

    const form = document.getElementById("physicianFormElement");
    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    const result = await createImplantingPhysician(formObject);

    if (result.status === "success") {
      physicianModal(true, {
        name: `${formObject.first_name} ${formObject.last_name}`,
      });
      // Reload physicians after successful creation
      await loadPhysicians();
    } else {
      physicianModal(false, result);
    }
  } catch (err) {
    console.error(err);
    physicianModal(false, err);
  } finally {
    document.getElementById("formLoader").classList.add("hidden");
  }
}

// Function to update an existing physician
async function updateExistingPhysician(event, physicianId) {
  event.preventDefault();

  try {
    document.getElementById("formLoader").classList.remove("hidden");

    const form = document.getElementById("physicianFormElement");
    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    const result = await updateImplantingPhysician(physicianId, formObject);

    if (result.status === "success") {
      physicianModal(true, {
        name: `${formObject.first_name} ${formObject.last_name}`,
        isUpdate: true,
      });
      // Reload physicians after successful update
      await loadPhysicians();
    } else {
      physicianModal(false, result);
    }
  } catch (err) {
    console.error(err);
    physicianModal(false, err);
  } finally {
    document.getElementById("formLoader").classList.add("hidden");
  }
}

// Function to show site success/error modal
function siteModal(success = true, data = {}) {
  const modal = document.getElementById("SiteSuccessModal");
  const header = modal.querySelector("#header");
  const headerTitle = modal.querySelector("#headertitle");
  const desc = modal.querySelector("#desc");

  if (success) {
    header.className =
      "flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = data.isUpdate ? "Site Updated" : "Site Created";
    desc.textContent = data.isUpdate
      ? `Site "${data.name}" has been updated successfully.`
      : `Site "${data.name}" has been created successfully.`;
  } else {
    header.className =
      "flex items-center justify-between bg-red-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = "Error";
    desc.textContent =
      data.message || "An error occurred while processing your request.";
  }

  // Make sure the modal is visible
  modal.classList.remove("hidden");
  modal.style.display = "flex";
}

// Function to show physician success/error modal
function physicianModal(success = true, data = {}) {
  const modal = document.getElementById("PhysicianSuccessModal");
  const header = modal.querySelector("#header");
  const headerTitle = modal.querySelector("#headertitle");
  const desc = modal.querySelector("#desc");

  if (success) {
    header.className =
      "flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = data.isUpdate
      ? "Physician Updated"
      : "Physician Created";
    desc.textContent = data.isUpdate
      ? `Dr. ${data.name} has been updated successfully.`
      : `Dr. ${data.name} has been created successfully.`;
  } else {
    header.className =
      "flex items-center justify-between bg-red-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = "Error";
    desc.textContent =
      data.message || "An error occurred while processing your request.";
  }

  modal.classList.remove("hidden");
}

// Function to close site success modal
function closeSiteSuccessModal() {
  // Don't reset the form, just hide the modal
  document.getElementById("SiteSuccessModal").classList.add("hidden");

  // If we have a site ID, reload that site's data
  const siteIdField = document.getElementById("site_id");
  if (siteIdField && siteIdField.value) {
    const siteId = siteIdField.value;
    // Find the site in the list and simulate a click to reload it
    const siteElement = document.querySelector(
      `.site-name[data-id="${siteId}"]`
    );
    if (siteElement) {
      siteElement.click();
    }
  } else {
    // If it's a new site, reload the sites list
    loadSites();
  }
}

// Function to close physician success modal
function closePhysicianSuccessModal() {
  // Don't reset the form, just hide the modal
  document.getElementById("PhysicianSuccessModal").classList.add("hidden");
  document.getElementById("PhysicianSuccessModal").style.display = "none";

  // Get the site ID from multiple possible sources
  let siteId = null;

  // 1. First check if there's a currently selected site in the left panel
  const selectedSiteElement = document.querySelector(".site-name.bg-gray-300");
  if (selectedSiteElement) {
    siteId = selectedSiteElement.getAttribute("data-id");
  }

  // 2. If not found, try to get site_id from the site filter dropdown
  if (!siteId) {
    const siteFilter = document.getElementById("physician-site-filter");
    if (siteFilter && siteFilter.value) {
      siteId = siteFilter.value;
    }
  }

  // 3. If not found, try to get site_id from a hidden input in the form
  if (!siteId) {
    const siteIdHiddenInput = document.querySelector(
      '#physicianFormElement input[name="site_id"]'
    );
    if (siteIdHiddenInput && siteIdHiddenInput.value) {
      siteId = siteIdHiddenInput.value;
    }
  }

  // 4. If not found, try to get from sessionStorage
  if (!siteId) {
    const storedSiteId = sessionStorage.getItem("currentSiteId");
    if (storedSiteId) {
      siteId = storedSiteId;
    }
  }

  // 5. If still not found, try to get from URL parameters
  if (!siteId) {
    const urlParams = new URLSearchParams(window.location.search);
    const siteIdFromUrl = urlParams.get("site_id");
    if (siteIdFromUrl) {
      siteId = siteIdFromUrl;
    }
  }

  // If we have a physician ID, try to reload that physician's data
  const physicianIdField = document.querySelector(
    '#physicianFormElement input[name="id"]'
  );
  if (physicianIdField && physicianIdField.value) {
    const physicianId = physicianIdField.value;

    // Find the physician in the list and simulate a click to reload it
    const physicianElement = document.querySelector(
      `.physician-name[data-id="${physicianId}"]`
    );
    if (physicianElement) {
      physicianElement.click();
      return; // Exit early if we successfully clicked the physician element
    }
  }

  // If we couldn't find or click the physician element, use the site ID if we have one
  if (siteId && siteId !== "undefined" && siteId !== "null") {
    // Store the site ID for future use
    sessionStorage.setItem("currentSiteId", siteId);

    // Set the site filter dropdown to the selected site
    const siteFilter = document.getElementById("physician-site-filter");
    if (siteFilter) {
      siteFilter.value = siteId;
    }

    // Highlight the site in the left panel
    const siteElement = document.querySelector(
      `.site-name[data-id="${siteId}"]`
    );
    if (siteElement) {
      // Remove highlight from all sites
      document.querySelectorAll(".site-name").forEach((el) => {
        el.classList.remove("bg-gray-300");
      });
      // Highlight this site
      siteElement.classList.add("bg-gray-300");

      // Scroll the site into view if needed
      siteElement.scrollIntoView({ behavior: "smooth", block: "nearest" });
    } else {
      console.log(`Site element not found in left panel for ID: ${siteId}`);
    }

    // Load physicians for this site
    loadPhysiciansBySite(siteId);
  } else {
    loadPhysicians();
  }
}

// Function to load physicians by site
async function loadPhysiciansBySite(siteId) {
  const physiciansListDiv = document.getElementById("physicians-list");
  if (!physiciansListDiv) return;

  // Validate the site ID
  if (!siteId || siteId === "undefined" || siteId === "null") {
    console.error(
      `Invalid site ID provided to loadPhysiciansBySite: ${siteId}`
    );
    physiciansListDiv.innerHTML = `<div class="text-center p-4 text-gray-600">Cannot load physicians: Invalid site ID.</div>`;
    toggleContentLoader(false);
    return;
  }

  try {
    toggleContentLoader(true);

    // Set the site filter dropdown to the selected site
    const siteFilter = document.getElementById("physician-site-filter");
    if (siteFilter) {
      siteFilter.value = siteId;
    }

    let physicians = [];

    if (siteId) {
      try {
        // Fetch physicians for specific site
        physicians = await fetchImplantingPhysicians(siteId);

        // Process the physician data based on the API response format
        if (physicians && Array.isArray(physicians)) {
          // Add site info to each physician
          const sites = await getAllSites();
          const site = sites.find((s) => s.id == siteId);

          if (site) {
            // Transform the physician data to match the expected format
            physicians = physicians.map((physician) => {
              // Check if the physician data is in the format with 'name' property
              if (physician.name && typeof physician.name === "string") {
                // Parse the name into first_name and last_name
                const nameParts = physician.name.split(",");
                const lastName = nameParts[0] ? nameParts[0].trim() : "";

                let firstName = "";
                let middleName = "";
                let suffix = "";

                if (nameParts.length > 1) {
                  // Handle potential suffixes like MD, DO, etc.
                  const firstParts = nameParts[1].trim().split(" ");
                  firstName = firstParts[0] || "";

                  // Check if there's a suffix (MD, DO, etc.)
                  if (firstParts.length > 1) {
                    const lastPart = firstParts[firstParts.length - 1];
                    if (["MD", "DO", "PhD", "PA", "NP"].includes(lastPart)) {
                      suffix = lastPart;
                      // If there are parts between first name and suffix, that's the middle name
                      if (firstParts.length > 2) {
                        middleName = firstParts.slice(1, -1).join(" ");
                      }
                    } else {
                      // If no recognized suffix, assume it's part of the name
                      middleName = firstParts.slice(1).join(" ");
                    }
                  }
                }

                return {
                  ...physician,
                  first_name: firstName,
                  last_name: lastName,
                  middle_name: middleName,
                  credential: suffix,
                  site_name: site.name,
                  site_id: site.id,
                };
              } else {
                // If the data is already in the expected format, just add site info
                return {
                  ...physician,
                  site_name: site.name,
                  site_id: site.id,
                };
              }
            });
          }
        } else {
          // If physicians is not an array, initialize it as an empty array
          physicians = [];
          console.error("Unexpected physician data format:", physicians);
        }
      } catch (fetchError) {
        console.error("Error fetching physicians for site:", fetchError);
        // Show a more specific error message
        showPopup(
          "",
          `Error loading physicians for the selected site. Please try again.`,
          "bg-red-600"
        );
        // Return empty array but don't throw error to allow UI to update
        physicians = [];
      }
    } else {
      // Only fetch the sites once
      const sites = await getAllSites();

      try {
        // Use Promise.all to fetch all physicians in parallel
        const physicianPromises = sites.map(async (site) => {
          try {
            const sitePhysicians = await fetchImplantingPhysicians(site.id);
            // Add site info to each physician
            sitePhysicians.forEach((physician) => {
              physician.site_name = site.name;
              physician.site_id = site.id;
            });
            return sitePhysicians;
          } catch (siteError) {
            console.error(
              `Error fetching physicians for site ${site.id}:`,
              siteError
            );
            return []; // Return empty array for this site
          }
        });

        // Wait for all promises to resolve
        const physicianArrays = await Promise.all(physicianPromises);

        // Flatten the array of arrays into a single array
        physicians = physicianArrays.flat();
      } catch (allSitesError) {
        console.error(
          "Error fetching physicians for all sites:",
          allSitesError
        );
        physicians = [];
      }
    }

    // Store all physicians globally
    allPhysicians = physicians;

    // Update the UI
    updatePhysiciansList(physicians);
  } catch (error) {
    console.error("Error loading physicians by site:", error);
    showPopup("", "Error loading physicians", "bg-red-600");
  } finally {
    toggleContentLoader(false);
  }
}

// Function to filter physicians based on search term
function filterPhysicians(searchTerm, siteId) {
  let filteredPhysicians = [...allPhysicians];

  // Filter by site if specified
  if (siteId) {
    filteredPhysicians = filteredPhysicians.filter((p) => p.site_id == siteId);
  }

  // Filter by search term
  if (searchTerm) {
    filteredPhysicians = filteredPhysicians.filter((p) => {
      const fullName = `${p.first_name} ${p.last_name}`.toLowerCase();
      return fullName.includes(searchTerm);
    });
  }

  // Update the UI
  updatePhysiciansList(filteredPhysicians);
}

// Function to update physicians list in the UI
function updatePhysiciansList(physicians) {
  const physiciansListDiv = document.getElementById("physicians-list");
  if (!physiciansListDiv) return;

  // Group physicians by site
  const physiciansBySite = {};

  // Check if physicians array is empty or undefined
  if (!physicians || physicians.length === 0) {
    // Return early with a message
    physiciansListDiv.innerHTML = `<div class="text-center p-4 text-gray-600">No physicians found for the selected site.</div>`;
    return;
  }

  physicians.forEach((physician) => {
    const siteName = physician.site_name || "Unknown Site";
    const siteId = physician.site_id || "unknown";

    if (!physiciansBySite[siteId]) {
      physiciansBySite[siteId] = {
        name: siteName,
        physicians: [],
      };
    }

    physiciansBySite[siteId].physicians.push(physician);
  });

  // Create HTML for physicians grouped by site
  let physiciansHTML = "";

  // Sort sites alphabetically by name
  const sortedSites = Object.entries(physiciansBySite).sort((a, b) =>
    a[1].name.localeCompare(b[1].name)
  );

  sortedSites.forEach(([siteId, siteData]) => {
    physiciansHTML += `
      <div class="flex flex-col mb-4">
        <div class="bg-[#E7E7E7] rounded-lg">
          <div class="flex flex-row justify-between m-3">
            <h3 class="text-sm md:text-lg font-medium md:font-semibold text-center mb-2 text-black">${siteData.name}</h3>
            <img src="/svg/arrow.svg" alt="image" class="toggle-button-${siteId} cursor-pointer rotate-image" data-section-id="${siteId}" data-section-name="physician-site-${siteId}">
          </div>
        </div>
        <div id="physician-site-${siteId}" class="space-y-2 section-content my-2" data-section-id="${siteId}">
    `;

    if (siteData.physicians.length === 0) {
      physiciansHTML += `<div class="text-primary p-2">No physicians found</div>`;
    } else {
      // Sort physicians alphabetically by last name or full name
      const sortedPhysicians = [...siteData.physicians].sort((a, b) => {
        // If both have last_name, use that for sorting
        if (a.last_name && b.last_name) {
          return a.last_name.localeCompare(b.last_name);
        }
        // If only one has last_name, put the one with last_name first
        if (a.last_name && !b.last_name) return -1;
        if (!a.last_name && b.last_name) return 1;

        // If neither has last_name but both have name, use name
        if (a.name && b.name) {
          return a.name.localeCompare(b.name);
        }

        // Default case
        return 0;
      });

      sortedPhysicians.forEach((physician) => {
        // Determine how to display the physician name based on available data
        let displayName = "";

        if (physician.first_name && physician.last_name) {
          displayName = `Dr. ${physician.first_name} ${physician.last_name}`;
        } else if (physician.name) {
          // If we have a full name string but not parsed parts
          displayName = physician.name.includes("Dr.")
            ? physician.name
            : `Dr. ${physician.name}`;
        } else {
          // Fallback
          displayName = "Unknown Physician";
        }

        physiciansHTML += `
          <hr class="border"></hr>
          <div class="physician-name relative transition-all duration-300 ease-in-out
            hover:bg-gray-200 hover:text-black text-black p-2 rounded cursor-pointer"
            data-physician-name="${physician.first_name || ""} ${
          physician.last_name || ""
        }"
            data-site-id="${siteId}"
            data-id="${physician.id}">
            <div class="text-container">
              <div class="font-medium md:font-semibold">${displayName}</div>
              <div class="text-xs text-gray-600">${
                physician.procedure_type_name ||
                physician.credential ||
                "No procedure type"
              }</div>
            </div>
          </div>
        `;
      });
    }

    physiciansHTML += `</div></div>`;
  });

  if (sortedSites.length === 0) {
    physiciansHTML = `<div class="text-primary p-4 text-center">No physicians found</div>`;
  }

  // Update the DOM
  physiciansListDiv.innerHTML = physiciansHTML;

  // Add click event listeners to physician names
  document.querySelectorAll(".physician-name").forEach((physicianElement) => {
    physicianElement.addEventListener("click", async (event) => {
      event.preventDefault();
      event.stopImmediatePropagation();

      // Show loader
      toggleContentLoader(true);

      const physicianId = physicianElement.getAttribute("data-id");

      // Validate that we have a valid physician ID
      if (!physicianId || physicianId === "undefined") {
        console.error("Invalid physician ID:", physicianId);
        toggleContentLoader(false);
        return;
      }

      try {
        // Use the new fetchSpecificImplantingPhysician function to get detailed data
        const physician = await fetchSpecificImplantingPhysician(physicianId);

        if (physician) {
          // Directly open the edit form for the physician
          await openPhysicianForm(null, physician);

          // Ensure the loader is hidden after form is loaded
          setTimeout(() => {
            toggleContentLoader(false);
          }, 500); // Add a small delay to ensure the form is loaded

          // Highlight selected physician
          document.querySelectorAll(".physician-name").forEach((el) => {
            el.classList.remove("bg-gray-300");
          });
          physicianElement.classList.add("bg-gray-300");

          // Store the selected physician ID and site ID
          const physicianId = physicianElement.getAttribute("data-id");
          const siteId = physicianElement.getAttribute("data-site-id");
          setCurrentSelections(siteId, physicianId);
          console.log(`Physician selected: ${physicianId}, Site: ${siteId}`);
        } else {
          console.error("No physician data returned from API");
          toggleContentLoader(false);
        }
      } catch (error) {
        console.error("Error fetching or opening physician form:", error);
        // Hide loader in case of error
        toggleContentLoader(false);
      }
    });
  });

  // Add toggle functionality for each site section
  sortedSites.forEach(([siteId]) => {
    const toggleButtons = document.querySelectorAll(`.toggle-button-${siteId}`);
    toggleButtons.forEach((button) => {
      button.addEventListener("click", handleToggleClick);
    });
  });
}

// Function to display physician details - Removed in favor of direct editing

// Function to open site form
async function openSiteForm(event, site = null) {
  if (event) event.preventDefault();

  try {
    const skillSetArea = document.getElementById("repproviderSkillSet");
    skillSetArea.innerHTML = "";
    $(skillSetArea).load("siteform.html", async () => {
      // If editing an existing site, populate the form
      if (site) {
        document.querySelector("#siteFormElement h1").textContent = "Edit Site";
        document.querySelector(
          '#siteFormElement button[type="submit"]'
        ).textContent = "Update";

        // Populate form fields
        document.getElementById("name").value = site.name || "";
        document.getElementById("address").value = site.address || "";
        document.getElementById("city").value = site.city || "";
        document.getElementById("state").value = site.state || "";
        document.getElementById("zip_code").value = site.zip_code || "";

        // Set account_id field
        const accountIdField = document.getElementById("account_id");
        if (accountIdField) {
          accountIdField.value = site.account || "";
        }

        // Change form submission handler
        const form = document.getElementById("siteFormElement");
        form.onsubmit = (e) => updateExistingSite(e, site.id);
      }

      // formLoader has been removed, using main loader only

      // Add event listener to success modal OK button
      document
        .getElementById("siteModalOkBtn")
        .addEventListener("click", closeSiteSuccessModal);
    });
  } catch (err) {
    console.error(err);
    // Hide the content loader
    toggleContentLoader(false);
  }
}

// Function to open physician form
async function openPhysicianForm(event, physician = null) {
  if (event) event.preventDefault();

  try {
    const skillSetArea = document.getElementById("repproviderSkillSet");
    skillSetArea.innerHTML = "";
    // Add multiple timeouts to ensure the loader is hidden
    const loaderTimeouts = [];

    // First timeout - quick check after 500ms
    loaderTimeouts.push(
      setTimeout(() => {
        const loader = document.getElementById("loader");
        if (loader) loader.style.display = "none";
        ensureLoadersHidden();
      }, 500)
    );

    // Second timeout - backup check after 2 seconds
    loaderTimeouts.push(
      setTimeout(() => {
        const loader = document.getElementById("loader");
        if (loader) loader.style.display = "none";
        ensureLoadersHidden();
      }, 2000)
    );

    // Third timeout - final check after 5 seconds
    loaderTimeouts.push(
      setTimeout(() => {
        const loader = document.getElementById("loader");
        if (loader) loader.style.display = "none";
        ensureLoadersHidden();
      }, 5000)
    );

    // Final safety timeout after 15 seconds
    loaderTimeouts.push(
      setTimeout(() => {
        console.log("Final safety timeout triggered in openPhysicianForm");
        ensureLoadersHidden();
      }, 15000)
    );

    // Add edit=true parameter if editing an existing physician
    const formUrl = physician
      ? "physicianform.html?edit=true"
      : "physicianform.html";

    $(skillSetArea).load(formUrl, async () => {
      // Clear all timeouts since the load completed
      loaderTimeouts.forEach(clearTimeout);

      // Expose the getAllSites function to the window object
      window.getAllSitesFromParent = getAllSites;

      // If the form has a loadSitesForPhysicianForm function, call it
      if (typeof window.loadSitesForPhysicianForm === "function") {
        window.loadSitesForPhysicianForm();
      } else {
        // Try to find the function in the document scope
        setTimeout(() => {
          if (typeof loadSitesForPhysicianForm === "function") {
            console.log(
              "Calling loadSitesForPhysicianForm from document scope"
            );
            loadSitesForPhysicianForm();
          }
        }, 300);
      }

      // We've removed the procedure type and experience type dropdowns

      // Call the loadCredentialsForDropdown function if it exists
      if (typeof window.loadCredentialsForDropdown === "function") {
        console.log(
          "Calling loadCredentialsForDropdown from openPhysicianForm"
        );
        // Force reload credentials to ensure we have the latest data
        window
          .loadCredentialsForDropdown(true)
          .then(() => {
            // If we're editing a physician, set the credential after loading
            if (physician && physician.credential) {
              console.log(
                `Setting credential after loading: ${physician.credential}`
              );
              if (typeof window.setPhysicianCredential === "function") {
                window.setPhysicianCredential(physician.credential);
              }
            }
          })
          .catch((error) => {
            console.error("Error loading credentials:", error);
          });
      } else {
        // Try to find the function in the document scope
        setTimeout(() => {
          if (typeof loadCredentialsForDropdown === "function") {
            console.log(
              "Calling loadCredentialsForDropdown from document scope"
            );
            loadCredentialsForDropdown(true)
              .then(() => {
                // If we're editing a physician, set the credential after loading
                if (physician && physician.credential) {
                  console.log(
                    `Setting credential after loading: ${physician.credential}`
                  );
                  if (typeof setPhysicianCredential === "function") {
                    setPhysicianCredential(physician.credential);
                  }
                }
              })
              .catch((error) => {
                console.error("Error loading credentials:", error);
              });
          }
        }, 300);
      }

      // Call the loadExperienceForDropdown function if it exists
      if (typeof window.loadExperienceForDropdown === "function") {
        console.log("Calling loadExperienceForDropdown from openPhysicianForm");
        // Force reload experience options to ensure we have the latest data
        window
          .loadExperienceForDropdown(true)
          .then(() => {
            // If we're editing a physician, set the experience after loading
            if (physician && physician.experience) {
              console.log(
                `Setting experience after loading: ${physician.experience}`
              );
              console.log(
                `Experience value type: ${typeof physician.experience}`
              );
              if (typeof window.setPhysicianExperience === "function") {
                window.setPhysicianExperience(physician.experience);
              }
            }
          })
          .catch((error) => {
            console.error("Error loading experience options:", error);
          });
      } else {
        // Try to find the function in the document scope
        setTimeout(() => {
          if (typeof loadExperienceForDropdown === "function") {
            console.log(
              "Calling loadExperienceForDropdown from document scope"
            );
            loadExperienceForDropdown(true)
              .then(() => {
                // If we're editing a physician, set the experience after loading
                if (physician && physician.experience) {
                  console.log(
                    `Setting experience after loading: ${physician.experience}`
                  );
                  console.log(
                    `Experience value type: ${typeof physician.experience}`
                  );
                  if (typeof setPhysicianExperience === "function") {
                    setPhysicianExperience(physician.experience);
                  }
                }
              })
              .catch((error) => {
                console.error("Error loading experience options:", error);
              });
          }
        }, 300);
      }

      // Handle site dropdown based on whether we're in add or edit mode
      if (physician) {
        // In edit mode, directly remove the site dropdown container
        console.log("In edit mode, directly removing site dropdown container");
        setTimeout(() => {
          const siteContainer = document.getElementById(
            "site-dropdown-container"
          );
          if (siteContainer) {
            console.log("Found site dropdown container, removing it");
            siteContainer.remove(); // Completely remove from DOM
          } else {
            console.log("Site dropdown container not found");
          }
        }, 100);
      } else {
        // In add mode, call the loadSitesForDropdown function if it exists
        console.log("In add mode, loading sites for dropdown");
        if (typeof window.loadSitesForDropdown === "function") {
          console.log("Calling loadSitesForDropdown from openPhysicianForm");
          window.loadSitesForDropdown();
        } else {
          // Try to find the function in the document scope
          setTimeout(() => {
            if (typeof loadSitesForDropdown === "function") {
              console.log("Calling loadSitesForDropdown from document scope");
              loadSitesForDropdown();
            }
          }, 300);
        }
      }

      // If editing an existing physician, populate the form
      if (physician) {
        // Validate that the physician object has an ID
        if (!physician.id) {
          console.error("Physician object is missing ID:", physician);

          // Try to get the ID from different sources
          // 1. Check if it's in the URL parameters
          const urlParams = new URLSearchParams(window.location.search);
          const physicianIdFromUrl = urlParams.get("id");

          if (physicianIdFromUrl) {
            console.log(`Adding physician ID from URL: ${physicianIdFromUrl}`);
            physician.id = physicianIdFromUrl;
          }
          // 2. If not in URL, check if it's in the pathname (e.g., /physicians/123)
          else {
            const pathMatch = window.location.pathname.match(
              /\/physicians\/([\w-]+)/
            );
            if (pathMatch && pathMatch[1]) {
              console.log(`Adding physician ID from pathname: ${pathMatch[1]}`);
              physician.id = pathMatch[1];
            }
          }

          // 3. If still no ID, try to extract it from the data-id attribute of the selected physician element
          if (!physician.id) {
            const selectedPhysician = document.querySelector(
              ".physician-name.bg-gray-300"
            );
            if (selectedPhysician) {
              const dataId = selectedPhysician.getAttribute("data-id");
              if (dataId) {
                console.log(
                  `Adding physician ID from selected element: ${dataId}`
                );
                physician.id = dataId;
              }
            }
          }
        }

        document.querySelector("#physicianFormElement h1").textContent =
          "Edit Implanting Physician";
        document.querySelector(
          '#physicianFormElement button[type="submit"]'
        ).textContent = "Update";

        // Populate form fields
        document.getElementById("first_name").value =
          physician.first_name || "";
        document.getElementById("last_name").value = physician.last_name || "";
        document.getElementById("middle_name").value =
          physician.middle_name || "";
        // Set the credential dropdown value using the dedicated function
        if (physician.credential) {
          console.log(`Setting credential value: ${physician.credential}`);

          // Store the credential in sessionStorage for persistence
          try {
            sessionStorage.setItem(
              "lastSelectedCredential",
              physician.credential
            );
            console.log(
              `Stored credential in sessionStorage: ${physician.credential}`
            );
          } catch (error) {
            console.error("Error storing credential in sessionStorage:", error);
          }

          // Set the credential using the dedicated function
          // This will be called after credentials are loaded in openPhysicianForm
          // We're adding a backup call here with a delay to ensure it's set
          setTimeout(() => {
            if (typeof window.setPhysicianCredential === "function") {
              console.log(
                `Delayed call to setPhysicianCredential with value: ${physician.credential}`
              );
              window.setPhysicianCredential(physician.credential);
            } else {
              console.error("setPhysicianCredential function not found");
            }
          }, 1000); // Longer delay to ensure the form and credentials are fully loaded
        } else {
          console.log("No credential value to set");
        }
        document.getElementById("npi_number").value =
          physician.npi_number || "";
        document.getElementById("email_id").value = physician.email_id || "";
        document.getElementById("phone_number").value =
          physician.phone_number || "";
        // Fax number field has been removed from the form
        // document.getElementById("fax_number").value = physician.fax_number || "";
        // Set the experience dropdown value using the dedicated function
        if (physician.experience) {
          console.log(`Setting experience value: ${physician.experience}`);
          console.log(`Experience value type: ${typeof physician.experience}`);
          console.log(
            `Full physician data:`,
            JSON.stringify(physician, null, 2)
          );

          // Store the experience in sessionStorage for persistence
          try {
            sessionStorage.setItem(
              "lastSelectedExperience",
              physician.experience
            );
            console.log(
              `Stored experience in sessionStorage: ${physician.experience}`
            );
          } catch (error) {
            console.error("Error storing experience in sessionStorage:", error);
          }

          // Set the experience using the dedicated function
          // This will be called after experience options are loaded in openPhysicianForm
          // We're adding a backup call here with a delay to ensure it's set
          setTimeout(() => {
            if (typeof window.setPhysicianExperience === "function") {
              console.log(
                `Delayed call to setPhysicianExperience with value: ${physician.experience}`
              );
              window.setPhysicianExperience(physician.experience);
            } else {
              console.error("setPhysicianExperience function not found");
            }
          }, 1000); // Longer delay to ensure the form and experience options are fully loaded
        } else {
          console.log("No experience value to set");
        }
        document.getElementById("address").value = physician.address || "";
        document.getElementById("city").value = physician.city || "";
        document.getElementById("state").value = physician.state || "";
        document.getElementById("zip_code").value = physician.zip_code || "";

        // Add a hidden input for the site_id and hide the site dropdown
        try {
          // Create a hidden input for site_id
          const siteInput = document.createElement("input");
          siteInput.type = "hidden";
          siteInput.name = "site_id";
          siteInput.value = physician.site_id;
          document
            .getElementById("physicianFormElement")
            .appendChild(siteInput);
          console.log(
            "Added hidden site_id input with value:",
            physician.site_id
          );

          // Also store the site ID in sessionStorage for future use
          sessionStorage.setItem("currentSiteId", physician.site_id);
          console.log("Stored site_id in sessionStorage:", physician.site_id);

          // Site dropdown has been completely removed from the form
          console.log(
            "Site dropdown has been completely removed from the form"
          );
        } catch (error) {
          console.error("Error handling site dropdown in edit mode:", error);
        }

        // Add hidden input for physician ID
        const idInput = document.createElement("input");
        idInput.type = "hidden";
        idInput.name = "id";
        idInput.id = "physician_id";

        // Make sure we have a valid ID to use
        if (physician.id) {
          idInput.value = physician.id;
          console.log(`Added hidden input with physician ID: ${physician.id}`);
        } else {
          // Try to get ID from URL if not in physician object
          const urlParams = new URLSearchParams(window.location.search);
          const physicianIdFromUrl = urlParams.get("id");

          if (physicianIdFromUrl) {
            idInput.value = physicianIdFromUrl;
            console.log(
              `Added hidden input with physician ID from URL: ${physicianIdFromUrl}`
            );
          } else {
            // If we still don't have an ID, use the one from the function parameter if available
            const pathMatch = window.location.pathname.match(
              /\/physicians\/([\w-]+)/
            );
            if (pathMatch && pathMatch[1]) {
              idInput.value = pathMatch[1];
              console.log(
                `Added hidden input with physician ID from pathname: ${pathMatch[1]}`
              );
            } else {
              console.error(
                "Could not determine physician ID for hidden input"
              );
            }
          }
        }

        // Add the hidden input to the form
        document.getElementById("physicianFormElement").appendChild(idInput);

        // Change form submission handler - use the same approach as site form
        const form = document.getElementById("physicianFormElement");

        // Directly set the onsubmit property (not attribute)
        form.onsubmit = function (e) {
          // Make sure we have a valid ID to use for the update
          let idToUse = physician.id;

          // If physician.id is not available, try to get it from the hidden input
          if (!idToUse) {
            const hiddenInput = document.getElementById("physician_id");
            if (hiddenInput && hiddenInput.value) {
              idToUse = hiddenInput.value;
              console.log(`Using physician ID from hidden input: ${idToUse}`);
            } else {
              // Try to get ID from URL if not in physician object or hidden input
              const urlParams = new URLSearchParams(window.location.search);
              const physicianIdFromUrl = urlParams.get("id");

              if (physicianIdFromUrl) {
                idToUse = physicianIdFromUrl;
                console.log(`Using physician ID from URL: ${idToUse}`);
              }
            }
          }

          if (!idToUse) {
            console.error("Could not determine physician ID for update");
            return false; // Prevent form submission if we can't determine the ID
          }

          return updateExistingPhysician(e, idToUse);
        };

        console.log("Set form.onsubmit property for updating physician");

        // We've already handled the site_id with a hidden input

        // We've already set the form submission handler above
      }

      // Explicitly hide the content loader
      console.log("Explicitly hiding content loader after physician form load");
      toggleContentLoader(false);
      ensureLoadersHidden();

      // Add event listener to success modal OK button
      document
        .getElementById("physicianModalOkBtn")
        .addEventListener("click", closePhysicianSuccessModal);
    });
  } catch (err) {
    console.error("Error in openPhysicianForm:", err);
    // Hide the content loader in case of error
    console.log("Hiding content loader due to error in physician form");
    toggleContentLoader(false);
    ensureLoadersHidden();
  }
}

// Function to create a new site
async function createNewSite(event) {
  event.preventDefault();

  try {
    // Show the content loader
    toggleContentLoader(true);

    const form = document.getElementById("siteFormElement");
    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    const result = await createSite(formObject);

    if (result.status === "success") {
      siteModal(true, { name: formObject.name });
      // Reload sites after successful creation
      await loadSites();
    } else {
      siteModal(false, result);
    }
  } catch (err) {
    console.error(err);
    siteModal(false, err);
  } finally {
    // Hide the content loader
    toggleContentLoader(false);
  }
}

// Function to update an existing site
async function updateExistingSite(event, siteId) {
  event.preventDefault();

  try {
    // Show the content loader
    toggleContentLoader(true);

    const form = document.getElementById("siteFormElement");
    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      formObject[key] = value;
    });

    const result = await updateSite(siteId, formObject);

    if (result.status === "success") {
      siteModal(true, { name: formObject.name, isUpdate: true });
      // Reload sites after successful update
      await loadSites();
    } else {
      siteModal(false, result);
    }
  } catch (err) {
    console.error(err);
    siteModal(false, err);
  } finally {
    // Hide the content loader
    toggleContentLoader(false);
  }
}

// Function to create a new physician
async function createNewPhysician(event) {
  event.preventDefault();

  try {
    // Show the content loader
    toggleContentLoader(true);

    const form = document.getElementById("physicianFormElement");

    const formData = new FormData(form);
    const formObject = {};

    formData.forEach((value, key) => {
      // Convert empty strings to null
      formObject[key] = value === "" ? null : value;
    });

    // Site dropdown has been removed from the form
    // We need to get the site_id from the URL or another source
    if (!formObject.site_id) {
      // Try to get from sessionStorage first
      const storedSiteId = sessionStorage.getItem("currentSiteId");
      if (storedSiteId) {
        formObject.site_id = storedSiteId;
      } else {
        // Try to get site_id from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const siteIdFromUrl = urlParams.get("site_id");

        if (siteIdFromUrl) {
          formObject.site_id = siteIdFromUrl;

          // Store the site ID in sessionStorage for future use
          sessionStorage.setItem("currentSiteId", siteIdFromUrl);
        } else {
          // If we can't find a site_id, show an error
          console.error(
            "No site_id found in form, sessionStorage, or URL parameters"
          );
          console.error(
            "site_id is required for the API endpoint /sites/{site_id}/physicians"
          );
          physicianModal(false, {
            message:
              "Site ID is required. Please try again from the site page.",
          });
          return; // Exit early if no site_id
        }
      }
    } else {
      // Store the site ID in sessionStorage for future use
      sessionStorage.setItem("currentSiteId", formObject.site_id);
    }

    const result = await createImplantingPhysician(formObject);

    if (result.status === "success") {
      console.log("Physician created successfully");
      physicianModal(true, {
        name: `${formObject.first_name} ${formObject.last_name}`,
      });
      // Reload physicians after successful creation
      console.log("Reloading physicians list");
      await loadPhysicians();
      console.log("Physicians list reloaded");
    } else {
      console.error("Failed to create physician:", result);
      physicianModal(false, result);
    }
  } catch (err) {
    console.error("Error creating physician:", err);
    console.error("Error details:", err.message || err);
    console.error("Error stack:", err.stack || "No stack trace available");
    physicianModal(false, err);
  } finally {
    // Hide the content loader
    console.log("Hiding content loader");
    toggleContentLoader(false);
    console.log("Create physician operation completed");
  }
}

// Function to update an existing physician
async function updateExistingPhysician(event, physicianId) {
  // Make sure to prevent the default form submission
  event.preventDefault();

  console.log(`updateExistingPhysician called with ID: ${physicianId}`);

  // Use our global variables for site and physician selection
  // If they're not set, try to get them from the DOM
  if (!lastSelectedSiteId) {
    const selectedSiteElement = document.querySelector(
      ".site-name.bg-gray-300"
    );
    if (selectedSiteElement) {
      lastSelectedSiteId = selectedSiteElement.getAttribute("data-id");
      console.log(`Got site ID from DOM: ${lastSelectedSiteId}`);
    }
  }

  if (!lastSelectedPhysicianId) {
    const selectedPhysicianElement = document.querySelector(
      ".physician-name.bg-gray-300"
    );
    if (selectedPhysicianElement) {
      lastSelectedPhysicianId =
        selectedPhysicianElement.getAttribute("data-id");
      console.log(`Got physician ID from DOM: ${lastSelectedPhysicianId}`);
    } else {
      lastSelectedPhysicianId = physicianId;
      console.log(`Using provided physician ID: ${lastSelectedPhysicianId}`);
    }
  }

  // Make sure we have the physician ID
  if (!lastSelectedPhysicianId) {
    lastSelectedPhysicianId = physicianId;
    console.log(`Using provided physician ID: ${lastSelectedPhysicianId}`);
  }

  // Store these values in sessionStorage for restoration after update
  setCurrentSelections(lastSelectedSiteId, lastSelectedPhysicianId);
  console.log(
    `Using site ID: ${lastSelectedSiteId}, physician ID: ${lastSelectedPhysicianId}`
  );

  // If we have a physician ID but no site ID, try to get the site ID from the API
  if (lastSelectedPhysicianId && !lastSelectedSiteId) {
    try {
      console.log(
        `Trying to get site ID from API for physician ID: ${lastSelectedPhysicianId}`
      );
      const siteId = await getPhysicianSiteId(lastSelectedPhysicianId);
      if (siteId) {
        lastSelectedSiteId = siteId;
        setCurrentSelections(lastSelectedSiteId, null);
        console.log(`Got site ID from API: ${lastSelectedSiteId}`);
      }
    } catch (error) {
      console.error("Error getting site ID from API:", error);
    }
  }

  try {
    // Show the content loader
    toggleContentLoader(true);

    const form = document.getElementById("physicianFormElement");

    const formData = new FormData(form);
    const formObject = {};

    // Log all form fields

    formData.forEach((value, key) => {
      console.log(`${key}: ${value}`);
      // Convert empty strings to null
      formObject[key] = value === "" ? null : value;
    });

    // Make sure the physician ID is included in the form data
    if (!formObject.id && physicianId) {
      formObject.id = physicianId;
      console.log(`Added missing physician ID to form data: ${physicianId}`);
    }

    console.log(
      "Updating physician with data:",
      JSON.stringify(formObject, null, 2)
    );
    console.log(`API endpoint: PUT /sites/physicians/${physicianId}`);

    // Make sure the site_id is included with multiple fallbacks
    if (!formObject.site_id) {
      // Look for a hidden input with name site_id
      const hiddenSiteInput = document.querySelector(
        'input[type="hidden"][name="site_id"]'
      );
      if (hiddenSiteInput && hiddenSiteInput.value) {
        formObject.site_id = hiddenSiteInput.value;
        console.log("Added site_id from hidden input:", hiddenSiteInput.value);

        // Store the site ID in sessionStorage for future use
        sessionStorage.setItem("currentSiteId", hiddenSiteInput.value);
        sessionStorage.setItem("lastSelectedSiteId", hiddenSiteInput.value);
        console.log("Stored site_id in sessionStorage:", hiddenSiteInput.value);
      } else {
        // Try to get from sessionStorage first (try both keys)
        const storedSiteId =
          sessionStorage.getItem("lastSelectedSiteId") ||
          sessionStorage.getItem("currentSiteId");
        if (storedSiteId) {
          formObject.site_id = storedSiteId;
          console.log("Added site_id from sessionStorage:", storedSiteId);
        } else {
          // Try to get the site ID from the selected site element
          const selectedSiteElement = document.querySelector(
            ".site-name.bg-gray-300"
          );
          if (selectedSiteElement) {
            const selectedSiteId = selectedSiteElement.getAttribute("data-id");
            if (selectedSiteId) {
              formObject.site_id = selectedSiteId;
              console.log(
                `Added site_id from selected site element: ${selectedSiteId}`
              );

              // Store the site ID in sessionStorage for future use
              sessionStorage.setItem("currentSiteId", selectedSiteId);
              sessionStorage.setItem("lastSelectedSiteId", selectedSiteId);
              console.log("Stored site_id in sessionStorage:", selectedSiteId);
            }
          } else {
            // Site dropdown has been removed, try to get site_id from URL
            const urlParams = new URLSearchParams(window.location.search);
            const siteIdFromUrl = urlParams.get("site_id");

            if (siteIdFromUrl) {
              formObject.site_id = siteIdFromUrl;
              console.log("Added site_id from URL parameters:", siteIdFromUrl);

              // Store the site ID in sessionStorage for future use
              sessionStorage.setItem("currentSiteId", siteIdFromUrl);
              sessionStorage.setItem("lastSelectedSiteId", siteIdFromUrl);
              console.log("Stored site_id in sessionStorage:", siteIdFromUrl);
            } else {
              console.error(
                "No site_id found in form, hidden input, sessionStorage, selected element, or URL"
              );
              physicianModal(false, { message: "Site ID is required" });
              return; // Exit early if no site_id
            }
          }
        }
      }
    } else {
      // Store the site ID in sessionStorage for future use
      sessionStorage.setItem("currentSiteId", formObject.site_id);
      sessionStorage.setItem("lastSelectedSiteId", formObject.site_id);
      console.log("Stored site_id in sessionStorage:", formObject.site_id);
    }

    const result = await updateImplantingPhysician(physicianId, formObject);

    if (result.status === "success") {
      console.log("Physician updated successfully");
      physicianModal(true, {
        name: `${formObject.first_name} ${formObject.last_name}`,
        isUpdate: true,
      });

      // Make sure we have the site ID
      if (!formObject.site_id) {
        // Try to get the site ID from the selected site element
        const selectedSiteElement = document.querySelector(
          ".site-name.bg-gray-300"
        );
        if (selectedSiteElement) {
          const selectedSiteId = selectedSiteElement.getAttribute("data-id");
          if (selectedSiteId) {
            formObject.site_id = selectedSiteId;
            console.log(
              `Added site_id from selected site element: ${selectedSiteId}`
            );
          }
        }

        // If still no site ID, try to get it from sessionStorage
        if (!formObject.site_id) {
          const storedSiteId =
            sessionStorage.getItem("lastSelectedSiteId") ||
            sessionStorage.getItem("currentSiteId");
          if (storedSiteId) {
            formObject.site_id = storedSiteId;
            console.log(`Added site_id from sessionStorage: ${storedSiteId}`);
          }
        }
      }

      // Store the updated physician data for reference
      const updatedPhysician = {
        ...formObject,
        id: physicianId,
      };

      // Log the site ID to make sure it's included
      console.log(`Site ID in updatedPhysician: ${updatedPhysician.site_id}`);

      // Store both the physician data and the site ID separately for redundancy
      sessionStorage.setItem(
        "lastUpdatedPhysician",
        JSON.stringify(updatedPhysician)
      );

      if (updatedPhysician.site_id) {
        sessionStorage.setItem("lastSelectedSiteId", updatedPhysician.site_id);
        sessionStorage.setItem("currentSiteId", updatedPhysician.site_id);
        console.log(
          `Stored site ID in sessionStorage: ${updatedPhysician.site_id}`
        );
      }

      console.log("Updated physician data stored in session storage");

      // We'll let the closePhysicianSuccessModal function handle reloading the physicians
      // and maintaining the selection state
    } else {
      console.error("Failed to update physician:", result);
      physicianModal(false, result);
    }
  } catch (err) {
    console.error("Error updating physician:", err);
    console.error("Error details:", err.message || err);
    console.error("Error stack:", err.stack || "No stack trace available");
    physicianModal(false, err);
  } finally {
    // Hide the content loader
    console.log("Hiding content loader");
    toggleContentLoader(false);

    // Make sure all loaders are hidden
    ensureLoadersHidden();
    console.log(
      "Update physician operation completed, ensured all loaders are hidden"
    );
  }

  // Return false to prevent form submission
  return false;
}

// Function to show site success/error modal
function siteModal(success = true, data = {}) {
  const modal = document.getElementById("SiteSuccessModal");
  const header = modal.querySelector("#header");
  const headerTitle = modal.querySelector("#headertitle");
  const desc = modal.querySelector("#desc");

  if (success) {
    header.className =
      "flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = data.isUpdate ? "Site Updated" : "Site Created";
    desc.textContent = data.isUpdate
      ? `Site "${data.name}" has been updated successfully.`
      : `Site "${data.name}" has been created successfully.`;
  } else {
    header.className =
      "flex items-center justify-between bg-red-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = "Error";
    desc.textContent =
      data.message || "An error occurred while processing your request.";
  }

  // Make sure the modal is visible
  modal.classList.remove("hidden");
  modal.style.display = "flex";
}

// Function to show physician success/error modal
function physicianModal(success = true, data = {}) {
  const modal = document.getElementById("PhysicianSuccessModal");
  const header = modal.querySelector("#header");
  const headerTitle = modal.querySelector("#headertitle");
  const desc = modal.querySelector("#desc");

  if (success) {
    header.className =
      "flex items-center justify-between bg-green-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = data.isUpdate
      ? "Physician Updated"
      : "Physician Created";
    desc.textContent = data.isUpdate
      ? `Dr. ${data.name} has been updated successfully.`
      : `Dr. ${data.name} has been created successfully.`;
  } else {
    header.className =
      "flex items-center justify-between bg-red-500 text-white font-medium text-lg p-4 rounded-t-lg";
    headerTitle.textContent = "Error";
    desc.textContent =
      data.message || "An error occurred while processing your request.";
  }

  // Make sure the modal is visible
  modal.classList.remove("hidden");
  modal.style.display = "flex";
}

// Function to close site success modal
async function closeSiteSuccessModal() {
  // Don't reset the form, just hide the modal
  const modal = document.getElementById("SiteSuccessModal");
  modal.classList.add("hidden");
  modal.style.display = "none";

  try {
    // Get the site ID from the hidden field
    const siteIdField = document.querySelector(
      '#siteFormElement input[name="id"]'
    );
    const siteId = siteIdField ? siteIdField.value : null;

    if (siteId) {
      // Fetch the updated site data
      const sites = await getAllSites();
      const updatedSite = sites.find((site) => site.id == siteId);

      if (updatedSite) {
        // Update the form with the latest data
        document.getElementById("name").value = updatedSite.name || "";
        document.getElementById("address").value = updatedSite.address || "";
        document.getElementById("city").value = updatedSite.city || "";
        document.getElementById("state").value = updatedSite.state || "";
        document.getElementById("zip_code").value = updatedSite.zip_code || "";

        // Update account_id field
        const accountIdField = document.getElementById("account_id");
        if (accountIdField) {
          accountIdField.value = updatedSite.account || "";
          console.log(
            "Account ID field updated with value:",
            accountIdField.value
          );
        } else {
          console.log("Account ID field not found in the form");
        }

        // Highlight the site in the list
        const siteElement = document.querySelector(
          `.site-name[data-id="${siteId}"]`
        );
        if (siteElement) {
          // Remove highlight from all sites
          document.querySelectorAll(".site-name").forEach((el) => {
            el.classList.remove("bg-gray-300");
          });
          // Highlight this site
          siteElement.classList.add("bg-gray-300");
        }
      }
    } else {
      // If it's a new site, reload the sites list
      await loadSites();
    }
  } catch (error) {
    console.error("Error in closeSiteSuccessModal:", error);
    // Fallback to reloading the sites list
    await loadSites();
  }
}

// Function to close physician success modal
async function closePhysicianSuccessModal() {
  // Don't reset the form, just hide the modal
  const modal = document.getElementById("PhysicianSuccessModal");
  modal.classList.add("hidden");
  modal.style.display = "none";

  // Set a safety timeout to ensure loaders are hidden after 10 seconds
  const safetyTimeout = setTimeout(() => {
    console.log("Safety timeout triggered: Hiding all loaders");
    ensureLoadersHidden();
  }, 10000);

  try {
    // Use our global variables for site and physician selection
    console.log(
      `Using global variables - Site ID: ${lastSelectedSiteId}, Physician ID: ${lastSelectedPhysicianId}`
    );

    // If global variables are not set, try to get them from sessionStorage
    if (!lastSelectedSiteId) {
      lastSelectedSiteId =
        sessionStorage.getItem("lastSelectedSiteId") ||
        sessionStorage.getItem("currentSiteId");
      console.log(`Got site ID from sessionStorage: ${lastSelectedSiteId}`);
    }

    if (!lastSelectedPhysicianId) {
      lastSelectedPhysicianId = sessionStorage.getItem(
        "lastSelectedPhysicianId"
      );
      console.log(
        `Got physician ID from sessionStorage: ${lastSelectedPhysicianId}`
      );
    }

    // Check if we have updated physician data in sessionStorage
    const lastUpdatedPhysicianJson = sessionStorage.getItem(
      "lastUpdatedPhysician"
    );
    let lastUpdatedPhysician = null;
    if (lastUpdatedPhysicianJson) {
      try {
        lastUpdatedPhysician = JSON.parse(lastUpdatedPhysicianJson);
        console.log(
          "Retrieved updated physician data from sessionStorage:",
          lastUpdatedPhysician
        );

        // If we have updated physician data, use it to set the IDs
        if (lastUpdatedPhysician) {
          if (!lastSelectedPhysicianId && lastUpdatedPhysician.id) {
            lastSelectedPhysicianId = lastUpdatedPhysician.id;
            console.log(
              `Got physician ID from updated data: ${lastSelectedPhysicianId}`
            );
          }

          if (!lastSelectedSiteId && lastUpdatedPhysician.site_id) {
            lastSelectedSiteId = lastUpdatedPhysician.site_id;
            console.log(`Got site ID from updated data: ${lastSelectedSiteId}`);
          }
        }
      } catch (e) {
        console.error("Error parsing lastUpdatedPhysician JSON:", e);
      }
    }

    // Get the physician ID from the hidden field as a fallback
    if (!lastSelectedPhysicianId) {
      const physicianIdField = document.querySelector(
        '#physicianFormElement input[name="id"]'
      );
      if (physicianIdField && physicianIdField.value) {
        lastSelectedPhysicianId = physicianIdField.value;
        console.log(
          `Got physician ID from hidden field: ${lastSelectedPhysicianId}`
        );
      }
    }

    // Get the site ID from the hidden field as a fallback
    if (!lastSelectedSiteId) {
      const siteIdField = document.getElementById("site_id");
      if (siteIdField && siteIdField.value) {
        lastSelectedSiteId = siteIdField.value;
        console.log(`Got site ID from hidden field: ${lastSelectedSiteId}`);
      }
    }

    // If still no site ID, try to get it from the selected site element
    if (!lastSelectedSiteId) {
      const selectedSiteElement = document.querySelector(
        ".site-name.bg-gray-300"
      );
      if (selectedSiteElement) {
        lastSelectedSiteId = selectedSiteElement.getAttribute("data-id");
        console.log(
          `Got site ID from selected site element: ${lastSelectedSiteId}`
        );
      }
    }

    // If still no site ID and we have a physician ID, try to get it from the API
    if (!lastSelectedSiteId && lastSelectedPhysicianId) {
      console.log(
        `No site ID found, trying to get it from the API for physician ID: ${lastSelectedPhysicianId}`
      );
      try {
        // Use the new function to get the site ID for this physician
        lastSelectedSiteId = await getPhysicianSiteId(lastSelectedPhysicianId);
        console.log(`Got site ID from API: ${lastSelectedSiteId}`);
      } catch (error) {
        console.error("Error getting site ID from API:", error);
      }
    }

    // Store the IDs using our function
    setCurrentSelections(lastSelectedSiteId, lastSelectedPhysicianId);
    console.log(
      `Final IDs - Site: ${lastSelectedSiteId}, Physician: ${lastSelectedPhysicianId}`
    );

    if (lastSelectedSiteId) {
      // Set the site filter dropdown to the selected site
      const siteFilter = document.getElementById("physician-site-filter");
      if (siteFilter) {
        siteFilter.value = lastSelectedSiteId;
        console.log(`Set site filter dropdown to: ${lastSelectedSiteId}`);
      }

      // Add a small delay before reloading to ensure the UI has time to update
      console.log("Adding a small delay before reloading physicians...");
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Reload the physicians for this site
      console.log(`Reloading physicians for site ID: ${lastSelectedSiteId}`);
      await loadPhysiciansBySite(lastSelectedSiteId);

      // Highlight the site in the left panel
      const siteElement = document.querySelector(
        `.site-name[data-id="${lastSelectedSiteId}"]`
      );
      if (siteElement) {
        // Remove highlight from all sites
        document.querySelectorAll(".site-name").forEach((el) => {
          el.classList.remove("bg-gray-300");
        });
        // Highlight this site
        siteElement.classList.add("bg-gray-300");
        console.log(`Highlighted site in left panel: ${lastSelectedSiteId}`);

        // Scroll the site into view if needed
        siteElement.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
        });
      }

      if (lastSelectedPhysicianId) {
        // Add a longer delay before highlighting the physician to ensure the list is fully loaded
        console.log("Adding a delay before highlighting the physician...");
        await new Promise((resolve) => setTimeout(resolve, 800));

        // Highlight the physician in the list and load specific data
        console.log(
          `Looking for physician element with ID: ${lastSelectedPhysicianId}`
        );
        const physicianElement = document.querySelector(
          `.physician-name[data-id="${lastSelectedPhysicianId}"]`
        );

        if (physicianElement) {
          console.log(
            `Found physician element for ID: ${lastSelectedPhysicianId}`
          );

          // Remove highlight from all physicians
          document.querySelectorAll(".physician-name").forEach((el) => {
            el.classList.remove("bg-gray-300");
          });

          // Highlight this physician
          physicianElement.classList.add("bg-gray-300");
          console.log(
            `Highlighted physician in list: ${lastSelectedPhysicianId}`
          );

          // Scroll the physician into view if needed
          physicianElement.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
          });

          try {
            // Validate that we have a valid physician ID
            if (
              !lastSelectedPhysicianId ||
              lastSelectedPhysicianId === "undefined"
            ) {
              console.error(
                "Invalid physician ID after update:",
                lastSelectedPhysicianId
              );
              return;
            }

            // Show loader
            toggleLoader(document.getElementById("loader"), true);

            // Fetch specific physician data using the new function
            console.log(
              `Fetching specific physician data for ID: ${lastSelectedPhysicianId}`
            );
            const updatedPhysician = await fetchSpecificImplantingPhysician(
              lastSelectedPhysicianId
            );

            if (updatedPhysician) {
              console.log(
                "Updated physician data fetched successfully:",
                updatedPhysician
              );
              // Open the form with the updated data
              await openPhysicianForm(null, updatedPhysician);
            } else {
              console.error("No updated physician data returned from API");
              toggleLoader(document.getElementById("loader"), false);
            }
          } catch (error) {
            console.error("Error fetching updated physician data:", error);
            toggleLoader(document.getElementById("loader"), false);
          }
        } else {
          console.log(
            `Physician element not found for ID: ${lastSelectedPhysicianId}`
          );
          console.log("Available physician elements:");
          document.querySelectorAll(".physician-name").forEach((el, index) => {
            console.log(
              `Physician ${index + 1}: ID=${el.getAttribute(
                "data-id"
              )}, Text=${el.textContent.trim()}`
            );
          });

          // If we couldn't find the physician element, try to reload the physicians list again with a longer delay
          console.log(
            "Trying to reload physicians list with a longer delay..."
          );
          setTimeout(async () => {
            try {
              await loadPhysiciansBySite(lastSelectedSiteId);

              // Try to find the physician element again
              console.log(
                `Looking for physician element again with ID: ${lastSelectedPhysicianId}`
              );
              const retryPhysicianElement = document.querySelector(
                `.physician-name[data-id="${lastSelectedPhysicianId}"]`
              );

              if (retryPhysicianElement) {
                console.log(
                  `Found physician element on retry for ID: ${lastSelectedPhysicianId}`
                );

                // Remove highlight from all physicians
                document.querySelectorAll(".physician-name").forEach((el) => {
                  el.classList.remove("bg-gray-300");
                });

                // Highlight this physician
                retryPhysicianElement.classList.add("bg-gray-300");
                console.log(
                  `Highlighted physician in list on retry: ${lastSelectedPhysicianId}`
                );

                // Scroll the physician into view if needed
                retryPhysicianElement.scrollIntoView({
                  behavior: "smooth",
                  block: "nearest",
                });

                // Fetch and display the physician data
                try {
                  // Show loader
                  toggleLoader(document.getElementById("loader"), true);

                  // Fetch specific physician data
                  const updatedPhysician =
                    await fetchSpecificImplantingPhysician(
                      lastSelectedPhysicianId
                    );

                  if (updatedPhysician) {
                    // Open the form with the updated data
                    await openPhysicianForm(null, updatedPhysician);
                  } else {
                    toggleLoader(document.getElementById("loader"), false);
                  }
                } catch (error) {
                  console.error(
                    "Error fetching physician data on retry:",
                    error
                  );
                  toggleLoader(document.getElementById("loader"), false);
                }
              } else {
                console.log(
                  `Still could not find physician element on retry for ID: ${lastSelectedPhysicianId}`
                );
                // Make sure loaders are hidden
                ensureLoadersHidden();
              }
            } catch (error) {
              console.error("Error during retry:", error);
              // Make sure loaders are hidden
              ensureLoadersHidden();
            }
          }, 1500);
        }
      }
    } else {
      // If no site ID is available, check if there's a site selected in the UI
      const selectedSiteElement = document.querySelector(
        ".site-name.bg-gray-300"
      );
      if (selectedSiteElement) {
        const selectedSiteId = selectedSiteElement.getAttribute("data-id");
        if (selectedSiteId) {
          console.log(`Using selected site from UI: ${selectedSiteId}`);
          await loadPhysiciansBySite(selectedSiteId);
          return;
        }
      }

      // If it's a new physician or no site is selected, reload the physicians list
      const siteIdField = document.getElementById("site_id");
      if (siteIdField && siteIdField.value) {
        await loadPhysiciansBySite(siteIdField.value);
      } else {
        console.log("No site ID available, reloading all physicians");
        await loadPhysicians();
      }
    }
  } catch (error) {
    console.error("Error in closePhysicianSuccessModal:", error);
    // Fallback to reloading the physicians list
    await loadPhysicians();
  } finally {
    // Clear the safety timeout if we've reached the end of the function
    clearTimeout(safetyTimeout);

    // Make sure loaders are hidden
    ensureLoadersHidden();
    console.log(
      "closePhysicianSuccessModal completed, ensured loaders are hidden"
    );
  }
}
