{"pre_procedure_medications": {"verified": "", "medication": {"field_id": "6985", "label": "Medication", "medications": {"fondaparinux": {"field_id": "14883", "label": "Fondaparinux", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "heparin_derivative": {"field_id": "14883", "label": "Heparin Derivative", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "low_molecular_weight_heparin": {"field_id": "14883", "label": "Low Molecular Weight Heparin", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "unfractionated_heparin": {"field_id": "14883", "label": "Unfractionated Heparin", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "warfarin": {"field_id": "14883", "label": "Warfarin", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "aspirin_81_100_mg": {"field_id": "14883", "label": "Aspirin (81-100 mg)", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "aspirin_101_324_mg": {"field_id": "14883", "label": "Aspirin (101-324 mg)", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "aspirin_325_mg": {"field_id": "14883", "label": "Aspirin 325 mg", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "aspirin_dipyridamole": {"field_id": "14883", "label": "Aspirin/Dipyridamole", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "vorapaxar": {"field_id": "14883", "label": "Vorapaxar", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "apixaban": {"field_id": "14883", "label": "Apixaban", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "dabigatran": {"field_id": "14883", "label": "Dabigatran", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "edoxaban": {"field_id": "14883", "label": "Edoxaban", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "rivaroxaban": {"field_id": "14883", "label": "Rivaroxaban", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "cangrelor": {"field_id": "14883", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "clopidogrel": {"field_id": "14883", "label": "Clopidogrel", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "other_p2y12": {"field_id": "14883", "label": "Other P2Y12", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "prasugrel": {"field_id": "14883", "label": "Prasug<PERSON>", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "ticagrelor": {"field_id": "14883", "label": "Ticagrelor", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}, "ticlopidine": {"field_id": "14883", "label": "Ticlopidine", "options": [{"id": "1", "value": "Current"}, {"id": "2", "value": "Held"}, {"id": "3", "value": "Past"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the prescribing history and administration status (past, current, held, never) of each medication administered within 24 hours prior to the start of the current procedure?", "description": "\n**Coding Instruction:**\nIndicate the prescribing history and administration status (past, current, held, never) of each medication.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What medications were prescribed within 24 hours prior to and during the TAVR procedure? Please provide the NCDR-assigned IDs for each medication from the following list: Fondaparinux, Heparin Derivative, Low Molecular Weight Heparin, Unfractionated Heparin, Warfarin, Aspirin 81 to 100 mg, Aspirin 101 to 324 mg, Aspirin 325 mg, Aspirin/Dipyridamole, Vorapaxar, Apixaban, Dabigatran, Edoxaban, Rivaroxaban, Cangrelor, Clopidogrel, Other P2Y12, Prasugrel, Ticagrelor, and Ticlopidine.", "description": "\n**Coding Instruction:**\nIndicate the NCDR-assigned IDs for the medications prescribed within 24 hours prior to and during the procedure.\n**Target Value:**\nAny occurrence within 24 hours prior to start of current procedure"}}}