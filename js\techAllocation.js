var msnry = null;

async function showRerunStaticPopup(message, bgColorClass) {
  return new Promise((resolve) => {
    const popup = document.getElementById("rerun-confirmation-popup");
    const popupMessage = document.getElementById(
      "rerun-confirmation-popup-message"
    );
    const discardBtn = document.getElementById("cancel-btn");
    const updateBtn = document.getElementById("rerun-btn");

    popupMessage.textContent = message;
    popup.className = popup.className.replace(/bg-\w+-\d+/g, "").trim();
    toggleClass(
      popup,
      [bgColorClass, "translate-y-6", "opacity-100"],
      ["-translate-y-full", "opacity-0"]
    );

    const closePopup = () => {
      toggleClass(
        popup,
        ["-translate-y-full", "opacity-0"],
        ["translate-y-6", "opacity-100"]
      );
    };

    discardBtn.replaceWith(discardBtn.cloneNode(true));
    const newDiscardBtn = document.getElementById("cancel-btn");

    newDiscardBtn.addEventListener("click", () => {
      closePopup();
      resolve(true);
    });

    updateBtn.replaceWith(updateBtn.cloneNode(true));
    const newUpdateBtn = document.getElementById("rerun-btn");

    newUpdateBtn.addEventListener("click", async () => {
      from_date = localStorage.getItem("start-date");
      end_date = localStorage.getItem("end-date");
      const result = await triggerRescheduler(from_date, end_date);
      closePopup();
      resolve(true);
      const rescheduleButton = document.getElementById("rerunScheduleButton");
      const imgTag = rescheduleButton.querySelector("img");
      showPopup(
        "",
        `Rescheduler agent got triggered for date range ${from_date} to ${end_date}`,
        "bg-[#8F58DD]"
      );

      let status = "inprogress";
      while (status !== "completed") {
        const response = await checkJobStatus(result.job_id);
        status = response.status;

        if (status === "inprogress") {
          imgTag.classList.add("animate-spin-custom");
          await new Promise((resolve) => setTimeout(resolve, 15000));
        } else if (status === "completed") {
          localStorage.setItem(
            "agent_changes",
            JSON.stringify(response.result.assignments)
          );
          imgTag.classList.remove("animate-spin-custom");
          showPopup(
            "",
            `Rescheduler agent completed for date range ${from_date} to ${end_date}`,
            "bg-[#8F58DD]"
          );
          const reschedulereviewBtn = document.getElementById(
            "reviewScheduleButton"
          );
          reschedulereviewBtn.classList.remove("hidden");
          reschedulereviewBtn.addEventListener("click", () => {
            reschedulereviewBtn.classList.add("hidden");
            const scheduleLoader = document.getElementById("scheduleLoader");
            scheduleLoader.classList.remove("hidden");
            document
              .getElementById("reschedulereview")
              .classList.remove("hidden");
            document.getElementById("reschedulereview").classList.remove("p-2");
            document
              .getElementById("dateButtonsContainer")
              .classList.add("hidden");
            document
              .getElementById("schedulesContainer")
              .classList.add("hidden");
            document.getElementById("repAllocation").classList.remove("p-2");
            changeReview();
            scheduleLoader.classList.add("hidden");
          });
        } else {
          imgTag.classList.remove("animate-spin-custom");
          showPopup(
            "",
            `Rescheduler agent failed for date range ${from_date} to ${end_date}`,
            "bg-red-500"
          );
          break;
        }
      }
    });
  });
}

function getCurrentOrFirstDate(dateList) {
  const today = new Date().toISOString().split("T")[0]; // Get the current date in 'YYYY-MM-DD' format
  return dateList.includes(today) ? today : dateList[0]; // Return current date if found, otherwise the 0 index
}

function getDateList(startDate, endDate) {
  const dateList = [];
  let currentDate = new Date(startDate);

  while (currentDate <= new Date(endDate)) {
    // Format the date as 'YYYY-MM-DD'
    const formattedDate = currentDate.toISOString().split("T")[0];
    dateList.push(formattedDate);

    // Move to the next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dateList;
}

function extractSchedules() {
  const scheduleDivs = document.querySelectorAll(
    "#schedulesContainer .actual-schedules"
  );
  const scheduleList = [];
  let calendereventList = [];

  scheduleDivs.forEach((scheduleDiv) => {
    const siteId = scheduleDiv
      .querySelector(".provider-span")
      .getAttribute("site_id");
    const siteName = scheduleDiv
      .querySelector(".provider-span")
      .textContent.trim();
    const Implanting_physician_id = scheduleDiv
      .querySelector(".location-span")
      .getAttribute("implanting_physician_id");
    const procedureDate = scheduleDiv
      .querySelector(".provider-span")
      .getAttribute("procedure_date");
    const location = scheduleDiv
      .querySelector(".location-span")
      .textContent.trim();
    const procedurestartTime = scheduleDiv
      .querySelector(".provider-span")
      .getAttribute("procedure_start_time")
      .split(":")
      .slice(0, 2)
      .join(":");
    const total_cases = scheduleDiv
      .querySelector(".cases-span")
      .getAttribute("cases-total");
    const techDivs = scheduleDiv.querySelectorAll(
      ".sortable-container-techs .worker-card"
    );

    let rep_id = null;
    let type = "delete";
    if (techDivs.length > 0) {
      let hasUpdate = false;
      let firstNode = techDivs[0];
      techDivs.forEach((techDiv) => {
        const techElement = techDiv.querySelector(".flex-grow");
        if (techElement) {
          const currentType = techElement.getAttribute("mod-type");
          if (currentType === "update") {
            hasUpdate = true;
          }
        }
      });

      const techElement = firstNode.querySelector(".flex-grow");
      if (techElement) {
        rep_id = techElement.getAttribute("tech-aoid");
        if (hasUpdate) {
          type = "update";
        } else {
          type = techElement.getAttribute("mod-type");
        }
      }
    }

    const scheduleData = {
      site_id: siteId,
      implanting_physician_id: Implanting_physician_id,
      procedure_date: procedureDate,
      Location: location,
      rep_id: rep_id,
      type: type,
    };
    scheduleList.push(scheduleData);

    calendereventList.push({
      rep_id: rep_id,
      procedure_date: procedureDate,
      procedure_start_time: procedurestartTime,
      site_name: siteName,
      total_case: total_cases,
      type: type,
    });
  });

  const updateCases = calendereventList
    .filter((item) => item.type === "update")
    .map(({ type, ...rest }) => rest);

  return [scheduleList, updateCases];
}

function isScheduleChangedFunction() {
  const [schedules, calender_data] = extractSchedules();
  const filteredSchedules = schedules.filter(
    (schedule) => schedule.type !== "init"
  );

  const items = document.querySelectorAll(".handle-state-change");
  const parentElements = Array.from(items).map((item) => item.parentElement);

  const discardChangesBtn = document.getElementById("discardChangesButton");
  const scheduleChangesManager = document.getElementById(
    "scheduleChangesManager"
  );

  if (filteredSchedules) {
    const dateButtonsContainer = document.getElementById(
      "dateButtonsContainer"
    );
    toggleClass(scheduleChangesManager, ["flex"], ["hidden"]);
    toggleClass(dateButtonsContainer, ["mt-20"], ["mt-0"]);

    discardChangesBtn.replaceWith(discardChangesBtn.cloneNode(true));
    const newDiscardChangesBtn = document.getElementById(
      "discardChangesButton"
    );

    newDiscardChangesBtn.addEventListener("click", () => {
      toggleClass(scheduleChangesManager, ["hidden"], ["flex"]);
      toggleClass(dateButtonsContainer, ["mt-0"], ["mt-20"]);

      parentElements.forEach((item) => {
        item.removeAttribute("style");
        const overlays = item.querySelectorAll(".overlay-blocker");
        if (overlays) {
          overlays.forEach((overlay) => overlay.remove());
        }
        const tooltips = document.querySelectorAll(".tooltip");
        if (tooltips) {
          tooltips.forEach((tooltip) => tooltip.remove());
        }
      });
      populateSchedulesContainer();
    });

    parentElements.forEach((item) => {
      const overlay = document.createElement("div");
      overlay.classList.add("overlay-blocker", "cursor-pointer");
      overlay.setAttribute(
        "data-tooltip",
        "You have unsaved changes. Please save or discard them before navigating."
      );

      Object.assign(overlay.style, {
        position: "absolute",
        top: "0",
        left: "0",
        width: "100%",
        height: "100%",
        backgroundColor: "transparent",
        zIndex: "1000",
        pointerEvents: "auto",
      });

      const parentStyle = window.getComputedStyle(item);
      if (parentStyle.position === "static") {
        item.style.position = "relative";
      }

      item.appendChild(overlay);

      const tooltip = document.createElement("div");
      tooltip.classList.add("tooltip");
      tooltip.innerText = overlay.getAttribute("data-tooltip");
      tooltip.style.cssText = `
                position: fixed; /* Changed to fixed */
                top: 50px;
                left: 0;
                padding: 5px 10px;
                background: #333;
                color: #fff;
                width: 350px;
                border-radius: 4px;
                word-wrap: break-word;
                display: none;
                z-index: 1001;
            `;
      document.body.appendChild(tooltip);

      overlay.addEventListener("mouseenter", function () {
        const overlayRect = overlay.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        let top = overlayRect.top + window.scrollY;
        let left = overlayRect.right + window.scrollX;

        tooltip.style.top = `${top + 50}px`;
        tooltip.style.left = `${left}px`;

        const viewportWidth = window.innerWidth;
        if (left + tooltipRect.width > viewportWidth) {
          tooltip.style.left = `${viewportWidth - tooltipRect.width - 10}px`;
        }

        tooltip.style.display = "block";
        const parent = overlay.parentElement;
        const closestHandleStateChange = parent.querySelector(
          ".handle-state-change"
        );

        if (closestHandleStateChange) {
          if (closestHandleStateChange.tagName.toLowerCase() === "a") {
            closestHandleStateChange.classList.add("bg-purple-400");
          } else if (
            closestHandleStateChange.tagName.toLowerCase() === "button"
          ) {
            if (closestHandleStateChange.classList.contains("date-btn")) {
              closestHandleStateChange.classList.add(
                "scale-110",
                "bg-purple-600",
                "shadow-2xl",
                "text-white"
              );
            } else if (
              closestHandleStateChange.classList.contains("dropdown-btn")
            ) {
              closestHandleStateChange.classList.add("shadow-xl");
            }
          }
        }
      });

      overlay.addEventListener("mouseleave", function () {
        tooltip.style.display = "none";
        const parent = overlay.parentElement;
        const closestHandleStateChange = parent.querySelector(
          ".handle-state-change"
        );

        if (closestHandleStateChange) {
          if (closestHandleStateChange.tagName.toLowerCase() === "a") {
            closestHandleStateChange.classList.remove("bg-purple-400");
          } else if (
            closestHandleStateChange.tagName.toLowerCase() === "button"
          ) {
            if (closestHandleStateChange.classList.contains("date-btn")) {
              closestHandleStateChange.classList.remove(
                "scale-110",
                "bg-purple-600",
                "shadow-2xl",
                "text-white"
              );
            } else if (
              closestHandleStateChange.classList.contains("dropdown-btn")
            ) {
              closestHandleStateChange.classList.remove("shadow-xl");
            }
          }
        }
      });
    });
  } else {
    toggleClass(scheduleChangesManager, ["hidden"], ["flex"]);

    parentElements.forEach((item) => {
      item.removeAttribute("style");
      const overlays = item.querySelectorAll(".overlay-blocker");
      if (overlays) {
        overlays.forEach((overlay) => overlay.remove());
      }
      const tooltips = document.querySelectorAll(".tooltip");
      if (tooltips) {
        tooltips.forEach((tooltip) => tooltip.remove());
      }
    });
  }
}

function loadDateDiv(days, selectedDateParameter = "emptystring") {
  const activeDate =
    "handle-state-changes date-btn text-white rounded-full w-10 md:w-12 h-10 md:h-12 flex items-center justify-center bg-[#8F58DD] transition-all duration-300 hover:shadow-2xl hover:scale-110 cursor-pointer text-xs md:text-sm";
  const inactiveDate =
    "handle-state-changes date-btn text-purple-600 rounded-full w-10 md:w-12 h-10 md:h-12 flex items-center justify-center bg-[#F1F1FB] hover:bg-purple-400 transition-all duration-300 hover:shadow-2xl hover:scale-110 cursor-pointer text-xs md:text-sm";

  const sortedDays = days.sort((a, b) => new Date(a) - new Date(b));
  const today = new Date().toLocaleDateString("en-US");
  const defaultActiveDate =
    selectedDateParameter !== "emptystring"
      ? selectedDateParameter
      : sortedDays.find(
        (day) => new Date(day).toLocaleDateString("en-US") === today
      ) || sortedDays[0];

  setDefaultActiveDate(defaultActiveDate);

  document.getElementById("dateButtonsContainer").innerHTML = sortedDays
    .map((day) => {
      const dateObj = getCorrectDate(day);
      const correctedDate = new Date(dateObj);
      const formattedDate = `${correctedDate
        .getDate()
        .toString()
        .padStart(2, "0")}`;
      const formattedDay = new Date(correctedDate).toLocaleDateString("en-US", {
        weekday: "short",
      });
      const formattedMonth = new Date(correctedDate)
        .toLocaleDateString("en-US", { month: "short", timeZone: "UTC" })
        .toUpperCase();
      const isActive = day === defaultActiveDate;
      const buttonClasses = isActive ? activeDate : inactiveDate;

      return `
            <div class="flex flex-col items-center p-2">
                <button class="${buttonClasses} font-bold" data-date="${day}">
                    ${formattedMonth}</br>${formattedDate}
                </button>
                <span class="text-xs md:text-sm text-gray-700 mt-1">${formattedDay}</span>
            </div>
        `;
    })
    .join("");

  document
    .querySelectorAll("#dateButtonsContainer button")
    .forEach((button) => {
      button.addEventListener("click", () => {
        document
          .querySelectorAll("#dateButtonsContainer button")
          .forEach((btn) => {
            btn.className = inactiveDate;
          });

        button.className = activeDate;
        const selectedDate = button.getAttribute("data-date");
        setDefaultActiveDate(selectedDate);
        setVariablesToDefault();
        populateSchedulesContainer();
      });
    });
}

async function populateSchedulesContainer() {
  const scheduleChangesManager = document.getElementById(
    "scheduleChangesManager"
  );

  scheduleChangesManager.classList.add("hidden");
  scheduleChangesManager.classList.remove("flex");
  isSkillSetChanged = false;
  removedTechs = [];

  const scheduleLoader = document.getElementById("scheduleLoader");
  const schedulesContainer = document.getElementById("schedulesContainer");

  schedulesContainer.classList.add("hidden");
  scheduleLoader.classList.remove("hidden");

  const filteredData = window.data.filter(
    (item) => item.procedure_date === defaultActiveDate
  );

  if (!predictedTechs) {
    predictedTechs = await fetchschedulerspecificdata(defaultActiveDate, null);
    fetchschedulerspecificdata(
      localStorage.getItem("start-date"),
      localStorage.getItem("end-date")
    )
      .then((data) => {
        localStorage.setItem("rep_lead_changes", JSON.stringify(data));
      })
      .catch((error) => {
        console.error("Error fetching scheduler specific data:", error);
      });
  }

  scheduleLoader.classList.add("hidden");
  schedulesContainer.classList.remove("hidden");

  schedulesContainer.innerHTML = "";

  const schedules = document.createElement("div");
  schedules.classList.add(
    "custom-scrollbar-vertical",
    "schedules-parent",
    "overflow-y-auto",
    "h-full",
    "max-h-full",
    "max-w-full"
  );
  if (filteredData.length === 0) {
    schedulesContainer.innerHTML =
      "<p class='custom-scrollbar-vertical flex h-full items-center justify-center'>No Data found for the selected date </p>";
    return;
  }

  filteredData.forEach((item) => {
    const filteredPredictedTechs = predictedTechs.filter(
      (techs) =>
        item.site.id === techs.site.id &&
        item.implanting_physician.id === techs.implanting_physician.id &&
        item.procedure_date === techs.procedure_date &&
        item.start_time === techs.procedure_start_time
    );

    const card = createAllocationCard(filteredPredictedTechs, item);
    schedules.appendChild(card);
  });

  schedulesContainer.appendChild(schedules);

  const sortableSelector = ".sortable-container-techs";
  const draggableSelector = ".worker-card";

  const sortableContainers = document.querySelectorAll(sortableSelector);

  sortableContainers.forEach((container) => {
    new Sortable(container, {
      group: "kanban",
      animation: 150,
      handle: draggableSelector,
      draggable: draggableSelector,
      onEnd: function (event) {
        const draggedElement = event.item;
        const innerDiv = draggedElement.querySelector("div[mod-type]");
        if (innerDiv) {
          innerDiv.setAttribute("mod-type", "update");
        }
        isScheduleChangedFunction();
        msnry.layout();
      },
    });
  });

  document
    .querySelectorAll(".handle-state-change")
    .forEach((card) => card.classList.remove("pointer-events-none"));

  var elem = document.querySelector(".schedules-parent");
  msnry = new Masonry(elem, {
    itemSelector: ".actual-schedules",
    columnWidth: ".actual-schedules",
    percentPosition: true,
  });
}

function createAllocationCard(filteredPredictedTechs, item) {
  const parentAllocationDiv = document.createElement("div");

  parentAllocationDiv.classList.add("actual-schedules", "w-80", "md:w-80");

  const allocationDiv = document.createElement("div");
  allocationDiv.classList.add("bg-white", "mt-4");

  const allocationDivContent = `
        <div class="relative p-2 flex flex-col space-y-2 bg-[#E9E9E9] rounded-tl-[12px] rounded-tr-[12px]">
            <div class="text-black pointer-events-none">
                    <div class="text-md md:text-xl space-x-1.5 flex items-center font-semibold">
                    <span site_id="${item.site.id}" procedure_date="${item.procedure_date
    }" procedure_start_time="${item?.start_time || item?.procedure_start_time
    }" class="font-bold provider-span truncate pr-[3rem]">${item.site.name}</span>
                </div>
                <div class="text-xs font-normal">
                    <span implanting_physician_id="${item.implanting_physician.id
    }" class="location-span">${item.implanting_physician.name
    }</span>
                </div>
            </div>
            <div class="flex flex-row space-x-1">
                <img src="/svg/cases.svg">
                <div class="text-purple-600 text-xs cases-span" cases-total=${item?.cases
    }>Cases: ${item.cases}</div>
                </div>
            <button class="bg-transparent text-white absolute right-4 top-2 hover:scale-110 transition-all duration-300 ease-in-out" 
            title="Add more Clinical Specialist">
                <img src="/svg/add.svg">
            </button>
        </div>
        <div class="sortable-container-techs px-2 py-0.5 h-full max-h-full bg-[#F1F1FB]"></div>
    `;

  allocationDiv.innerHTML = allocationDivContent;

  filteredPredictedTechs.sort((a, b) => {
    const nameA = a.rep.name || "";
    const nameB = b.rep.name || "";
    return nameA.localeCompare(nameB);
  });

  filteredPredictedTechs.forEach((worker) => {
    const div_data = createWorkerCard(worker);
    if (div_data) {
      allocationDiv
        .querySelector(".sortable-container-techs")
        .appendChild(div_data);
    }
  });

  allocationDiv
    .querySelector('button[title="Add more Clinical Specialist"]')
    .addEventListener("click", () =>
      showAvailableTechs(
        item.site.name,
        item.implanting_physician.name,
        allocationDiv
      )
    );

  parentAllocationDiv.appendChild(allocationDiv);

  return parentAllocationDiv;
}

function createWorkerCard(worker) {
  let workerCardHTML = "";
  if (worker?.rep?.id || worker?.rep_id || worker?.Aoid || worker?.id) {
    workerCardHTML = `
        <div class="worker-card py-1 mb-2 flex items-center">
            <div class="flex-grow text-sm mr-2" tech-name="${worker?.rep?.name || worker?.rep_name
      }" tech-job="N/A" mod-type="${worker?.id
        ? "update"
        : worker?.Aoid
          ? "update"
          : worker?.rep.id
            ? "init"
            : worker?.rep_id
              ? "init"
              : null
      }" tech-aoid="${worker?.id || worker?.Aoid || worker.rep.id || worker?.rep_id
      }">
                ${worker?.rep?.name || worker?.rep_name}
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-2 py-1 text-black rounded-md hover:bg-purple-400 hover:text-white" title="Remove Rep">
    <img src="/svg/delete.svg" class="">
</button>
            </div>
        </div>`;
  }

  const workerCardContainer = document.createElement("div");
  workerCardContainer.innerHTML = workerCardHTML;

  const workerCard = workerCardContainer.firstElementChild;

  if (workerCard) {
    const removeButton = workerCard.querySelector('button[title="Remove Rep"]');
    removeButton.addEventListener("click", () => {
      removedTechs.push({
        Aoid: workerCard.querySelector(".flex-grow").getAttribute("tech-aoid"),
        rep_name: workerCard.querySelector(".flex-grow").textContent,
      });
      workerCard.remove();
      msnry.layout();
      isScheduleChangedFunction();
    });
  }

  return workerCard;
}

const sortTechs = (techs) => {
  return techs.sort((a, b) => a.rep_name.localeCompare(b.rep_name));
};

async function showAvailableTechs(location, provider, allocationDiv) {
  const overlay = document.createElement("div");
  overlay.className = "fixed inset-0 bg-black bg-opacity-50 z-40";

  const parentDiv = document.getElementById("content-div");
  parentDiv.appendChild(overlay);

  const panelDiv = document.createElement("div");
  panelDiv.className =
    "w-[14rem] md:w-96 fixed flex flex-col right-0 top-0 h-full bg-[#F1F1FB] shadow-lg z-50 transform transition-transform duration-300";
  parentDiv.appendChild(panelDiv);

  requestAnimationFrame(() => {
    parentDiv.classList.remove("translate-x-full");
    parentDiv.classList.add("translate-x-0");
  });

  panelDiv.innerHTML = `
        <div class="p-4 relative bg-white m-3 rounded-tl-[12px] rounded-tr-[12px]">
        <div class="space-y-2">
            <h2 class="text-lg md:text-xl font-semibold">Available Clinical Specialist</h2>
            <hr class="border">
            <p class="text-sm md:text-lg">In ${location} | For ${provider}</p>
            </div>
            <div class="relative my-4">
                <input id="search-input" type="text" placeholder="Search Reps" class="w-full p-2 border bg-[#F6F6F6] rounded-[12px] outline-none focus:outline-none">
                <button id="clear-search" class="absolute top-1/2 right-2 transform -translate-y-1/2 text-gray-500">
                    <img src="/svg/clear.svg">
                </button>
            </div>
            <button class="close-btn text-black hover:scale-110 transform transition-transform absolute top-4 right-4">
            <img src="/svg/close.svg">
            </button>
            <div class="flex justify-center items-center text-purple-600">
                <i class="text-xs">(Click a Rep to add them into the Schedule)</i>    
            </div>
        </div>
        <div class="flex-1 flex justify-center items-center loader-holder">
            <div class="loader"></div>
        </div>
        <div id="techs-container" class="gap-4 p-4 overflow-y-auto h-full hidden"></div>
    `;

  const loaderHolder = panelDiv.querySelector(".loader-holder");
  const techsContainer = panelDiv.querySelector("#techs-container");
  const searchInput = panelDiv.querySelector("#search-input");
  const clearSearchButton = panelDiv.querySelector("#clear-search");

  try {
    let availableTechs;

    availableTechs = await fetchUnscheduledTechs(defaultActiveDate);

    loaderHolder.remove();

    const allWorkerCards = Array.from(
      document.querySelectorAll("#schedulesContainer .worker-card")
    );
    const existingTechNames = allWorkerCards.map((card) =>
      card.querySelector("div[tech-name]").getAttribute("tech-name")
    );

    const recommendedTechs = sortTechs(
      availableTechs.filter(
        (tech) => !existingTechNames.includes(tech.rep_name)
      )
    );

    const createSection = (title, techs, sectionId) => {
      const techsHtml = techs
        .map(
          (tech) => `
                <div class="tech-name p-2 rounded cursor-pointer hover:bg-gray-200" data-aoid="${tech?.Aoid || tech?.id
            }">
                    <div class="text-container text-sm md:text-lg">
                        <div class="tech-name-text font-semibold text-purple-600">${tech.rep_name
            }</div>
                    </div>
                </div>
            `
        )
        .join("");

      return `
                <div class="tech-section mb-4">
                    <div class="section-header flex items-center justify-between">
                        <h3 class="text-sm md:text-lg font-semibold text-center mb-2">${title}</h3>
                            <img src="/svg/arrow.svg" alt="image" class="toggle-button cursor-pointer rotate-image" data-section-id="${sectionId}">
                        
                    </div>
                    <hr class="border">
                    <div class="section-content scale-100 translate-x-0 transition-all duration-300 mt-2" data-section-id="${sectionId}">
                        <div class="flex flex-col gap-2">
                            ${techsHtml}
                        </div>
                    </div>
                </div>
            `;
    };

    function updateTechList() {
      const searchTerm = searchInput.value.toLowerCase();

      const filterTechs = (techs) =>
        techs.filter((tech) =>
          tech.rep_name.toLowerCase().includes(searchTerm)
        );

      techsContainer.innerHTML = `
                ${removedTechs.length > 0
          ? createSection(
            "Removed Clinical Specialist",
            filterTechs(removedTechs),
            "removed"
          )
          : ""
        }
                ${recommendedTechs.length > 0
          ? createSection(
            "Recommended Clinical Specialist",
            filterTechs(recommendedTechs),
            "recommended"
          )
          : ""
        }
            `;
      techsContainer.classList.remove("hidden");
      techsContainer.classList.add("flex", "flex-col");
    }

    updateTechList();

    searchInput.addEventListener("input", updateTechList);
    clearSearchButton.addEventListener("click", () => {
      searchInput.value = "";
      updateTechList();
      searchInput.focus();
    });

    techsContainer.addEventListener("click", (event) => {
      if (event.target.closest(".tech-name")) {
        const techDiv = event.target.closest(".tech-name");
        const aoid = techDiv.dataset.aoid;
        const tech =
          availableTechs.find((t) => t.id === aoid) ||
          removedTechs.find((t) => t.Aoid === aoid.toString);
        if (tech) {
          const workerCard = createWorkerCard(tech);
          allocationDiv
            .querySelector(".sortable-container-techs")
            .appendChild(workerCard);
          isScheduleChangedFunction();
          closeOverlay();

          const techIndex = removedTechs.findIndex((t) => t.Aoid === aoid);
          if (techIndex !== -1) {
            removedTechs.splice(techIndex, 1);
          }
        }
        msnry.layout();
      }
    });
  } catch (error) {
    console.error("Error fetching techs:", error);
  }

  function closeOverlay() {
    panelDiv.classList.remove("translate-x-0");
    panelDiv.classList.add("translate-x-full");

    panelDiv.addEventListener("transitionend", () => {
      parentDiv.removeChild(panelDiv);
      parentDiv.removeChild(overlay);
    });
  }

  const closeButton = panelDiv.querySelector(".close-btn");
  closeButton.addEventListener("click", closeOverlay);

  overlay.addEventListener("click", closeOverlay);

  const handleToggleButtonClick = (event) => {
    const button = event.target;
    button.classList.toggle("rotated");
    const sectionId = button.getAttribute("data-section-id");
    const contentDiv = document.querySelector(
      `.section-content[data-section-id="${sectionId}"]`
    );

    if (contentDiv) {
      if (contentDiv.classList.contains("translate-x-full")) {
        contentDiv.classList.remove("hidden");
        setTimeout(() => {
          contentDiv.classList.remove("translate-x-full", "scale-0");
          contentDiv.classList.add("translate-x-0", "scale-100");
          button.textContent = "+";
        }, 300);
      } else {
        contentDiv.classList.remove("translate-x-0", "scale-100");
        contentDiv.classList.add("translate-x-full", "scale-0");
        button.textContent = "-";
        setTimeout(() => {
          contentDiv.classList.add("hidden");
        }, 300);
      }
    }
  };

  function attachToggleListeners() {
    const toggleButtons = document.querySelectorAll(".toggle-button");
    toggleButtons.forEach((button) => {
      button.addEventListener("click", handleToggleButtonClick);
    });
  }

  attachToggleListeners();
}

function getWeekDaysFromSunday(date) {
  const dayOfWeek = date.getDay(); // 0 (Sunday) to 6 (Saturday)
  const startOfWeek = new Date(date); // Clone the input date
  startOfWeek.setDate(date.getDate() - dayOfWeek); // Set to Sunday (start of the week)

  const weekDays = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(startOfWeek);
    day.setDate(startOfWeek.getDate() + i);
    // Format the date as YYYY-MM-DD
    const formattedDate = day.toISOString().slice(0, 10);
    weekDays.push(formattedDate);
  }
  return weekDays;
}

async function loadTechAllocation(
  event,
  selectedDateParameter = "emptystring"
) {
  setActiveNav("assignments");
  setVariablesToDefault();
  setVisibilityForContents();

  const scheduleChangesManager = document.getElementById(
    "scheduleChangesManager"
  );

  const updateBtn = document.getElementById("updateScheduleButton");
  const rescheduleBtn = document.getElementById("rerunScheduleButton");
  document.getElementById("repAllocation").classList.add("hidden");

  function redesignNavIcons() {
    const sidebar = document.getElementById("logo-sidebar");
    const linkATags = document.querySelectorAll(".link-a-tags");

    if (!sidebar.classList.contains("w-64")) {
      linkATags.forEach((el) => el.classList.add("justify-center"));
    } else {
      linkATags.forEach((el) => el.classList.remove("justify-center"));
    }
  }
  redesignNavIcons();

  document.getElementById("techCalendar").classList.add("hidden");
  document.getElementById("repproviderList").classList.add("hidden");
  document.getElementById("repAllocation").classList.remove("hidden");
  document.getElementById("repAllocation").classList.add("flex");

  const from_date = localStorage.getItem("start-date");
  const end_date = localStorage.getItem("end-date");

  if (
    !from_date ||
    from_date === "undefined" ||
    !end_date ||
    end_date === "undefined"
  ) {
    console.error("Invalid date range selected.");
    return;
  }
  const date_list = getDateList(from_date, end_date);
  document.getElementById("schedulesContainer").innerHTML = "";
  if (selectedDateParameter != 'emptystring') {
    selectedDateParameter = selectedDateParameter
  } else {
    selectedDateParameter = getCurrentOrFirstDate(date_list)
  }
  loadDateDiv(date_list, selectedDateParameter);

  result_data = await fetchschedulerdata(from_date, end_date);
  window.data = result_data["cases_details"];

  if (window.data && window.data.length > 0) {
    populateSchedulesContainer();

    updateBtn.replaceWith(updateBtn.cloneNode(true));
    const newUpdateBtn = document.getElementById("updateScheduleButton");

    newUpdateBtn.addEventListener("click", async () => {
      const loader = document.getElementById("loader-with-opacity");
      toggleClass(loader, ["flex"], ["hidden"]);

      const [schedules, calender_event_data] = extractSchedules();
      // console.log("schedules",schedules)
      // console.log("calender_event_data",calender_event_data)
      const filteredSchedules = schedules.filter(
        (schedule) => schedule.type !== "init"
      );
      // console.log("filteredSchedules",filteredSchedules)

      if (filteredSchedules) {
        const result = await updateSchedule(filteredSchedules);
        toggleClass(loader, ["hidden"], ["flex"]);

        if (result.status === "success") {
          const calender_result = calenderevent({
            assignments: calender_event_data,
          });
          scheduleChangesManager.classList.add("hidden");
          scheduleChangesManager.classList.remove("flex");

          const items = document.querySelectorAll(".handle-state-change");
          const parentElements = Array.from(items).map(
            (item) => item.parentElement
          );

          parentElements.forEach((item) => {
            item.removeAttribute("style");
            const overlays = item.querySelectorAll(".overlay-blocker");
            if (overlays) {
              overlays.forEach((overlay) => overlay.remove());
            }
            const tooltips = document.querySelectorAll(".tooltip");
            if (tooltips) {
              tooltips.forEach((tooltip) => tooltip.remove());
            }
          });

          setVariablesToDefault();
          showPopup("", "The schedule has been updated.", "bg-[#8F58DD]");
          populateSchedulesContainer();
        }
      }
    });

    rescheduleBtn.replaceWith(rescheduleBtn.cloneNode(true));
    const newRescheduleBtn = document.getElementById("rerunScheduleButton");

    newRescheduleBtn.addEventListener("click", () => {
      const from_date = localStorage.getItem("start-date");
      const end_date = localStorage.getItem("end-date");
      showRerunStaticPopup(
        `This action will override your manual schedule changes that you've made from ${from_date} to ${end_date}, including those you've saved. Are you sure you want to proceed?`,
        "bg-red-500"
      );
    });
  } else {
    populateSchedulesContainer();
  }
}
