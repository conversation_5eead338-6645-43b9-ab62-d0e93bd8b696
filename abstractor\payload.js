{"additional_history_and_risk_factors": {"cardiomyopathy_cm": {"field_id": "4565", "label": "Cardiomyopathy (CM)", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cm_type": {"field_id": "4570", "label": "CM Type", "options": [{"id": "1", "value": "Non-Ischemic"}, {"id": "2", "value": "Ischemic"}, {"id": "3", "value": "Restrictive"}, {"id": "4", "value": "Hypertrophic"}, {"id": "5", "value": "Other"}], "input_type": "select", "value": "4"}, "chronic_lung_disease": {"field_id": "4575", "label": "Chronic Lung Disease", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "sleep_apnea": {"field_id": "4580", "label": "Sleep Apnea", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "coronary_artery_disease": {"field_id": "4285", "label": "Coronary Artery Disease", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "sleep_apnea_rec_treatment_followed": {"field_id": "4585", "label": "Sleep Apnea Rec Treatment Followed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}}, "additional_stroke_and_bleeding_risk_factors": {"increased_fall_risk": {"field_id": "14793", "label": "Increased Fall Risk", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "clinically_relevant_bleeding_event": {"field_id": "14794", "label": "Clinically Relevant Bleeding Event", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "bleeding_event_type": {"field_id": "14796", "label": "Bleeding Event Type", "options": [{"id": "1", "value": "Intracranial"}, {"id": "2", "value": "Epistaxis"}, {"id": "3", "value": "Gastrointestinal"}, {"id": "4", "value": "Other"}], "input_type": "select", "value": "2"}, "genetic_coagulopathy": {"field_id": "14797", "label": "Genetic Coagulopathy", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "concurrent_anticoagulant_therapy": {"field_id": "14798", "label": "Concurrent Anticoagulant Therapy", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}}, "diagnostic_studies": {"atrial_rhythm": {"field_id": "5100", "label": "Atrial Rhythm", "options": [{"id": "1", "value": "Sinus"}, {"id": "2", "value": "AFib"}, {"id": "3", "value": "Atrial tach"}, {"id": "4", "value": "Atrial flutter"}, {"id": "5", "value": "Sinus arrest"}, {"id": "6", "value": "Atrial paced"}, {"id": "7", "value": "Not Documented"}], "input_type": "select", "value": "5"}, "lvef_assessed": {"field_id": "5110", "label": "LVEF Assessed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "lvef": {"field_id": "5115", "label": "LVEF", "input_type": "string", "value": "N/A"}, "transthoracic_echo_tte_performed": {"field_id": "5120", "label": "Transthoracic Echo (TTE) Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "date_of_tte": {"field_id": "5125", "label": "Date of TTE", "input_type": "string", "value": "12-03-24"}, "baseline_imaging_performed": {"field_id": "5170", "label": "Baseline Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "ct_performed": {"field_id": "5175", "label": "CT Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "date_of_ct": {"field_id": "5180", "label": "Date of CT", "input_type": "string", "value": "12-04-24"}, "mri_performed": {"field_id": "5185", "label": "MRI Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "date_of_mri": {"field_id": "5190", "label": "Date of MRI", "input_type": "string", "value": "13-04-25"}, "intracardiac_echo_performed": {"field_id": "14826", "label": "Intracardiac Echo Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "date_of_intracardiac_echo": {"field_id": "14827", "label": "Date of Intracardiac Echo", "input_type": "string", "value": "14-04-24"}}, "discharge": {"surgery": {"field_id": "14835", "label": "Surgery", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "percutaneous_coronary_interventions": {"field_id": "14836", "label": "Percutaneous Coronary Interventions", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "discharge_date": {"field_id": "10100", "label": "Discharge Date", "input_type": "string", "value": "12-04-24"}, "discharge_status": {"field_id": "10105", "label": "Discharge Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": "Deceased"}, "if_alive_discharge_location": {"field_id": "10110", "label": "If Alive, Discharge Location", "options": [{"id": "1", "value": "Home"}, {"id": "2", "value": "Skilled Nursing facility"}, {"id": "3", "value": "Extended care/TCU/rehab"}, {"id": "4", "value": "Other"}, {"id": "5", "value": "Other acute care hospital"}, {"id": "6", "value": "Left against medical advice (AMA)"}], "input_type": "select", "value": "Other acute care hospital"}, "if_alive_hospice_care": {"field_id": "10115", "label": "If Alive, Hospice Care", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "if_deceased_death_during_the_procedure": {"field_id": "10120", "label": "If Deceased, Death During the Procedure", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "if_deceased_cause_of_death": {"field_id": "10125", "label": "If Deceased, Cause of Death", "options": [{"id": "1", "value": "Acute myocardial infarction"}, {"id": "2", "value": "Pulmonary"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Sudden cardiac death"}, {"id": "5", "value": "Renal"}, {"id": "6", "value": "Non-cardiovascular procedure or surgery"}, {"id": "7", "value": "Heart failure"}, {"id": "8", "value": "Gastrointestinal"}, {"id": "9", "value": "<PERSON>rauma"}, {"id": "10", "value": "Stroke"}, {"id": "11", "value": "Hepatobiliary"}, {"id": "12", "value": "Suicide"}, {"id": "13", "value": "Cardiovascular procedure"}, {"id": "14", "value": "Pancreatic"}, {"id": "15", "value": "Neurological"}, {"id": "16", "value": "Cardiovascular hemorrhage"}, {"id": "17", "value": "Infection"}, {"id": "18", "value": "Malignancy"}, {"id": "19", "value": "Other cardiovascular reason"}, {"id": "20", "value": "Inflammatory/Immunologic"}, {"id": "21", "value": "Other non-cardiovascular reason"}], "input_type": "select", "value": "Heart failure"}}, "discharge_medications": {"note": "Medications prescribed at discharge are not required for patients who expired, discharged to 'Other acute care Hospital', 'AMA', or are receiving Hospice Care.", "prescribed_at_discharge": {"field_id": "10205", "label": "Prescribed at Discharge", "medications": {"fondaparinux": {"field_id": "", "label": "Fondaparinux", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "heparin_derivative": {"field_id": "", "label": "Heparin Derivative", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}, "low_molecular_weight_heparin": {"field_id": "", "label": "Low Molecular Weight Heparin", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}, "unfractionated_heparin": {"field_id": "", "label": "Unfractionated Heparin", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "warfarin": {"field_id": "", "label": "Warfarin", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "aspirin": {"field_id": "", "label": "<PERSON><PERSON><PERSON>", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason", "81-100 mg", "101-324 mg", "325 mg"], "input_type": "radio", "value": "No - No Reason"}, "aspirin_dipyridamole": {"field_id": "", "label": "<PERSON><PERSON><PERSON>", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "vorapaxar": {"field_id": "", "label": "Vorapaxar", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "apixaban": {"field_id": "", "label": "Apixaban", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "dabigatran": {"field_id": "", "label": "Dabigatran", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "edoxaban": {"field_id": "", "label": "Edoxaban", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}, "rivaroxaban": {"field_id": "", "label": "Rivaroxaban", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}, "cangrelor": {"field_id": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "clopidogrel": {"field_id": "", "label": "Clopidogrel", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}, "other_p2y12": {"field_id": "", "label": "Other P2Y12", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "prasugrel": {"field_id": "", "label": "Prasug<PERSON>", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - Pt. Reason"}, "ticagrelor": {"field_id": "", "label": "Ticagrelor", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "Yes"}, "ticlopidine": {"field_id": "", "label": "Ticlopidine", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}}}}, "epicardial_access_assessment": {"epicardial_approach_considered": {"field_id": "14824", "label": "Epicardial Approach Considered", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "medical_conditions": {"field_id": "14823", "label": "Medical Conditions", "options": [{"id": "1", "value": "Cardiac Surgery"}, {"id": "2", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "3", "value": "Epicardial Access"}, {"id": "4", "value": "Thoracic Radiation Therapy"}, {"id": "5", "value": "<PERSON><PERSON><PERSON> Excavatum"}, {"id": "6", "value": "Epigastric Surgery"}, {"id": "7", "value": "Autoimmune Disease"}, {"id": "8", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "9", "value": "Hiatal Hernia"}], "input_type": "select", "value": "6"}, "lupus_erythematosus": {"field_id": "14825", "label": "<PERSON><PERSON>sus", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}}, "episode_of_care_admission": {"arrival_date": {"field_id": "3000", "label": "Arrival Date", "input_type": "string", "value": "dscddcdcscd"}, "health_insurance": {"field_id": "3005", "label": "Health Insurance", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "payment_source": {"field_id": "3010", "label": "Payment Source", "options": [{"id": "1", "value": "Private Health Insurance"}, {"id": "2", "value": "Medicare"}, {"id": "3", "value": "Medicare Advantage"}, {"id": "4", "value": "Medicaid"}, {"id": "5", "value": "Military Health Care"}, {"id": "6", "value": "State-Specific Plan (non-Medicaid)"}, {"id": "7", "value": "Indian Health Service"}, {"id": "8", "value": "Non-US"}], "input_type": "select", "value": "Medicaid"}, "mbi": {"field_id": "12846", "label": "MBI", "input_type": "string", "value": "5236582"}, "ncdr_research_study": {"field_id": "3020", "label": "NCDR Research Study", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "study_name": {"field_id": "3025", "label": "Study Name", "input_type": "string", "value": "test"}, "patient_id": {"field_id": "3030", "label": "Patient ID", "input_type": "string", "value": "452365"}, "admitted_for_laa_occlusion_intervention": {"field_id": "14791", "label": "Admitted for LAA Occlusion Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}, "follow_up": {"assessment_date": {"field_id": "11000", "label": "Assessment Date", "input_type": "string", "value": "bgb"}, "follow_up_interval": {"field_id": "14851", "label": "Follow-up Interval", "options": [{"id": "1", "value": "45 day"}, {"id": "2", "value": "6 month"}, {"id": "3", "value": "1 year"}, {"id": "4", "value": "2 year"}], "input_type": "select", "value": ""}, "reference_episode_arrival_date": {"field_id": "14946", "label": "Reference Episode Arrival Date", "input_type": "string", "value": "fgbgfb"}, "reference_episode_discharge_date": {"field_id": "14338", "label": "Reference Episode Discharge Date", "input_type": "string", "value": "12-02-24"}, "reference_procedure_start_date_time": {"field_id": "11001", "label": "Reference Procedure Start Date/Time", "input_type": "string", "value": "12-02-24"}, "method_to_determine_follow_up_status": {"field_id": "11003", "label": "Method(s) to Determine Follow-up Status", "options": [{"id": "1", "value": "Office Visit"}, {"id": "2", "value": "Medical Records"}, {"id": "3", "value": "Letter from Medical Provider"}, {"id": "4", "value": "Phone Call"}, {"id": "5", "value": "Social Security Death Master File"}, {"id": "6", "value": "Hospitalized"}, {"id": "7", "value": "Other"}], "input_type": "select", "value": ""}, "follow_up_status": {"field_id": "11004", "label": "Follow-up Status", "options": [{"id": "1", "value": "Alive"}, {"id": "2", "value": "Deceased"}, {"id": "3", "value": "Lost to Follow-up"}], "input_type": "select", "value": ""}, "date_of_death": {"field_id": "11006", "label": "Date of Death", "input_type": "string", "value": "12-02-24"}, "cause_of_death": {"field_id": "11007", "label": "Cause of Death", "options": [{"id": "1", "value": "Acute myocardial infarction"}, {"id": "2", "value": "Sudden cardiac death"}, {"id": "3", "value": "Heart failure"}, {"id": "4", "value": "Stroke"}, {"id": "5", "value": "Cardiovascular procedure"}, {"id": "6", "value": "Cardiovascular hemorrhage"}, {"id": "7", "value": "Other cardiovascular reason"}, {"id": "8", "value": "Pulmonary"}, {"id": "9", "value": "Renal"}, {"id": "10", "value": "Gastrointestinal"}, {"id": "11", "value": "Hepatobiliary"}, {"id": "12", "value": "Pancreatic"}, {"id": "13", "value": "Infection"}, {"id": "14", "value": "Inflammatory/Immunologic"}, {"id": "15", "value": "Hemorrhage"}, {"id": "16", "value": "Non-cardiovascular procedure or surgery"}, {"id": "17", "value": "<PERSON>rauma"}, {"id": "18", "value": "Suicide"}, {"id": "19", "value": "Neurological"}, {"id": "20", "value": "Malignancy"}, {"id": "21", "value": "Other non-cardiovascular reason"}], "input_type": "select", "value": "Pulmonary"}, "barthel_index_evaluation": {"barthel_index_evaluation_performed": {"field_id": "14891", "label": "Barthel Index Evaluation Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "feeding": {"field_id": "14892", "label": "Feeding", "options": [{"id": "1", "value": "Unable"}, {"id": "2", "value": "Needs Help"}, {"id": "3", "value": "Independent"}], "input_type": "select", "value": "Independent"}, "bathing": {"field_id": "14893", "label": "Bathing", "options": [{"id": "1", "value": "Dependent"}, {"id": "2", "value": "Independent"}], "input_type": "select", "value": "Independent"}, "grooming": {"field_id": "14894", "label": "Grooming", "options": [{"id": "1", "value": "Needs Help"}, {"id": "2", "value": "Independent"}], "input_type": "select", "value": "Independent"}, "dressing": {"field_id": "14895", "label": "Dressing", "options": [{"id": "1", "value": "Dependent"}, {"id": "2", "value": "Needs Help"}, {"id": "3", "value": "Independent"}], "input_type": "select", "value": "Independent"}, "bowels": {"field_id": "14896", "label": "Bowels", "options": [{"id": "1", "value": "Incontinent"}, {"id": "2", "value": "Inconsistent"}, {"id": "3", "value": "Continent"}], "input_type": "select", "value": "Inconsistent"}, "bladder": {"field_id": "14897", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": "1", "value": "Incontinent"}, {"id": "2", "value": "Inconsistent"}, {"id": "3", "value": "Continent"}], "input_type": "select", "value": "Inconsistent"}, "toilet_use": {"field_id": "14898", "label": "Toilet Use", "options": [{"id": "1", "value": "Dependent"}, {"id": "2", "value": "Needs Help"}, {"id": "3", "value": "Independent"}], "input_type": "select", "value": "Needs Help"}, "transfers": {"field_id": "14899", "label": "Transfers", "options": [{"id": "1", "value": "Unable"}, {"id": "2", "value": "Major <PERSON><PERSON> Needed"}, {"id": "3", "value": "Minor Assist Needed"}, {"id": "4", "value": "Independent"}], "input_type": "select", "value": "Independent"}, "mobility": {"field_id": "14900", "label": "Mobility", "options": [{"id": "1", "value": "Immobile"}, {"id": "2", "value": "Wheelchair"}, {"id": "3", "value": "One Person Assist"}, {"id": "4", "value": "Independent"}], "input_type": "select", "value": "Independent"}, "stairs": {"field_id": "14901", "label": "Stairs", "options": [{"id": "1", "value": "Unable"}, {"id": "2", "value": "Needs Help"}, {"id": "3", "value": "Independent"}], "input_type": "select", "value": "Independent"}}}, "follow_up_diagnostic_studies": {"lvef_assessed": {"field_id": "14858", "label": "LVEF Assessed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "lvef": {"field_id": "13690", "label": "LVEF", "input_type": "string", "value": ""}, "transthoracic_echo_performed": {"field_id": "14859", "label": "Transthoracic Echo (TTE) Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_tte": {"field_id": "14873", "label": "Date of TTE", "input_type": "string", "value": ""}, "transesophageal_echo_performed_tee": {"field_id": "14874", "label": "Transesophageal Echo Performed (TEE)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_tee": {"field_id": "14875", "label": "Date of TEE", "input_type": "string", "value": ""}, "cardiac_ct_performed": {"field_id": "14876", "label": "Cardiac CT Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_cardiac_ct": {"field_id": "14877", "label": "Date of Cardiac CT", "input_type": "string", "value": ""}, "cardiac_mri_performed": {"field_id": "14878", "label": "Cardiac MRI Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_cardiac_mri": {"field_id": "14879", "label": "Date of Cardiac MRI", "input_type": "string", "value": ""}, "intracardiac_echo_performed": {"field_id": "14880", "label": "Intracardiac Echo Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_intracardiac_echo": {"field_id": "14881", "label": "Date of Intracardiac Echo", "input_type": "string", "value": ""}, "atrial_thrombus_detected": {"field_id": "14882", "label": "Atrial Thrombus Detected", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "device_margin_residual_leak": {"field_id": "14884", "label": "<PERSON><PERSON> Residual Leak", "options": [{"id": "1", "value": "Not Assessed"}], "input_type": "select", "value": ""}}, "follow_up_physical_exam_and_labs": {"creatinine": {"field_id": "14886", "label": "Creatinine", "options": [{"id": "1", "value": "Not Drawn"}], "input_type": "radio", "value": ""}, "hemoglobin": {"field_id": "14888", "label": "Hemoglobin", "options": [{"id": "1", "value": "Not Drawn"}], "input_type": "radio", "value": ""}, "modified_rankin_scale_mrs": {"field_id": "13148", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "6: Death"}, {"id": "8", "value": "Not Administered"}], "input_type": "select", "value": ""}}, "follow_up_adjudication": {"follow_up_adjudication": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": "dc"}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": "cdsc"}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": "csdc"}, "reference_procedure_start_date_time": {"field_id": "11001", "label": "Reference Procedure Start Date/Time", "input_type": "string", "value": "dcdsc"}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": "dcdc"}, "study_patient_id": {"field_id": "3030", "label": "Study Patient ID", "input_type": "string", "value": "dcdc"}, "follow_up_event": {"field_id": "14967", "label": "Follow-up Event", "input_type": "string", "value": "dcdsc"}, "follow_up_event_date": {"field_id": "14386", "label": "Follow-up Event Date", "input_type": "string", "value": "dcds"}}, "neurologic": {"follow_up_adjudication_status": {"field_id": "14969", "label": "Follow-up Adjudication Status", "input_type": "radio", "options": ["Alive", "Deceased"], "value": "Deceased"}, "date_of_death": {"field_id": "14970", "label": "Date of Death", "input_type": "string", "value": "cdc"}, "symptom_onset_date": {"field_id": "14976", "label": "Symptom Onset Date", "input_type": "string", "value": "dscd"}, "neurologic_deficit_with_rapid_onset": {"field_id": "14977", "label": "Neurologic Deficit with Rapid Onset", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "neurologic_deficit_clinical_presentation": {"field_id": "14978", "label": "Neurologic Deficit Clinical Presentation", "input_type": "radio", "options": ["Stroke-related", "Non-Stroke-related"], "value": "Non-Stroke-related"}, "diagnosis_confirmation_by_neurology": {"field_id": "14979", "label": "Diagnosis Confirmation by Neurology", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "brain_imaging_performed": {"field_id": "14980", "label": "Brain Imaging Performed", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "brain_imaging_type": {"field_id": "14981", "label": "Brain Imaging Type", "input_type": "select", "options": [{"id": "1", "value": "Cerebral Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Other"}], "value": "Computed Tomography"}, "deficit_type": {"field_id": "14982", "label": "Deficit Type", "input_type": "select", "options": [{"id": "1", "value": "No deficit"}, {"id": "2", "value": "Infarction"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Both"}], "value": "Both"}, "hemorrhage_type": {"field_id": "14983", "label": "Hemorrhage Type", "input_type": "select", "options": [{"id": "1", "value": "Intracerebral"}, {"id": "2", "value": "Subarachnoid"}, {"id": "3", "value": "Subdural"}], "value": "Subarachnoid"}, "subsequent_iv_rtpa_administered": {"field_id": "14984", "label": "Subsequent IV rtPA Administered", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "subsequent_endovascular_therapeutic_intervention": {"field_id": "14985", "label": "Subsequent Endovascular Therapeutic Intervention", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "symptoms_duration": {"field_id": "14986", "label": "Symptoms Duration", "input_type": "select", "options": [{"id": "1", "value": "< 1 Hour"}, {"id": "2", "value": "1 – 24 Hours"}, {"id": "3", "value": "> 24 Hours"}], "value": ""}, "trauma": {"field_id": "14987", "label": "<PERSON>rauma", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "modified_rankin_scale_mrs": {"field_id": "14988", "label": "Modified Rankin Scale (mRS)", "input_type": "select", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "6: Death"}, {"id": "8", "value": "Not Administered"}], "value": ""}, "procedure_related_neurologic_event": {"field_id": "14990", "label": "Procedure Related Neurologic Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}, "device_related_neurologic_event": {"field_id": "15015", "label": "Device Related Neurologic Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}}, "bleeding": {"follow_up_adjudication_status": {"field_id": "14971", "label": "Follow-up Adjudication Status", "input_type": "radio", "options": ["Alive", "Deceased"], "value": "Deceased"}, "adjudication_date_of_death": {"field_id": "14972", "label": "Adjudication Date of Death", "input_type": "string", "value": "sdcdc"}, "invasive_intervention_required": {"field_id": "14991", "label": "Invasive Intervention Required", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "rbc_transfusion": {"field_id": "14992", "label": "RBC Transfusion", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "number_of_rbc_units_transfused": {"field_id": "14993", "label": "Number of RBC Units Transfused", "input_type": "string", "value": "csdcd"}, "hemoglobin_pre_transfusion": {"field_id": "14994", "label": "Hemoglobin Pre-Transfusion", "input_type": "string", "value": "sdcd"}, "diagnostic_imaging_performed": {"field_id": "14995", "label": "Diagnostic Imaging Performed", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "end_organ_damage": {"field_id": "14996", "label": "End Organ Damage", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "bleeding_event_readmission": {"field_id": "14975", "label": "Bleeding Event Readmission", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "major_surgery_within_past_30_days": {"field_id": "14997", "label": "Major Surgery within Past 30 days", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "percutaneous_coronary_intervention_within_past_30_days": {"field_id": "14998", "label": "Percutaneous Coronary Intervention within Past 30 days", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "procedure_related_bleeding_event": {"field_id": "14999", "label": "Procedure Related Bleeding Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}, "device_related_bleeding_event": {"field_id": "15000", "label": "<PERSON>ce Related Bleeding Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}}, "systemic_thromboembolism": {"follow_up_adjudication_status": {"field_id": "14973", "label": "Follow-up Adjudication Status", "input_type": "radio", "options": ["Alive", "Deceased"], "value": "Alive"}, "date_of_death": {"field_id": "14974", "label": "Date of Death", "input_type": "string", "format": "mm/dd/yyyy", "value": "12-02-24"}, "death_cause": {"field_id": "15016", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "focal_end_organ_hypoperfusion_present": {"field_id": "15001", "label": "Focal End-Organ Hypoperfusion Present", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "systemic_thromboembolization_imaging_evidence": {"field_id": "15002", "label": "Systemic Thromboembolization Imaging Evidence", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "imaging_method": {"field_id": "15003", "label": "Imaging Method", "input_type": "select", "options": [{"id": "1", "value": "Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Ultrasound"}, {"id": "5", "value": "Other Imaging"}], "value": ""}, "therapeutic_intervention_performed": {"field_id": "15004", "label": "Therapeutic Intervention Performed", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "intervention_type": {"field_id": "15005", "label": "Intervention Type", "input_type": "select", "options": [{"id": "1", "value": "Catheter"}, {"id": "2", "value": "Pharmacological"}, {"id": "3", "value": "Surgical"}, {"id": "4", "value": "Other"}], "value": ""}}, "current_medications_at_time_of_event": {"field_id": "15007", "label": "Current Medications at Time of Event", "medications": {"fondaparinux": {"field_id": "15006", "label": "Fondaparinux", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "heparin_derivative": {"field_id": "", "label": "Heparin Derivative", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "low_molecular_weight_heparin": {"field_id": "", "label": "Low Molecular Weight Heparin", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "unfractionated_heparin": {"field_id": "", "label": "Unfractionated Heparin", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "warfarin": {"field_id": "", "label": "Warfarin", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "aspirin_81_100_mg": {"field_id": "", "label": "Aspirin (81-100 mg)", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "aspirin_101_324_mg": {"field_id": "", "label": "Aspirin (101-324 mg)", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "aspirin_325_mg": {"field_id": "", "label": "Aspirin 325 mg", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "aspirin_dipyridamole": {"field_id": "", "label": "Aspirin/Dipyridamole", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "vorapaxar": {"field_id": "", "label": "Vorapaxar", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "apixaban": {"field_id": "", "label": "Apixaban", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "dabigatran": {"field_id": "", "label": "Dabigatran", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "edoxaban": {"field_id": "", "label": "Edoxaban", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "rivaroxaban": {"field_id": "", "label": "Rivaroxaban", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "cangrelor": {"field_id": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "clopidogrel": {"field_id": "", "label": "Clopidogrel", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "other_p2y12": {"field_id": "", "label": "Other P2Y12", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "prasugrel": {"field_id": "", "label": "Prasug<PERSON>", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}, "ticagrelor": {"field_id": "", "label": "Ticagrelor", "input_type": "radio", "options": ["Yes", "No"], "value": "Yes"}, "ticlopidine": {"field_id": "", "label": "Ticlopidine", "input_type": "radio", "options": ["Yes", "No"], "value": "No"}}}}, "follow_up_anticoagulation_therapy": {"warfarin_discontinued": {"field_id": "14951", "label": "Warfarin Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": "No - Not Discontinued"}, "if_yes_date": {"field_id": "14966", "label": "If Yes, Date", "format": "mm / dd / yyyy", "input_type": "string", "value": "12-09-25"}, "warfarin_resumed": {"field_id": "14953", "label": "Warfarin Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": "No"}, "doac_discontinued": {"field_id": "14955", "label": "DOAC Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": "Yes - Discontinued"}, "doac_resumed": {"field_id": "14957", "label": "DOAC Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": "Yes (Other)"}, "aspirin_discontinued": {"field_id": "14959", "label": "<PERSON><PERSON><PERSON> Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": "Yes - Discontinued"}, "aspirin_resumed": {"field_id": "14961", "label": "Aspirin Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": "No"}, "p2y12_discontinued": {"field_id": "14963", "label": "P2Y12 Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": "No - Not Discontinued"}, "p2y12_resumed": {"field_id": "14965", "label": "P2Y12 Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": "Yes (Other)"}}, "follow_up_events": {"follow_up_event": {"field_id": "14948", "label": "Follow-up Event", "input_type": "string", "value": "10-04-25"}, "event_occurred": {"field_id": "14276", "label": "Event Occurred", "input_type": "string", "value": "10-04-25"}, "event_dates": {"field_id": "14277", "label": "Event Date(s)", "input_type": "string", "value": "10-04-25"}, "cardiovascular": {"endocarditis": {"field_id": "", "label": "Endocarditis", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "iatrogenic_asd": {"field_id": "", "label": "Iatrogenic ASD (requiring intervention)", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "laa_occlusion_reintervention": {"field_id": "", "label": "LAA Occlusion Reintervention", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "myocardial_infarction": {"field_id": "", "label": "Myocardial Infarction", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "pci": {"field_id": "", "label": "PCI", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "pericarditis": {"field_id": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "unplanned_cardiac_surgery": {"field_id": "", "label": "Unplanned Cardiac Surgery", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "unplanned_intervention": {"field_id": "", "label": "Unplanned Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}, "systemic": {"deep_vein_thrombosis": {"field_id": "", "label": "Deep Vein Thrombosis", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "new_requirement_for_dialysis": {"field_id": "", "label": "New Requirement for Dialysis", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "non_device_related_readmission": {"field_id": "", "label": "Non-Device Related Readmission", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "systemic_thromboembolism": {"field_id": "", "label": "Systemic Thromboembolism (other than stroke) (COMPLETE ADJUDICATION)", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}, "device": {"device_explant": {"field_id": "", "label": "Device Explant", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "device_fracture": {"field_id": "", "label": "Device Fracture", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "device_migration": {"field_id": "", "label": "<PERSON><PERSON>", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "device_related_readmission": {"field_id": "", "label": "Device Related Readmission", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "device_systemic_embolism": {"field_id": "", "label": "Device Systemic Embolism", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "device_thrombus": {"field_id": "", "label": "<PERSON><PERSON>", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}, "neurologic": {"hemorrhagic_stroke": {"field_id": "", "label": "Hemorrhagic Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "intracranial_hemorrhage": {"field_id": "", "label": "Intracranial Hemorrhage (other than hemorrhagic stroke)", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "ischemic_stroke": {"field_id": "", "label": "Ischemic Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "tia": {"field_id": "", "label": "TIA", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "undetermined_stroke": {"field_id": "", "label": "Undetermined Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}}, "bleeding_complete_adjudication": {"access_site_bleeding": {"field_id": "14948", "label": "Access Site Bleeding", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "gi_bleeding": {"field_id": "", "label": "GI Bleeding", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "hematoma": {"field_id": "", "label": "Hematoma", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "hemothorax_not_requiring_drainage": {"field_id": "", "label": "Hemothorax (not requiring drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "hemothorax_requiring_drainage": {"field_id": "", "label": "Hemothorax (requiring drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "other_hemorrhage_non_intracranial": {"field_id": "", "label": "Other Hemorrhage (non-intracranial)", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "pericardial_effusion_requiring_open_cardiac_surgery": {"field_id": "", "label": "Pericardial Effusion (requiring open cardiac surgery)", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "pericardial_effusion_with_tamponade_requiring_percutaneous_drainage": {"field_id": "", "label": "Pericardial Effusion with tamponade (requiring percutaneous drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}, "pericardial_effusion_without_tamponade_requiring_percutaneous_drainage": {"field_id": "", "label": "Pericardial Effusion without tamponade (requiring percutaneous drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "retroperitoneal_bleeding": {"field_id": "", "label": "Retroperitoneal Bleeding", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "vascular_complications": {"field_id": "", "label": "Vascular Complications", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}}, "peripheral_vascular": {"av_fistula_requiring_surgical_repair": {"field_id": "", "label": "AV Fistula (requiring surgical repair)", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "pseudoaneurysm_requiring_endovascular_repair": {"field_id": "", "label": "Pseudoaneurysm (requiring endovascular repair)", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "pseudoaneurysm_requiring_surgical_repair": {"field_id": "", "label": "Pseudoaneurysm (requiring surgical repair)", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}, "pseudoaneurysm_requiring_thrombin_injection_only": {"field_id": "", "label": "Pseudoaneurysm (requiring thrombin injection only)", "input_type": "radio", "options": ["No", "Yes"], "value": "No"}}, "pulmonary": {"pulmonary_embolism": {"field_id": "", "label": "Pulmonary Embolism", "input_type": "radio", "options": ["No", "Yes"], "value": "Yes"}}}, "follow_up_medications": {"medication": {"field_id": "1990", "label": "Medication", "options": [{"id": "1", "value": "Fondaparinux"}, {"id": "2", "value": "Heparin Derivative"}, {"id": "3", "value": "Low Molecular Weight Heparin"}, {"id": "4", "value": "Unfractionated Heparin"}, {"id": "5", "value": "Warfarin"}, {"id": "6", "value": "<PERSON><PERSON><PERSON>"}, {"id": "7", "value": "Aspirin/Dipyridamole"}, {"id": "8", "value": "Vorapaxar"}, {"id": "9", "value": "Apixaban"}, {"id": "10", "value": "Dabigatran"}, {"id": "11", "value": "Edoxaban"}, {"id": "12", "value": "Rivaroxaban"}, {"id": "13", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "14", "value": "Clopidogrel"}, {"id": "15", "value": "Other P2Y12"}, {"id": "16", "value": "Prasug<PERSON>"}, {"id": "17", "value": "Ticagrelor"}, {"id": "18", "value": "Ticlopidine"}], "input_type": "select", "value": "Aspirin/Dipyridamole"}, "current_medications_at_time_of_follow_up": {"field_id": "14949", "label": "Current Medications at Time of Follow-up", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": "No - No Reason"}, "if_yes_dose": {"field_id": "14950", "label": "If Yes, <PERSON><PERSON>", "options": [{"id": "1", "value": "81-100 MG"}, {"id": "2", "value": "101-324 MG"}, {"id": "3", "value": "325 MG"}], "input_type": "select", "value": "325 MG"}}, "history_and_risk_factors": {"cha2ds2_vasc_risk_scores": {"cha2ds2_vasc_congestive_heart_failure": {"field_id": "4005", "label": "CHA2DS2-VASc Congestive Heart Failure", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "nyha_functional_classification": {"field_id": "4010", "label": "NYHA Functional Classification", "options": [{"id": "1", "value": "Class I"}, {"id": "2", "value": "Class II"}, {"id": "3", "value": "Class III"}, {"id": "4", "value": "Class IV"}], "input_type": "select", "value": "Class II"}, "cha2ds2_vasc_lv_dysfunction": {"field_id": "4015", "label": "CHA2DS2-VASc LV Dysfunction", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cha2ds2_vasc_thromboembolic_event": {"field_id": "4040", "label": "CHA2DS2-VASc Thromboembolic Event", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cha2ds2_vasc_hypertension": {"field_id": "4020", "label": "CHA2DS2-VASc Hypertension", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cha2ds2_vasc_vascular_disease": {"field_id": "4045", "label": "CHA2DS2-VASc Vascular Disease", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cha2ds2_vasc_diabetes_mellitus": {"field_id": "4025", "label": "CHA2DS2-VASc Diabetes Mellitus", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "vascular_disease_type": {"field_id": "4050", "label": "Vascular Disease Type", "options": [{"id": "1", "value": "Prior MI"}, {"id": "2", "value": "PAD"}, {"id": "3", "value": "Known Aortic Plaque"}, {"id": "4", "value": "CAD"}, {"id": "5", "value": "PCI"}, {"id": "6", "value": "CABG"}, {"id": "7", "value": "Carotid Artery Disease"}], "input_type": "select", "value": "1"}, "cha2ds2_vasc_stroke": {"field_id": "4030", "label": "CHA2DS2-VASc Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cha2ds2_vasc_tia": {"field_id": "4035", "label": "CHA2DS2-VASc TIA", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}, "has_bled_risk_scores": {"has_bled_hypertension_uncontrolled": {"field_id": "4055", "label": "HAS-BLED Hypertension (Uncontrolled)", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_bleeding": {"field_id": "4095", "label": "HAS-BLED Bleeding", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_abnormal_renal_function": {"field_id": "4060", "label": "HAS-BLED Abnormal Renal Function", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_labile_inr": {"field_id": "4100", "label": "HAS-BLED Labile INR", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_abnormal_liver_function": {"field_id": "4065", "label": "HAS-BLED Abnormal Liver Function", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_alcohol": {"field_id": "4105", "label": "HAS-BLED Alcohol", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_stroke": {"field_id": "4070", "label": "HAS-BLED Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_stroke_type": {"field_id": "14792", "label": "HAS-BLED Stroke Type", "options": [{"id": "1", "value": "Hemorrhagic"}, {"id": "2", "value": "Ischemic"}, {"id": "3", "value": "Undetermined"}], "input_type": "select", "value": "1"}, "has_bled_drugs_antiplatelet": {"field_id": "4110", "label": "HAS-BLED Drugs - Antiplatelet", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "has_bled_drugs_nsaids": {"field_id": "4115", "label": "HAS-BLED Drugs - NSAIDS", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}}, "history_interventions": {"cardiac_structural_intervention": {"field_id": "14802", "label": "Cardiac Structural Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "cardiac_structural_intervention_type": {"field_id": "14803", "label": "Cardiac Structural Intervention Type", "options": [{"id": "1", "value": "Aortic Balloon Valvuloplasty"}, {"id": "2", "value": "Transcatheter Aortic Valve Replacement (TAVR)"}, {"id": "3", "value": "AV Replacement – Surgical"}, {"id": "4", "value": "AV Repair – Surgical"}, {"id": "5", "value": "Mitral Balloon Valvuloplasty"}, {"id": "6", "value": "Transcatheter Mitral Valve Repair (TMVR)"}, {"id": "7", "value": "MV Replacement – Surgical"}, {"id": "8", "value": "MV Repair – Surgical"}, {"id": "9", "value": "Mitral Annuloplasty Ring – Surgical"}, {"id": "10", "value": "Mitral Transcatheter – Valve-in-valve"}, {"id": "11", "value": "ASD Closure"}, {"id": "12", "value": "PFO Closure"}, {"id": "13", "value": "Pulmonic Replacement"}, {"id": "14", "value": "Pulmonic Repair"}, {"id": "15", "value": "Tricuspid Replacement"}, {"id": "16", "value": "Tricuspid Repair"}], "input_type": "select", "value": "AV Replacement – Surgical"}, "left_atrial_appendage_occlusion_intervention": {"field_id": "14804", "label": "Left Atrial Appendage Occlusion Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "left_atrial_appendage_intervention_type": {"field_id": "14806", "label": "Left Atrial Appendage Intervention Type", "options": [{"id": "1", "value": "Epicardial Ligation"}, {"id": "2", "value": "Surgical Amputation"}, {"id": "3", "value": "Surgical Ligation"}, {"id": "4", "value": "Percutaneous Occlusion"}, {"id": "5", "value": "Surgical Closure Device"}, {"id": "6", "value": "Surgical Stapling"}], "input_type": "select", "value": "Surgical Stapling"}}, "history_rhythm_history": {"atrial_fibrillation": {"field_id": "13709", "label": "Atrial Fibrillation", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "atrial_fibrillation_classification": {"field_id": "4400", "label": "Atrial Fibrillation Classification", "options": [{"id": "1", "value": "Paroxysmal"}, {"id": "2", "value": "Persistent"}, {"id": "3", "value": "Long standing persistent"}, {"id": "4", "value": "Permanent"}], "input_type": "select", "value": "Permanent"}, "valvular_atrial_fibrillation": {"field_id": "4380", "label": "Valvular Atrial Fibrillation", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "hx_of_rheumatic_valve_disease": {"field_id": "14799", "label": "Hx of Rheumatic Valve Disease", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "hx_of_mitral_valve_replacement": {"field_id": "4385", "label": "Hx of Mitral Valve Replacement", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "mechanical_valve_in_mitral_position": {"field_id": "4390", "label": "Mechanical Valve in Mitral Position", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "hx_of_mitral_valve_repair": {"field_id": "4395", "label": "Hx of Mitral Valve Repair", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "attempt_at_atrial_fibrillation_termination": {"field_id": "4410", "label": "Attempt at Atrial Fibrillation Termination", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "pharmacologic_cardioversion": {"field_id": "4415", "label": "Pharmacologic Cardioversion", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "dc_cardioversion": {"field_id": "4420", "label": "DC Cardioversion", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "catheter_ablation": {"field_id": "4425", "label": "Catheter Ablation", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "most_recent_catheter_ablation_date": {"field_id": "4430", "label": "Most Recent Catheter Ablation Date", "input_type": "string", "value": "12-01-25"}, "prior_ablation_strategy": {"field_id": "4435", "label": "Prior Ablation Strategy(s)", "input_type": "string", "value": "Yes"}, "surgical_ablation": {"field_id": "4440", "label": "Surgical Ablation", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "most_recent_surgical_ablation_date": {"field_id": "4445", "label": "Most Recent Surgical Ablation Date", "input_type": "string", "value": "12-01-25"}, "atrial_flutter": {"field_id": "4450", "label": "Atrial Flutter", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "atrial_flutter_classification": {"field_id": "4455", "label": "Atrial Flutter Classification", "options": [{"id": "1", "value": "Typical/Cavotricuspid Isthmus (CTI) Dependent"}, {"id": "2", "value": "Atypical"}], "input_type": "select", "value": "Atypical"}, "attempt_at_atrial_flutter_termination": {"field_id": "4460", "label": "Attempt at Atrial Flutter Termination", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "pharmacologic_cardioversion_flutter": {"field_id": "4465", "label": "Pharmacologic Cardioversion (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "dc_cardioversion_flutter": {"field_id": "4470", "label": "DC Cardioversion (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "catheter_ablation_flutter": {"field_id": "4475", "label": "Catheter Ablation (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "most_recent_catheter_ablation_date_flutter": {"field_id": "4480", "label": "Most Recent Catheter Ablation Date (Flutter)", "input_type": "string", "value": "11-01-24"}}, "in_hospital_adjudication": {"demographics": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": "sefsfsdfdsf"}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": "sdfsdfdf"}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": "sdfsf"}, "procedure_start_date": {"field_id": "7001", "label": "Procedure Start Date", "input_type": "string", "value": "09-06-24"}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": "456"}, "study_patient_id": {"field_id": "3030", "label": "Study Patient ID", "input_type": "string", "value": "26"}, "adjudication_event": {"field_id": "14312", "label": "Adjudication Event", "input_type": "string", "value": "09-06-24"}, "adjudication_event_date": {"field_id": "14313", "label": "Adjudication Event Date", "input_type": "string", "value": "09-06-24"}}, "neurologic": {"adjudication_status": {"field_id": "14902", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": "Alive"}, "adjudication_date_of_death": {"field_id": "14903", "label": "Adjudication Date of Death", "input_type": "string", "value": "09-06-24"}, "symptom_onset_date": {"field_id": "14904", "label": "Symptom Onset Date", "input_type": "string", "value": "09-06-24"}, "neurologic_deficit_with_rapid_onset": {"field_id": "14905", "label": "Neurologic Deficit with Rapid Onset", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "neurologic_deficit_clinical_presentation": {"field_id": "14906", "label": "Neurologic Deficit Clinical Presentation", "options": ["Stroke-related", "Non-Stroke-related"], "input_type": "radio", "value": "Stroke-related"}, "diagnosis_confirmation_by_neurology": {"field_id": "14907", "label": "Diagnosis Confirmation by Neurology", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "brain_imaging_performed": {"field_id": "14908", "label": "Brain Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "brain_imaging_type": {"field_id": "14909", "label": "Brain Imaging Type", "options": [{"id": "1", "value": "Cerebral Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Other"}], "input_type": "select", "value": ""}, "deficit_type": {"field_id": "14910", "label": "Deficit Type", "options": [{"id": "1", "value": "No deficit"}, {"id": "2", "value": "Infarction"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Both"}], "input_type": "select", "value": ""}, "hemorrhagic_stroke_type": {"field_id": "14911", "label": "Hemorrhagic Stroke Type", "options": [{"id": "1", "value": "Intracerebral"}, {"id": "2", "value": "Subarachnoid"}, {"id": "3", "value": "Subdural"}], "input_type": "select", "value": ""}, "subsequent_iv_rtpa_administered": {"field_id": "14912", "label": "Subsequent IV rtPA Administered", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "subsequent_endovascular_therapeutic_intervention": {"field_id": "14913", "label": "Subsequent Endovascular Therapeutic Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "symptoms_duration": {"field_id": "14914", "label": "Symptoms Duration", "options": [{"id": "1", "value": "< 1 Hour"}, {"id": "2", "value": "1 – 24 Hours"}, {"id": "3", "value": "> 24 Hours"}], "input_type": "select", "value": ""}, "trauma": {"field_id": "14915", "label": "<PERSON>rauma", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "modified_rankin_scale_mrs": {"field_id": "14916", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "6: Death"}], "input_type": "select", "value": "3: Moderate disability"}, "not_administered": {"field_id": "14917", "label": "Not Administered", "input_type": "string", "value": "09-06-24"}, "procedure_related_neurologic_event": {"field_id": "14918", "label": "Procedure Related Neurologic Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "Possible"}, "device_related_neurologic_event": {"field_id": "14931", "label": "Device Related Neurologic Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "Possible"}}, "bleeding": {"adjudication_status": {"field_id": "14924", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": "Deceased"}, "adjudication_date_of_death": {"field_id": "14930", "label": "Adjudication Date of Death", "input_type": "string", "value": "09-06-24"}, "invasive_intervention_required": {"field_id": "14929", "label": "Invasive Intervention Required", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "rbc_transfusion": {"field_id": "14919", "label": "RBC Transfusion", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "number_of_rbc_units_transfused": {"field_id": "14920", "label": "Number of RBC Units Transfused", "input_type": "string", "value": "465"}, "hemoglobin_pre_transfusion": {"field_id": "14921", "label": "Hemoglobin Pre-Transfusion", "input_type": "string", "value": "09-06-24"}, "diagnostic_imaging_performed": {"field_id": "14922", "label": "Diagnostic Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "end_organ_damage": {"field_id": "14923", "label": "End Organ Damage", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "major_surgery_within_past_30_days": {"field_id": "14927", "label": "Major Surgery within Past 30 days", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "percutaneous_coronary_intervention_within_past_30_days": {"field_id": "14928", "label": "Percutaneous Coronary Intervention within Past 30 days", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "procedure_related_bleeding_event": {"field_id": "14925", "label": "Procedure Related Bleeding Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "Possible"}, "device_related_bleeding_event": {"field_id": "14926", "label": "<PERSON>ce Related Bleeding Event", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "input_type": "select", "value": "Unlikely"}}}, "intra_or_post_procedure_events": {"general_events": {"event": {"field_id": "12153", "label": "Event", "input_type": "string", "value": "test"}, "event_occurred": {"field_id": "9002", "label": "Event Occurred", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "event_dates": {"field_id": "14275", "label": "Event Date(s)", "format": "mm/dd/yyyy", "input_type": "string", "value": "12-04-24"}}, "peripheral_vascular": {"av_fistula_no_intervention_required": {"field_id": "", "label": "AV Fistula (no intervention required)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "av_fistula_requiring_surgical_repair": {"field_id": "", "label": "AV Fistula (requiring surgical repair)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pseudoaneurysm_no_intervention_required": {"field_id": "", "label": "Pseudoaneurysm (no intervention required)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pseudoaneurysm_requiring_endovascular_repair": {"field_id": "", "label": "Pseudoaneurysm (requiring endovascular repair)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "pseudoaneurysm_requiring_surgical_repair": {"field_id": "", "label": "Pseudoaneurysm (requiring surgical repair)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pseudoaneurysm_requiring_thrombin_injection": {"field_id": "", "label": "Pseudoaneurysm (requiring thrombin injection only)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}}, "neurologic": {"hemorrhagic_stroke": {"field_id": "", "label": "Hemorrhagic Stroke", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "intracranial_hemorrhage": {"field_id": "", "label": "Intracranial Hemorrhage (other than hemorrhagic stroke)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "ischemic_stroke": {"field_id": "", "label": "Ischemic Stroke", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "tia": {"field_id": "", "label": "TIA", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "undetermined_stroke": {"field_id": "", "label": "Undetermined Stroke", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}}, "bleeding": {"access_site_bleeding": {"field_id": "", "label": "Access Site Bleeding", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "gi_bleeding": {"field_id": "", "label": "GI Bleeding", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "hematoma": {"field_id": "", "label": "Hematoma", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "hemothorax_no_drainage": {"field_id": "", "label": "Hemothorax (not requiring drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "hemothorax_requiring_drainage": {"field_id": "", "label": "Hemothorax (requiring drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "other_hemorrhage_non_intracranial": {"field_id": "", "label": "Other Hemorrhage (non-intracranial)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "pericardial_effusion_open_cardiac_surgery": {"field_id": "", "label": "Pericardial Effusion (requiring open cardiac surgery)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pericardial_effusion_tamponade_percutaneous": {"field_id": "", "label": "Pericardial Effusion with tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pericardial_effusion_without_tamponade": {"field_id": "", "label": "Pericardial Effusion without tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "retroperitoneal_bleeding": {"field_id": "", "label": "Retroperitoneal Bleeding", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "vascular_complications": {"field_id": "", "label": "Vascular Complications", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}}, "pulmonary": {"pleural_effusion": {"field_id": "", "label": "Pleural Effusion", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pneumonia": {"field_id": "", "label": "Pneumonia", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}, "pneumothorax_no_intervention": {"field_id": "", "label": "Pneumothorax (no intervention required)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pneumothorax_requiring_intervention": {"field_id": "", "label": "Pneumothorax (requiring intervention)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "pulmonary_embolism": {"field_id": "", "label": "Pulmonary Embolism", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "Yes"}, "respiratory_failure": {"field_id": "", "label": "Respiratory Failure", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": "No"}}}, "demographics": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": "<PERSON>"}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": "<PERSON>"}, "middle_name": {"field_id": "2020", "label": "Middle Name", "input_type": "string", "value": "K"}, "ssn": {"field_id": "2030", "label": "SSN", "input_type": "string", "value": "***********"}, "ssn_na": {"field_id": "2031", "label": "SSN NA", "options": ["True", "False"], "input_type": "radio", "value": "True"}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": "6756cd8eb6ce792aed7c6e7c"}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": "ID98766"}, "birth_date": {"field_id": "2050", "label": "Birth Date", "input_type": "string", "value": "1954-09-11"}, "sex": {"field_id": "2060", "label": "Sex", "options": ["Male", "Female"], "input_type": "radio", "value": "Male"}, "patient_zip_code": {"field_id": "2065", "label": "Patient Zip Code", "input_type": "string", "value": "97212"}, "patient_zip_code_na": {"field_id": "2066", "label": "Patient Zip Code NA", "options": ["True", "False"], "input_type": "radio", "value": "True"}, "hispanic_or_latino_ethnicity": {"field_id": "2076", "label": "Hispanic or Latino Ethnicity", "options": ["True", "False"], "input_type": "radio", "value": "False"}, "race": {"field_id": "unknown", "label": "Race", "options": [{"id": "1", "value": "White"}, {"id": "2", "value": "Black/African American"}, {"id": "3", "value": "Asian"}, {"id": "4", "value": "American Indian/Alaskan Native"}, {"id": "5", "value": "Native Hawaiian/Pacific Islander"}], "input_type": "select", "value": "Native Hawaiian/Pacific Islander"}}, "physical_exam_and_labs": {"height": {"field_id": "6000", "label": "Height", "metric": "cm", "input_type": "string", "value": "156"}, "weight": {"field_id": "6005", "label": "Weight", "metric": "kg", "input_type": "string", "value": "56"}, "pulse": {"field_id": "6010", "label": "Pulse", "metric": "bpm", "input_type": "string", "value": "156"}, "blood_pressure": {"field_id": "6015/6020", "label": "Blood Pressure", "metric": "mmHg", "input_type": "string", "value": "120"}, "hemoglobin": {"field_id": "6030", "label": "Hemoglobin", "options": [{"id": "1", "value": "g/dL"}, {"id": "2", "value": "Not Drawn"}], "input_type": "select", "value": "Not Drawn"}, "prothrombin_time": {"field_id": "6040", "label": "Prothrombin Time (PT)", "options": [{"id": "1", "value": "sec"}, {"id": "2", "value": "Not Drawn"}], "input_type": "select", "value": "Not Drawn"}, "inr": {"field_id": "6045", "label": "INR", "options": [{"id": "1", "value": "Not Drawn"}], "input_type": "string", "value": "415636"}, "creatinine": {"field_id": "6050", "label": "Creatinine", "options": [{"id": "1", "value": "mg/dL"}, {"id": "2", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "albumin": {"field_id": "14210", "label": "Albumin", "options": [{"id": "1", "value": "g/dL"}, {"id": "2", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "platelet_count": {"field_id": "13213", "label": "Platelet Count", "options": [{"id": "1", "value": "µL"}, {"id": "2", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "modified_rankin_scale": {"field_id": "14805", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "Not Administered"}], "input_type": "select", "value": ""}}, "post_procedure_labs": {"peak_creatinine": {"field_id": "14868", "label": "Peak Creatinine", "metric": "mg/dL", "not_drawn_field_id": "14870", "input_type": "string", "value": "45"}, "hemoglobin_lowest": {"field_id": "14871", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lowest)", "metric": "g/dL", "not_drawn_field_id": "14872", "input_type": "string", "value": "16"}, "creatinine_at_discharge": {"field_id": "14869", "label": "Creatinine (at Discharge)", "metric": "mg/dL", "not_drawn_field_id": "14867", "input_type": "string", "value": "23"}}, "pre_procedure_diagnostics": {"transesophageal_echocardiogram_tee_performed": {"field_id": "14828", "label": "Transesophageal Echocardiogram (TEE) Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "most_recent_tee_date": {"field_id": "14829", "label": "Most Recent TEE Date", "input_type": "string", "value": "12-04-24"}, "atrial_thrombus_detected": {"field_id": "14838", "label": "Atrial Thrombus Detected", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "laa_orifice_maximal_width": {"field_id": "14830", "label": "LAA Orifice Maximal Width", "input_type": "string", "value": "56"}}, "pre_procedure_medications": {"fondaparinux": {"field_id": "6985", "label": "Fondaparinux", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Current"}, "heparin_derivative": {"field_id": "6985", "label": "Heparin Derivative", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "low_molecular_weight_heparin": {"field_id": "6985", "label": "Low Molecular Weight Heparin", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Current"}, "unfractionated_heparin": {"field_id": "6985", "label": "Unfractionated Heparin", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Current"}, "warfarin": {"field_id": "6985", "label": "Warfarin", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "aspirin_81_100_mg": {"field_id": "6985", "label": "Aspirin (81-100 mg)", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Never"}, "aspirin_101_324_mg": {"field_id": "6985", "label": "Aspirin (101-324 mg)", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "aspirin_325_mg": {"field_id": "6985", "label": "Aspirin 325 mg", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "aspirin_dipyridamole": {"field_id": "6985", "label": "Aspirin/Dipyridamole", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Never"}, "vorapaxar": {"field_id": "6985", "label": "Vorapaxar", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "apixaban": {"field_id": "6985", "label": "Apixaban", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Never"}, "dabigatran": {"field_id": "6985", "label": "Dabigatran", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Never"}, "edoxaban": {"field_id": "6985", "label": "Edoxaban", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Never"}, "rivaroxaban": {"field_id": "6985", "label": "Rivaroxaban", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "cangrelor": {"field_id": "6985", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "clopidogrel": {"field_id": "6985", "label": "Clopidogrel", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Current"}, "other_p2y12": {"field_id": "6985", "label": "Other P2Y12", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "prasugrel": {"field_id": "6985", "label": "Prasug<PERSON>", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Never"}, "ticagrelor": {"field_id": "6985", "label": "Ticagrelor", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}, "ticlopidine": {"field_id": "6985", "label": "Ticlopidine", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "Held"}}, "procedureInfo": {"procedure": {"procedure_start_date_and_time": {"field_id": "7000", "label": "Procedure Start Date and Time", "input_type": "text", "value": "12-04-24"}, "procedure_stop_date_and_time": {"field_id": "7005", "label": "Procedure Stop Date and Time", "input_type": "text", "value": "12-04-24"}, "operator_name_npi": {"field_id": "14861, 14860, 14862/14863", "label": "Operator Name/NPI", "input_type": "text", "value": "test"}, "fellow_name_npi_fellowship_program_id": {"field_id": "15433, 15434, 15435/15436/15431", "label": "Fellow Name/NPI/Fellowship Program ID", "input_type": "text", "value": "test"}, "shared_decision_making": {"field_id": "14732", "label": "Shared Decision Making", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "sdm_tool_used": {"field_id": "14733", "label": "SDM Tool Used", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "sdm_tool_name": {"field_id": "14734", "label": "SDM Tool Name", "input_type": "text", "value": "test"}, "procedure_location": {"field_id": "12871", "label": "Procedure Location", "options": [{"id": "1", "value": "OR"}, {"id": "2", "value": "Hybrid OR"}, {"id": "3", "value": "Cath Lab"}, {"id": "4", "value": "Hybrid Cath Lab"}, {"id": "5", "value": "EP Lab"}], "input_type": "select", "value": "Hybrid Cath Lab"}, "sedation": {"field_id": "7130", "label": "Sedation", "options": [{"id": "1", "value": "Minimal Sedation/Anxiolysis"}, {"id": "2", "value": "Deep Sedation/Analgesia"}, {"id": "3", "value": "Moderate Sedation/Analgesia (Conscious Sedation)"}, {"id": "4", "value": "General Anesthesia"}], "input_type": "select", "value": "General Anesthesia"}, "laa_occlusion_indication": {"field_id": "14837", "label": "LAA Occlusion Indication", "options": [{"id": "1", "value": "High fall risk"}, {"id": "2", "value": "History of major bleed"}, {"id": "3", "value": "Clinically significant bleeding risk (Other than those listed here)"}, {"id": "4", "value": "Increased thromboembolic stroke risk"}, {"id": "5", "value": "Labile INR"}, {"id": "6", "value": "Non-compliance with anticoagulation therapy"}, {"id": "7", "value": "Patient preference"}], "input_type": "select", "value": "Clinically significant bleeding risk (Other than those listed here)"}, "procedure_canceled": {"field_id": "14834", "label": "Procedure Canceled", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "procedure_canceled_reason": {"field_id": "14833", "label": "Procedure Canceled Reason", "options": [{"id": "1", "value": "Anatomy not conducive for implant"}, {"id": "2", "value": "Appendage too large (for device implant)"}, {"id": "3", "value": "Appendage too small (for device implant)"}, {"id": "4", "value": "Catheterization challenge"}, {"id": "5", "value": "Decompensation in patient condition"}, {"id": "6", "value": "Epicardial access issue"}, {"id": "7", "value": "<PERSON><PERSON><PERSON><PERSON> detected"}, {"id": "8", "value": "Unanticipated patient condition"}, {"id": "9", "value": "Patient/Family choice"}], "input_type": "select", "value": "Unanticipated patient condition"}}, "device_information": {"access_system": {"field_id": "14839", "label": "Access System", "input_type": "text", "value": "test"}, "device": {"field_id": "14841", "label": "<PERSON><PERSON>", "input_type": "text", "value": "test"}, "udi": {"field_id": "14843", "label": "UDI", "input_type": "text", "value": "562"}, "laa_isolation_approach": {"field_id": "14844", "label": "LAA Isolation Approach", "options": ["Epicardial", "Percutaneous"], "input_type": "radio", "value": "Percutaneous"}, "device_successfully_deployed": {"field_id": "14968", "label": "<PERSON>ce Successfully Deployed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "reason_device_not_deployed_successfully": {"field_id": "14845", "label": "Reason Device Not Deployed Successfully", "options": [{"id": "1", "value": "Deployed, not released"}, {"id": "2", "value": "Not deployed"}, {"id": "3", "value": "<PERSON><PERSON> retrieved"}], "input_type": "select", "value": "<PERSON><PERSON> retrieved"}}, "procedure_aborted": {"procedure_aborted": {"field_id": "14831", "label": "Procedure Aborted", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "procedure_aborted_reason": {"field_id": "14832", "label": "Procedure Aborted Reason", "options": [{"id": "1", "value": "Anatomy not conducive for implant"}, {"id": "2", "value": "Appendage too large (for device implant)"}, {"id": "3", "value": "Appendage too small (for device implant)"}, {"id": "4", "value": "Catheterization challenge"}, {"id": "5", "value": "Decompensation in patient condition"}, {"id": "6", "value": "Device related"}, {"id": "7", "value": "Transcatheter device retrieval"}, {"id": "8", "value": "Device release criteria not met"}, {"id": "9", "value": "Epicardial access issue"}, {"id": "10", "value": "Surgical device retrieval"}, {"id": "11", "value": "Device associated thrombus developed during procedure"}, {"id": "12", "value": "Unanticipated patient condition"}, {"id": "13", "value": "Patient/Family choice"}], "input_type": "select", "value": "Epicardial access issue"}}, "device_margin_residual_leak": {"device_margin_residual_leak": {"field_id": "14848", "label": "<PERSON><PERSON> Residual Leak", "input_type": "text", "value": "96"}, "not_assessed": {"field_id": "14849", "label": "Not Assessed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}}, "guidance_method": {"guidance_method": {"field_id": "7200", "label": "Guidance Method(s)", "options": [{"id": "1", "value": "Intracardiac 3D Echo"}, {"id": "2", "value": "Electro Anatomic Mapping"}, {"id": "3", "value": "Fluoroscopy"}, {"id": "4", "value": "Transesophageal Echo (TEE)"}], "input_type": "select", "value": "Fluoroscopy"}}, "conversion_to_open_heart_surgery": {"conversion_to_open_heart_surgery": {"field_id": "14846", "label": "Conversion to Open Heart Surgery", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "reason": {"field_id": "14847", "label": "Reason", "options": [{"id": "1", "value": "Complication"}, {"id": "2", "value": "Device Retrieval"}, {"id": "3", "value": "Unfavorable Anatomy"}, {"id": "4", "value": "Medical decision for open ligation of appendage"}], "input_type": "select", "value": "Unfavorable Anatomy"}}, "concomitant_procedure": {"concomitant_procedure_performed": {"field_id": "14855", "label": "Concomitant Procedure(s) Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "concomitant_procedure_type": {"field_id": "14857", "label": "Concomitant Procedure Type", "options": [{"id": "1", "value": "AFib Ablation"}, {"id": "2", "value": "ICD"}, {"id": "3", "value": "PCI"}, {"id": "4", "value": "TAVR"}, {"id": "5", "value": "TMVR"}, {"id": "6", "value": "ASD Closure Congenital"}, {"id": "7", "value": "ASD Closure Iatrogenic"}, {"id": "8", "value": "PFO Closure Congenital"}], "input_type": "select", "value": "ASD Closure Iatrogenic"}}, "device_systemic_embolization": {"device_systemic_embolization_surgical_retrieval": {"field_id": "14856", "label": "Device Systemic Embolization (surgical retrieval)", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}}}, "systemic_thromboembolism": {"adjudication_status": {"field_id": "14932", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": "Deceased"}, "adjudication_date_of_death": {"field_id": "14933", "label": "Adjudication Date of Death", "format": "mm/dd/yyyy", "input_type": "string", "value": "12-04-23"}, "death_cause": {"field_id": "14934", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "focal_end_organ_hypoperfusion_present": {"field_id": "14935", "label": "Focal End-Organ Hypoperfusion Present", "options": ["No", "Yes"], "input_type": "radio", "value": "Yes"}, "systemic_thromboembolization_imaging_evidence": {"field_id": "14939", "label": "Systemic Thromboembolization Imaging Evidence", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "imaging_method": {"field_id": "14936", "label": "Imaging Method", "options": [{"id": "1", "value": "Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Ultrasound"}, {"id": "5", "value": "Other Imaging"}], "input_type": "select", "value": "Magnetic Resonance Imaging"}, "therapeutic_intervention_performed": {"field_id": "14937", "label": "Therapeutic Intervention Performed", "options": ["No", "Yes"], "input_type": "radio", "value": "No"}, "intervention_type": {"field_id": "14938", "label": "Intervention Type", "options": [{"id": "1", "value": "Catheter"}, {"id": "2", "value": "Pharmacological"}, {"id": "3", "value": "Surgical"}, {"id": "4", "value": "Other"}], "input_type": "select", "value": "Other"}, "current_medications_at_time_of_event": {"field_id": "14941", "label": "Current Medications at Time of Event", "medications": {"fondaparinux": {"field_id": "14940", "label": "Fondaparinux", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "heparin_derivative": {"field_id": "", "label": "Heparin Derivative", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "low_molecular_weight_heparin": {"field_id": "", "label": "Low Molecular Weight Heparin", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "unfractionated_heparin": {"field_id": "", "label": "Unfractionated Heparin", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "warfarin": {"field_id": "", "label": "Warfarin", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "aspirin_81_100_mg": {"field_id": "", "label": "<PERSON><PERSON><PERSON> 81-100mg", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "aspirin_101_324_mg": {"field_id": "", "label": "Aspirin 101-324mg", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "aspirin_325_mg": {"field_id": "", "label": "Aspirin 325mg", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "aspirin_dipyridamole": {"field_id": "", "label": "<PERSON><PERSON><PERSON>", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "vorapaxar": {"field_id": "", "label": "Vorapaxar", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "apixaban": {"field_id": "", "label": "Apixaban", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "dabigatran": {"field_id": "", "label": "Dabigatran", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "edoxaban": {"field_id": "", "label": "Edoxaban", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "rivaroxaban": {"field_id": "", "label": "Rivaroxaban", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "cangrelor": {"field_id": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "clopidogrel": {"field_id": "", "label": "Clopidogrel", "options": ["Yes", "No"], "input_type": "radio", "value": "No"}, "other_p2y12": {"field_id": "", "label": "Other p2y12", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "prasugrel": {"field_id": "", "label": "Prasug<PERSON>", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "ticagrelor": {"field_id": "", "label": "Ticagrelor", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}, "ticlopidine": {"field_id": "", "label": "Ticlopidine", "options": ["Yes", "No"], "input_type": "radio", "value": "Yes"}}}}}