import {
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  // Check if all fields are filled - currently not used but kept for future use
  // let allFilled = true;
  // Object.entries(data).forEach(([key, field]) => {
  //   if (key === "verified") return;
  //   // If any field's value is missing or empty, set allFilled to false.
  //   if (!field.value || field.value.toString().trim() === "") {
  //     allFilled = false;
  //   }
  // });
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

export function renderPhysicalExamAndLabs(patientData) {
  const container = document.getElementById("physicalExamAndLabs");
  container.innerHTML = ""; // Clear the container before rendering new tiles
  container.style.position = "relative"; // Ensures the verified checkbox is positioned within the section

  // Identify fields that are dependencies or have dependencies
  const skipFields = new Set();

  // First pass: find all fields with dependencies and their dependent fields
  Object.entries(patientData.physical_exam_and_labs).forEach(
    ([fieldKey, value]) => {
      if (value.dependency) {
        // This field has a dependency, find the field it depends on
        const dependentFieldKey = Object.keys(
          patientData.physical_exam_and_labs
        ).find(
          (k) =>
            patientData.physical_exam_and_labs[k].field_id === value.dependency
        );

        if (dependentFieldKey) {
          // Add the dependent field to the skip list
          skipFields.add(dependentFieldKey);
          // Also add the field with dependency to the skip list
          // as we don't want to render it as a separate tile
          skipFields.add(fieldKey);
        }
      }
    }
  );

  // Render each physical exam and lab field.
  Object.entries(patientData.physical_exam_and_labs).forEach(([key, value]) => {
    // Skip container-level verified field.
    if (key === "verified") return;

    // Skip fields that are dependencies of other fields
    if (skipFields.has(key)) return;

    // Create an outer wrapper to hold the field tile and the modified_by display.
    const tileWrapper = document.createElement("div");
    tileWrapper.classList.add("tile-wrapper");
    tileWrapper.style.position = "relative";
    tileWrapper.style.marginBottom = "24px";

    // Create the field container.
    const fieldContainer = document.createElement("div");
    fieldContainer.classList.add("field-container");

    // Create and append the label element.
    const label = document.createElement("label");
    label.classList.add("label", "cursor-pointer");
    label.textContent = value.metric
      ? `${value.label} (${value.metric}) (${value.field_id})`
      : `${value.label} (${value.field_id})`;
    if (value.description) {
      label.setAttribute("title", value.description);
    }
    fieldContainer.appendChild(label);

    // Create the modified_by display element.
    const modifiedByDisplay = document.createElement("span");
    modifiedByDisplay.style.display = "block";
    modifiedByDisplay.style.textAlign = "right";
    modifiedByDisplay.style.marginTop = "-10px";
    modifiedByDisplay.style.color = "#8143d9";
    modifiedByDisplay.style.fontSize = "12px";
    if (!value.modified_by) {
      value.modified_by = "";
    }
    modifiedByDisplay.textContent = value.modified_by;

    // Process input based on input_type.
    if (value.input_type === "string" || value.input_type === "text") {
      // Regular string input without dependency
      const input = document.createElement("input");
      input.type = "text";
      input.name = key;
      input.placeholder = `Enter ${value.label}`;
      input.value = value.value || "";

      updateTileStyle(fieldContainer, input.value);

      let previousValue = input.value;

      input.addEventListener("input", (e) => {
        const currentValue = e.target.value;

        // Use validation utilities with temporary callback
        let isValid = false;
        validateStringInput(currentValue, value.field_id, (validatedValue) => {
          isValid = true;
          previousValue = validatedValue;

          // Update model and UI only if value is valid
          patientData.physical_exam_and_labs[key].value = validatedValue;
          patientData.physical_exam_and_labs[key].modified_by = "ABSTRACTOR";
          updateTileStyle(fieldContainer, validatedValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.physical_exam_and_labs
          );

          // Update input value if validation modified it
          if (validatedValue !== currentValue) {
            e.target.value = validatedValue;
          }
        });

        // Revert to previous value if validation failed
        if (!isValid) {
          e.target.value = previousValue;
        }
      });

      fieldContainer.appendChild(input);
    } else if (value.input_type === "date") {
      const dateWrapper = document.createElement("div");
      dateWrapper.style.position = "relative";

      // Create display input
      const displayInput = document.createElement("input");
      displayInput.type = "text";
      displayInput.name = `${key}_display`;
      displayInput.readOnly = true;
      displayInput.value = formatDisplayDate(value.value);
      displayInput.placeholder = "MM/DD/YYYY";
      displayInput.style.cursor = "pointer";

      // Hidden date input
      const dateInput = document.createElement("input");
      dateInput.type = "date";
      dateInput.name = key;
      dateInput.value = value.value || "";
      dateInput.style.position = "absolute";
      dateInput.style.opacity = "0";
      dateInput.style.cursor = "pointer";

      // Set max date to today
      const today = new Date().toISOString().split("T")[0];
      dateInput.max = today;

      updateTileStyle(fieldContainer, dateInput.value);

      dateInput.addEventListener("change", (e) => {
        const selectedDate = e.target.value;
        displayInput.value = formatDisplayDate(selectedDate);
        patientData.physical_exam_and_labs[key].value = selectedDate;
        patientData.physical_exam_and_labs[key].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, selectedDate);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.physical_exam_and_labs);
      });

      // Trigger date picker when clicking display input
      displayInput.addEventListener("click", () => {
        dateInput.showPicker();
      });

      dateWrapper.appendChild(displayInput);
      dateWrapper.appendChild(dateInput);
      fieldContainer.appendChild(dateWrapper);
    } else if (value.input_type === "radio") {
      const radioContainer = document.createElement("div");
      radioContainer.classList.add("radio-container");
      value.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        const input = document.createElement("input");
        input.type = "radio";
        input.name = key;
        input.value = option;
        input.id = `${key}-${option}`;
        input.checked = value.value === option;
        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            patientData.physical_exam_and_labs[key].value = e.target.value;
            patientData.physical_exam_and_labs[key].modified_by = "ABSTRACTOR";
            updateTileStyle(fieldContainer, e.target.value);
            modifiedByDisplay.textContent = "ABSTRACTOR";
            updateContainerHighlight(
              container,
              patientData.physical_exam_and_labs
            );
          }
        });
        const optionLabel = document.createElement("label");
        optionLabel.setAttribute("for", `${key}-${option}`);
        optionLabel.innerText = option;
        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });
      updateTileStyle(fieldContainer, value.value || "");
      fieldContainer.appendChild(radioContainer);
    } else if (value.input_type === "select") {
      const select = document.createElement("select");
      select.name = key;
      if (!value.value) {
        const defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.innerText = "Select an option";
        defaultOption.disabled = true;
        defaultOption.selected = true;
        select.appendChild(defaultOption);
      }
      value.options.forEach((option) => {
        const optionElement = document.createElement("option");
        optionElement.value = option.id;
        optionElement.innerText = option.value;
        if (value.value && value.value === option.value) {
          optionElement.selected = true;
        }
        select.appendChild(optionElement);
      });
      const selectedOption = value.options.find(
        (option) => option.value === value.value
      );
      updateTileStyle(
        fieldContainer,
        selectedOption ? selectedOption.value : ""
      );
      select.addEventListener("change", (e) => {
        const selOption = value.options.find(
          (option) => option.id === e.target.value
        );
        const newValue = selOption ? selOption.value : "";
        patientData.physical_exam_and_labs[key].value = newValue;
        patientData.physical_exam_and_labs[key].modified_by = "ABSTRACTOR";
        updateTileStyle(fieldContainer, newValue);
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.physical_exam_and_labs);
      });
      fieldContainer.appendChild(select);
    }

    // Append field container and modified_by display to the tile wrapper.
    tileWrapper.appendChild(fieldContainer);
    tileWrapper.appendChild(modifiedByDisplay);
    container.appendChild(tileWrapper);
  });

  // Render dependency fields as combined fields
  Object.entries(patientData.physical_exam_and_labs).forEach(
    ([fieldKey, value]) => {
      // Skip fields that don't have dependencies or are already rendered
      if (!value.dependency || !skipFields.has(fieldKey)) return;

      // Find the dependent field (N/A checkbox)
      const dependentFieldKey = Object.keys(
        patientData.physical_exam_and_labs
      ).find(
        (k) =>
          patientData.physical_exam_and_labs[k].field_id === value.dependency
      );

      if (!dependentFieldKey) return;

      const dependentField =
        patientData.physical_exam_and_labs[dependentFieldKey];
      if (dependentField.input_type !== "checkbox") return;

      // Create an outer wrapper to hold the field tile and the modified_by display
      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";

      // Create the field container
      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");

      // Create and append the label element
      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.textContent = value.metric
        ? `${value.label} (${value.metric}) (${value.field_id})`
        : `${value.label} (${value.field_id})`;
      if (value.description) {
        label.setAttribute("title", value.description);
      }
      fieldContainer.appendChild(label);

      // Create the modified_by display element
      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.marginTop = "-10px";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";
      if (!value.modified_by) {
        value.modified_by = "";
      }
      modifiedByDisplay.textContent = value.modified_by;

      // Create a wrapper for the input and checkbox to display them side by side
      const inputWrapper = document.createElement("div");
      inputWrapper.style.display = "flex";
      inputWrapper.style.alignItems = "center";
      inputWrapper.style.gap = "10px";

      // Create the appropriate input based on input_type
      let input;

      if (value.input_type === "select" && value.options) {
        // Create a select dropdown
        input = document.createElement("select");
        input.name = fieldKey;
        input.style.flexGrow = "1";

        // Add a default option
        if (!value.value) {
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.innerText = "Select an option";
          defaultOption.disabled = true;
          defaultOption.selected = true;
          input.appendChild(defaultOption);
        }

        // Add options from the data
        value.options.forEach((option) => {
          const optionElement = document.createElement("option");
          optionElement.value = option.id;
          optionElement.innerText = option.value;
          if (value.value && value.value === option.value) {
            optionElement.selected = true;
          }
          input.appendChild(optionElement);
        });
      } else {
        // Create a text input for other types
        input = document.createElement("input");
        input.type = "text";
        input.name = fieldKey;
        input.placeholder = `Enter ${value.label}`;
        input.value = value.value || "";
        input.style.flexGrow = "1";
      }

      // Create the N/A checkbox
      const checkboxWrapper = document.createElement("div");
      checkboxWrapper.style.display = "flex";
      checkboxWrapper.style.alignItems = "center";

      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.name = dependentFieldKey;
      checkbox.id = `${dependentFieldKey}-checkbox`;
      checkbox.checked = dependentField.value === "True";

      const checkboxLabel = document.createElement("label");
      checkboxLabel.setAttribute("for", `${dependentFieldKey}-checkbox`);
      checkboxLabel.innerText = `${dependentField.label} (${dependentField.field_id})`|| "N/A";
      checkboxLabel.style.marginLeft = "5px";

      checkboxWrapper.appendChild(checkbox);
      checkboxWrapper.appendChild(checkboxLabel);

      // Set initial state based on current values
      if (dependentField.value === "True") {
        input.disabled = true;
        if (value.input_type === "select") {
          // For select, reset to first option
          if (input.options.length > 0) {
            input.selectedIndex = 0;
          }
        } else {
          // For text input, clear the value
          input.value = "";
        }
        patientData.physical_exam_and_labs[fieldKey].value = "";
      }

      updateTileStyle(
        fieldContainer,
        value.value || (dependentField.value === "True" ? "True" : "")
      );

      // Add appropriate event listeners based on input type
      if (value.input_type === "select") {
        // For select fields
        input.addEventListener("change", (e) => {
          const selectedOption = value.options.find(
            (option) => option.id.toString() === e.target.value
          );
          const newValue = selectedOption ? selectedOption.value : "";

          // Update model and UI
          patientData.physical_exam_and_labs[fieldKey].value = newValue;
          patientData.physical_exam_and_labs[fieldKey].modified_by =
            "ABSTRACTOR";

          // If select has value, disable the checkbox
          if (newValue) {
            checkbox.checked = false;
            patientData.physical_exam_and_labs[dependentFieldKey].value = "";
          }

          updateTileStyle(fieldContainer, newValue);
          modifiedByDisplay.textContent = "ABSTRACTOR";
          updateContainerHighlight(
            container,
            patientData.physical_exam_and_labs
          );
        });
      } else {
        // For text input fields
        let previousValue = input.value;

        input.addEventListener("input", (e) => {
          const currentValue = e.target.value;

          // Use validation utilities with temporary callback
          let isValid = false;
          validateStringInput(
            currentValue,
            value.field_id,
            (validatedValue) => {
              isValid = true;
              previousValue = validatedValue;

              // Update model and UI only if value is valid
              patientData.physical_exam_and_labs[fieldKey].value =
                validatedValue;
              patientData.physical_exam_and_labs[fieldKey].modified_by =
                "ABSTRACTOR";

              // If input has value, disable the checkbox
              if (validatedValue) {
                checkbox.checked = false;
                patientData.physical_exam_and_labs[dependentFieldKey].value =
                  "";
              }

              updateTileStyle(fieldContainer, validatedValue);
              modifiedByDisplay.textContent = "ABSTRACTOR";
              updateContainerHighlight(
                container,
                patientData.physical_exam_and_labs
              );

              // Update input value if validation modified it
              if (validatedValue !== currentValue) {
                e.target.value = validatedValue;
              }
            }
          );

          // Revert to previous value if validation failed
          if (!isValid) {
            e.target.value = previousValue;
          }
        });
      }

      checkbox.addEventListener("change", (e) => {
        const isChecked = e.target.checked;

        // Update the dependent field value
        patientData.physical_exam_and_labs[dependentFieldKey].value = isChecked
          ? "True"
          : "";
        patientData.physical_exam_and_labs[dependentFieldKey].modified_by =
          "ABSTRACTOR";

        // If checkbox is checked, disable and clear the input
        if (isChecked) {
          input.disabled = true;
          if (value.input_type === "select") {
            // For select, reset to first option
            if (input.options.length > 0) {
              input.selectedIndex = 0;
            }
          } else {
            // For text input, clear the value
            input.value = "";
          }
          patientData.physical_exam_and_labs[fieldKey].value = "";
        } else {
          input.disabled = false;
        }

        updateTileStyle(fieldContainer, isChecked ? "True" : "");
        modifiedByDisplay.textContent = "ABSTRACTOR";
        updateContainerHighlight(container, patientData.physical_exam_and_labs);
      });

      // Append elements to the wrapper
      inputWrapper.appendChild(input);
      inputWrapper.appendChild(checkboxWrapper);
      fieldContainer.appendChild(inputWrapper);

      // Append field container and modified_by display to the tile wrapper
      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);
      container.appendChild(tileWrapper);
    }
  );

  // Integrate container-level verified checkbox.
  if (
    typeof patientData.physical_exam_and_labs.verified !== "object" ||
    patientData.physical_exam_and_labs.verified === null
  ) {
    patientData.physical_exam_and_labs.verified = {
      value: patientData.physical_exam_and_labs.verified || "False",
      modified_by: "",
    };
  }
  const verifiedData = patientData.physical_exam_and_labs.verified;
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";
  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "physicalExamAndLabs-verified-checkbox";
  containerVerifiedCheckbox.checked = verifiedData.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";
  containerVerifiedCheckbox.addEventListener("change", (e) => {
    verifiedData.value = e.target.checked ? "True" : "False";
    updateContainerHighlight(container, patientData.physical_exam_and_labs);
  });
  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "physicalExamAndLabs-verified-checkbox"
  );
  containerVerifiedLabel.classList.add("mt-2", "ml-2");
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";
  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);
  container.appendChild(verifiedContainer);

  // Initial update of container highlight.
  updateContainerHighlight(container, patientData.physical_exam_and_labs);
}
