<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>AtriAI</title>
  <link rel="icon" href="../svg/welcome-icon.svg" type="image/x-icon" />
  <script src="https://cdn.tailwindcss.com"></script>


  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>

  <style>
    .container {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .upload-section {
      text-align: center;
      padding: 20px;
      border: 2px dashed #ccc;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .controls {
      margin-bottom: 20px;
    }

    select {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
      font-size: 14px;
      width: 200px;
    }

    .resource-section {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .resource-header {
      background-color: #8143D9;
      color: white;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 15px;
      font-weight: bold;
    }

    .resource-item-header {
      background-color: #f0f0f0;
      color: #333;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 10px;
      font-weight: bold;
    }

    .resource-content {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
    }

    .key-value-pair {
      display: grid;
      grid-template-columns: 200px 1fr;
      padding: 5px 0;
      border-bottom: 1px solid #ddd;
    }

    .key {
      font-weight: bold;
      color: #555;
    }

    .value {
      word-break: break-word;
    }

    .array-container {
      margin-left: 20px;
      border-left: 2px solid #ddd;
      padding-left: 10px;
    }

    .array-item {
      margin: 5px 0;
      padding: 5px;
      background-color: #f1f1f1;
      border-radius: 4px;
    }

    .array-header {
      color: #666;
      font-size: 0.9em;
      margin-bottom: 5px;
    }

    .sub-item {
      margin: 3px 0 3px 15px;
      padding: 2px 0;
      display: grid;
      grid-template-columns: 180px 1fr;
      gap: 10px;
    }

    .sub-key {
      color: #666;
      font-size: 0.9em;
    }

    .error {
      color: red;
      padding: 10px;
      margin: 10px 0;
      border: 1px solid red;
      border-radius: 4px;
      display: none;
    }

    .stats {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }


    .scroll-to-top-btn {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #8143D9;
      color: white;
      border: none;
      border-radius: 50%;
      padding: 15px;
      font-size: 18px;
      cursor: pointer;
      display: none;
      /* Initially hidden */
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .scroll-to-top-btn:hover {
      background-color: #000000;
    }

    #parser {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }

    ul {
      padding: 15px;
    }

    ul>li {
      color: blue;
      list-style-type: circle;
      text-decoration: underline;
    }
  </style>
</head>

<body>
  <div id="loader-container"
    class="flex flex-col items-center justify-center space-y-2 z-20 fixed bg-black bg-opacity-50 h-full w-full">
    <div id="loader" class="w-10 h-10 border-4 border-blue-500 border-t-white rounded-full animate-spin"></div>
    <div id="loader-text" class="text-white font-medium">Processing...</div>
  </div>
  <div id="container" class="absolute w-full h-full" style="font-family: sans-serif;">
    <div class="w-full flex flex-col md:flex-row justify-between items-center p-2">
      <div class="md:flex-1"></div>
      <div class="flex md:flex-1 border-2">
        <button id="editorTab"
          class="flex-1 px-4 py-3 text-gray-600 border-b-2 border-transparent font-bold bg-[#8143D9]"
          onclick="switchTab('editor')">Editor</button>
        <button id="parserTab" class="flex-1 px-4 py-3 text-gray-600 border-b-2 border-transparent font-bold bg-white"
          onclick="switchTab('parser')">Parser</button>
      </div>
      <div class="flex justify-end p-3 gap-5 md:flex-1">
        <div class="cursor-pointer flex flex-col justify-center items-center" id="upload-trigger">
          <img src="./upload.png" draggable="false" alt="upload" class="w-10 h-10">
          <input type="file" name="json-file" id="json-file" class="hidden">
          <p class="text-sm font-sans">Upload File</p>
        </div>
        <div class="cursor-pointer flex flex-col justify-center items-center" id="generate-data">
          <img src="./chat-bot.jpg" draggable="false" alt="upload" class="w-10 h-10">
          <p class="text-sm font-sans">Generate Data</p>
        </div>
      </div>
    </div>

    <!-- JSON Text Area -->
    <div class="p-4 h-full" id="editor">


      <div class="max-w-5xl mx-auto p-6 bg-white shadow-lg rounded-lg relative" style="background-color: #f5f5f5;">
        <h2 class="text-xl font-semibold mb-4 text-white-700 text-center" style="font-family: sans-serif;">Patient's
          Medical Records</h2>

        <div class="grid grid-cols-1 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Patient</label>
            <textarea id="patient" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Document Reference</label>
            <textarea id="documentreference" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Encounter</label>
            <textarea id="encounter" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Procedure</label>
            <textarea id="procedure" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Condition</label>
            <textarea id="condition" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Diagnostic Report</label>
            <textarea id="diagnosticreport" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Explanation of Benefit</label>
            <textarea id="explanationbenefit" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Care Plan</label>
            <textarea id="careplan" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Allergy Intolerance</label>
            <textarea id="allergyintolerance" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Medication Request</label>
            <textarea id="medicationrequest" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Device</label>
            <textarea id="device" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Observation</label>
            <textarea id="observation" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Immunization</label>
            <textarea id="immunization" class="w-full p-2 border rounded-md focus:ring focus:ring-blue-300"
              rows="6"></textarea>
          </div>
        </div>

        <div class="mt-6 text-center">
          <button id="send-data" class="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition">
            Submit
          </button>
        </div>
      </div>
    </div>

    <!-- JSON Parser -->
    <div class="p-4 h-full hidden" id="parser">
      <div class="container">

        <!-- <div id="loader" class="loader" style="display: none;">
          <p>Loading...</p>
        </div> -->

        <button id="scrollToTopBtn" class="scroll-to-top-btn" style="display: none;">⬆ Scroll to Top</button>


        <h1 class="text-lg text-center">FHIR Resource Viewer</h1>


        <!-- <div class="upload-section">
          <h3>Upload FHIR JSON File</h3>
          <input type="file" id="fileInput" accept=".json">
        </div> -->

        <div class="error" id="errorMessage"></div>

        <div class="stats" id="statsSection" style="display: none;">
          <h3>File Statistics</h3>
          <div id="statsContent" class="p-5"></div>
        </div>

        <div id="resourceViewer"></div>
      </div>
    </div>

  </div>
  <script src="../config.js"></script>

  <script src="./js/redirect.js"></script>
</body>

</html>