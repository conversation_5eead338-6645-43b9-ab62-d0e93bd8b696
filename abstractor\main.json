{"additional_history_and_risk_factors": {"cardiomyopathy_cm": {"field_id": "4565", "label": "Cardiomyopathy (CM)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cm_type": {"field_id": "4570", "label": "CM Type", "options": [{"id": "1", "value": "Non-Ischemic"}, {"id": "2", "value": "Ischemic"}, {"id": "3", "value": "Restrictive"}, {"id": "4", "value": "Hypertrophic"}, {"id": "5", "value": "Other"}], "input_type": "select", "value": "id"}, "chronic_lung_disease": {"field_id": "4575", "label": "Chronic Lung Disease", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "sleep_apnea": {"field_id": "4580", "label": "Sleep Apnea", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "coronary_artery_disease": {"field_id": "4285", "label": "Coronary Artery Disease", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "sleep_apnea_rec_treatment_followed": {"field_id": "4585", "label": "Sleep Apnea Rec Treatment Followed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "additional_stroke_and_bleeding_risk_factors": {"increased_fall_risk": {"field_id": "14793", "label": "Increased Fall Risk", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "clinically_relevant_bleeding_event": {"field_id": "14794", "label": "Clinically Relevant Bleeding Event", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "bleeding_event_type": {"field_id": "14796", "label": "Bleeding Event Type", "options": [{"id": "1", "value": "Intracranial"}, {"id": "2", "value": "Epistaxis"}, {"id": "3", "value": "Gastrointestinal"}, {"id": "4", "value": "Other"}], "input_type": "select", "value": "id"}, "genetic_coagulopathy": {"field_id": "14797", "label": "Genetic Coagulopathy", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "concurrent_anticoagulant_therapy": {"field_id": "14798", "label": "Concurrent Anticoagulant Therapy", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "diagnostic_studies": {"atrial_rhythm": {"field_id": "5100", "label": "Atrial Rhythm", "options": [{"id": "1", "value": "Sinus"}, {"id": "2", "value": "AFib"}, {"id": "3", "value": "Atrial tach"}, {"id": "4", "value": "Atrial flutter"}, {"id": "5", "value": "Sinus arrest"}, {"id": "6", "value": "Atrial paced"}, {"id": "7", "value": "Not Documented"}], "input_type": "select", "value": "id"}, "lvef_assessed": {"field_id": "5110", "label": "LVEF Assessed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "lvef": {"field_id": "5115", "label": "LVEF", "input_type": "string", "value": ""}, "transthoracic_echo_tte_performed": {"field_id": "5120", "label": "Transthoracic Echo (TTE) Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_tte": {"field_id": "5125", "label": "Date of TTE", "input_type": "string", "value": ""}, "baseline_imaging_performed": {"field_id": "5170", "label": "Baseline Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "ct_performed": {"field_id": "5175", "label": "CT Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_ct": {"field_id": "5180", "label": "Date of CT", "input_type": "string", "value": ""}, "mri_performed": {"field_id": "5185", "label": "MRI Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_mri": {"field_id": "5190", "label": "Date of MRI", "input_type": "string", "value": ""}, "intracardiac_echo_performed": {"field_id": "14826", "label": "Intracardiac Echo Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "date_of_intracardiac_echo": {"field_id": "14827", "label": "Date of Intracardiac Echo", "input_type": "string", "value": ""}}, "discharge": {"surgery": {"field_id": "14835", "label": "Surgery", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "percutaneous_coronary_interventions": {"field_id": "14836", "label": "Percutaneous Coronary Interventions", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "discharge_date": {"field_id": "10100", "label": "Discharge Date", "input_type": "string", "value": ""}, "discharge_status": {"field_id": "10105", "label": "Discharge Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": ""}, "if_alive_discharge_location": {"field_id": "10110", "label": "If Alive, Discharge Location", "options": [{"id": "1", "value": "Home"}, {"id": "2", "value": "Skilled Nursing facility"}, {"id": "3", "value": "Extended care/TCU/rehab"}, {"id": "4", "value": "Other"}, {"id": "5", "value": "Other acute care hospital"}, {"id": "6", "value": "Left against medical advice (AMA)"}], "input_type": "select", "value": "id"}, "if_alive_hospice_care": {"field_id": "10115", "label": "If Alive, Hospice Care", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "if_deceased_death_during_the_procedure": {"field_id": "10120", "label": "If Deceased, Death During the Procedure", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "if_deceased_cause_of_death": {"field_id": "10125", "label": "If Deceased, Cause of Death", "options": [{"id": "1", "value": "Acute myocardial infarction"}, {"id": "2", "value": "Pulmonary"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Sudden cardiac death"}, {"id": "5", "value": "Renal"}, {"id": "6", "value": "Non-cardiovascular procedure or surgery"}, {"id": "7", "value": "Heart failure"}, {"id": "8", "value": "Gastrointestinal"}, {"id": "9", "value": "<PERSON>rauma"}, {"id": "10", "value": "Stroke"}, {"id": "11", "value": "Hepatobiliary"}, {"id": "12", "value": "Suicide"}, {"id": "13", "value": "Cardiovascular procedure"}, {"id": "14", "value": "Pancreatic"}, {"id": "15", "value": "Neurological"}, {"id": "16", "value": "Cardiovascular hemorrhage"}, {"id": "17", "value": "Infection"}, {"id": "18", "value": "Malignancy"}, {"id": "19", "value": "Other cardiovascular reason"}, {"id": "20", "value": "Inflammatory/Immunologic"}, {"id": "21", "value": "Other non-cardiovascular reason"}], "input_type": "select", "value": "id"}}, "discharge_medications": {"note": "Medications prescribed at discharge are not required for patients who expired, discharged to 'Other acute care Hospital', 'AMA', or are receiving Hospice Care.", "prescribed_at_discharge": {"field_id": "10205", "label": "Prescribed at Discharge", "medications": {"fondaparinux": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "heparin_derivative": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "low_molecular_weight_heparin": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "unfractionated_heparin": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "warfarin": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "aspirin": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason", "81-100 mg", "101-324 mg", "325 mg"], "input_type": "radio"}, "aspirin_dipyridamole": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "vorapaxar": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "apixaban": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "dabigatran": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "edoxaban": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "rivaroxaban": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "cangrelor": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "clopidogrel": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "other_p2y12": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "prasugrel": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "ticagrelor": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}, "ticlopidine": {"options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio"}}}}, "epicardial_access_assessment": {"epicardial_approach_considered": {"field_id": "14824", "label": "Epicardial Approach Considered", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "medical_conditions": {"field_id": "14823", "label": "Medical Conditions", "options": [{"id": "1", "value": "Cardiac Surgery"}, {"id": "2", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "3", "value": "Epicardial Access"}, {"id": "4", "value": "Thoracic Radiation Therapy"}, {"id": "5", "value": "<PERSON><PERSON><PERSON> Excavatum"}, {"id": "6", "value": "Epigastric Surgery"}, {"id": "7", "value": "Autoimmune Disease"}, {"id": "8", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "9", "value": "Hiatal Hernia"}], "input_type": "select", "value": "id"}, "lupus_erythematosus": {"field_id": "14825", "label": "<PERSON><PERSON>sus", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "episode_of_care_admission": {"arrival_date": {"field_id": "3000", "label": "Arrival Date", "input_type": "string", "value": ""}, "health_insurance": {"field_id": "3005", "label": "Health Insurance", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "payment_source": {"field_id": "3010", "label": "Payment Source", "options": [{"id": "1", "value": "Private Health Insurance"}, {"id": "2", "value": "Medicare"}, {"id": "3", "value": "Medicare Advantage"}, {"id": "4", "value": "Medicaid"}, {"id": "5", "value": "Military Health Care"}, {"id": "6", "value": "State-Specific Plan (non-Medicaid)"}, {"id": "7", "value": "Indian Health Service"}, {"id": "8", "value": "Non-US"}], "input_type": "select", "value": "id"}, "mbi": {"field_id": "12846", "label": "MBI", "input_type": "string", "value": ""}, "ncdr_research_study": {"field_id": "3020", "label": "NCDR Research Study", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "study_name": {"field_id": "3025", "label": "Study Name", "input_type": "string", "value": ""}, "patient_id": {"field_id": "3030", "label": "Patient ID", "input_type": "string", "value": ""}, "admitted_for_laa_occlusion_intervention": {"field_id": "14791", "label": "Admitted for LAA Occlusion Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "follow_up": {"assessment_date": {"field_id": "11000", "label": "Assessment Date", "options": [], "input_type": "string", "value": ""}, "follow_up_interval": {"field_id": "14851", "label": "Follow-up Interval", "options": [{"id": "", "value": "45 day"}, {"id": "", "value": "6 month"}, {"id": "", "value": "1 year"}, {"id": "", "value": "2 year"}], "input_type": "select", "value": "id"}, "reference_episode_arrival_date": {"field_id": "14946", "label": "Reference Episode Arrival Date", "options": [], "input_type": "string", "value": ""}, "reference_episode_discharge_date": {"field_id": "14338", "label": "Reference Episode Discharge Date", "options": [], "input_type": "string", "value": ""}, "reference_procedure_start_date_time": {"field_id": "11001", "label": "Reference Procedure Start Date/Time", "options": [], "input_type": "string", "value": ""}, "method_to_determine_follow_up_status": {"field_id": "11003", "label": "Method(s) to Determine Follow-up Status", "options": [{"id": "", "value": "Office Visit"}, {"id": "", "value": "Medical Records"}, {"id": "", "value": "Letter from Medical Provider"}, {"id": "", "value": "Phone Call"}, {"id": "", "value": "Social Security Death Master File"}, {"id": "", "value": "Hospitalized"}, {"id": "", "value": "Other"}], "input_type": "select", "value": "id"}, "follow_up_status": {"field_id": "11004", "label": "Follow-up Status", "options": [{"id": "", "value": "Alive"}, {"id": "", "value": "Deceased"}, {"id": "", "value": "Lost to Follow-up"}], "input_type": "select", "value": "id"}, "date_of_death": {"field_id": "11006", "label": "Date of Death", "options": [], "input_type": "string", "value": ""}, "cause_of_death": {"field_id": "11007", "label": "Cause of Death", "options": [{"id": "", "value": "Acute myocardial infarction"}, {"id": "", "value": "Sudden cardiac death"}, {"id": "", "value": "Heart failure"}, {"id": "", "value": "Stroke"}, {"id": "", "value": "Cardiovascular procedure"}, {"id": "", "value": "Cardiovascular hemorrhage"}, {"id": "", "value": "Other cardiovascular reason"}, {"id": "", "value": "Pulmonary"}, {"id": "", "value": "Renal"}, {"id": "", "value": "Gastrointestinal"}, {"id": "", "value": "Hepatobiliary"}, {"id": "", "value": "Pancreatic"}, {"id": "", "value": "Infection"}, {"id": "", "value": "Inflammatory/Immunologic"}, {"id": "", "value": "Hemorrhage"}, {"id": "", "value": "Non-cardiovascular procedure or surgery"}, {"id": "", "value": "<PERSON>rauma"}, {"id": "", "value": "Suicide"}, {"id": "", "value": "Neurological"}, {"id": "", "value": "Malignancy"}, {"id": "", "value": "Other non-cardiovascular reason"}], "input_type": "select", "value": "id"}, "barthel_index_evaluation": {"barthel_index_evaluation_performed": {"field_id": "14891", "label": "Barthel Index Evaluation Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "feeding": {"field_id": "14892", "label": "Feeding", "options": [{"id": "", "value": "Unable"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "bathing": {"field_id": "14893", "label": "Bathing", "options": [{"id": "", "value": "Dependent"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "grooming": {"field_id": "14894", "label": "Grooming", "options": [{"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "dressing": {"field_id": "14895", "label": "Dressing", "options": [{"id": "", "value": "Dependent"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "bowels": {"field_id": "14896", "label": "Bowels", "options": [{"id": "", "value": "Incontinent"}, {"id": "", "value": "Inconsistent"}, {"id": "", "value": "Continent"}], "input_type": "select", "value": "id"}, "bladder": {"field_id": "14897", "label": "<PERSON><PERSON><PERSON>", "options": [{"id": "", "value": "Incontinent"}, {"id": "", "value": "Inconsistent"}, {"id": "", "value": "Continent"}], "input_type": "select", "value": "id"}, "toilet_use": {"field_id": "14898", "label": "Toilet Use", "options": [{"id": "", "value": "Dependent"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "transfers": {"field_id": "14899", "label": "Transfers", "options": [{"id": "", "value": "Unable"}, {"id": "", "value": "Major <PERSON><PERSON> Needed"}, {"id": "", "value": "Minor Assist Needed"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "mobility": {"field_id": "14900", "label": "Mobility", "options": [{"id": "", "value": "Immobile"}, {"id": "", "value": "Wheelchair"}, {"id": "", "value": "One Person Assist"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}, "stairs": {"field_id": "14901", "label": "Stairs", "options": [{"id": "", "value": "Unable"}, {"id": "", "value": "Needs Help"}, {"id": "", "value": "Independent"}], "input_type": "select", "value": "id"}}}, "follow_up_diagnostic_studies": {"lvef_assessed": {"field_id": "14858", "label": "LVEF Assessed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "lvef": {"field_id": "13690", "label": "LVEF", "options": [], "input_type": "string", "value": ""}, "transthoracic_echo_performed": {"field_id": "14859", "label": "Transthoracic Echo (TTE) Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_tte": {"field_id": "14873", "label": "Date of TTE", "options": [], "input_type": "string", "value": ""}, "transesophageal_echo_performed_tee": {"field_id": "14874", "label": "Transesophageal Echo Performed (TEE)", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_tee": {"field_id": "14875", "label": "Date of TEE", "options": [], "input_type": "string", "value": ""}, "cardiac_ct_performed": {"field_id": "14876", "label": "Cardiac CT Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_cardiac_ct": {"field_id": "14877", "label": "Date of Cardiac CT", "options": [], "input_type": "string", "value": ""}, "cardiac_mri_performed": {"field_id": "14878", "label": "Cardiac MRI Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_cardiac_mri": {"field_id": "14879", "label": "Date of Cardiac MRI", "options": [], "input_type": "string", "value": ""}, "intracardiac_echo_performed": {"field_id": "14880", "label": "Intracardiac Echo Performed", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "date_of_intracardiac_echo": {"field_id": "14881", "label": "Date of Intracardiac Echo", "options": [], "input_type": "string", "value": ""}, "atrial_thrombus_detected": {"field_id": "14882", "label": "Atrial Thrombus Detected", "options": [{"id": "", "value": "No"}, {"id": "", "value": "Yes"}], "input_type": "radio", "value": ""}, "device_margin_residual_leak": {"field_id": "14884", "label": "<PERSON><PERSON> Residual Leak", "options": [{"id": "", "value": "Not Assessed"}], "input_type": "select", "value": "id"}}, "follow_up_physical_exam_and_labs": {"creatinine": {"field_id": "14886", "label": "Creatinine", "options": [{"id": "", "value": "Not Drawn"}], "input_type": "radio", "value": ""}, "hemoglobin": {"field_id": "14888", "label": "Hemoglobin", "options": [{"id": "", "value": "Not Drawn"}], "input_type": "radio", "value": ""}, "modified_rankin_scale_mrs": {"field_id": "13148", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "", "value": "0: No symptoms at all"}, {"id": "", "value": "1: No significant disability despite symptoms"}, {"id": "", "value": "2: Slight disability"}, {"id": "", "value": "3: Moderate disability"}, {"id": "", "value": "4: Moderately severe disability"}, {"id": "", "value": "5: Severe disability"}, {"id": "", "value": "6: Death"}, {"id": "", "value": "Not Administered"}], "input_type": "select", "value": "id"}}, "follow_up_adjudication": {"follow_up_adjudication": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": ""}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": ""}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": ""}, "reference_procedure_start_date_time": {"field_id": "11001", "label": "Reference Procedure Start Date/Time", "input_type": "string", "value": ""}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": ""}, "study_patient_id": {"field_id": "3030", "label": "Study Patient ID", "input_type": "string", "value": ""}, "follow_up_event": {"field_id": "14967", "label": "Follow-up Event", "input_type": "string", "value": ""}, "follow_up_event_date": {"field_id": "14386", "label": "Follow-up Event Date", "input_type": "string", "value": ""}}, "neurologic": {"follow_up_adjudication_status": {"field_id": "14969", "label": "Follow-up Adjudication Status", "input_type": "radio", "options": ["Alive", "Deceased"], "value": ""}, "date_of_death": {"field_id": "14970", "label": "Date of Death", "input_type": "string", "value": ""}, "symptom_onset_date": {"field_id": "14976", "label": "Symptom Onset Date", "input_type": "string", "value": ""}, "neurologic_deficit_with_rapid_onset": {"field_id": "14977", "label": "Neurologic Deficit with Rapid Onset", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "neurologic_deficit_clinical_presentation": {"field_id": "14978", "label": "Neurologic Deficit Clinical Presentation", "input_type": "radio", "options": ["Stroke-related", "Non-Stroke-related"], "value": ""}, "diagnosis_confirmation_by_neurology": {"field_id": "14979", "label": "Diagnosis Confirmation by Neurology", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "brain_imaging_performed": {"field_id": "14980", "label": "Brain Imaging Performed", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "brain_imaging_type": {"field_id": "14981", "label": "Brain Imaging Type", "input_type": "select", "options": [{"id": "1", "value": "Cerebral Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Other"}], "value": ""}, "deficit_type": {"field_id": "14982", "label": "Deficit Type", "input_type": "select", "options": [{"id": "1", "value": "No deficit"}, {"id": "2", "value": "Infarction"}, {"id": "3", "value": "Hemorrhage"}, {"id": "4", "value": "Both"}], "value": ""}, "hemorrhage_type": {"field_id": "14983", "label": "Hemorrhage Type", "input_type": "select", "options": [{"id": "1", "value": "Intracerebral"}, {"id": "2", "value": "Subarachnoid"}, {"id": "3", "value": "Subdural"}], "value": ""}, "subsequent_iv_rtpa_administered": {"field_id": "14984", "label": "Subsequent IV rtPA Administered", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "subsequent_endovascular_therapeutic_intervention": {"field_id": "14985", "label": "Subsequent Endovascular Therapeutic Intervention", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "symptoms_duration": {"field_id": "14986", "label": "Symptoms Duration", "input_type": "select", "options": [{"id": "1", "value": "< 1 Hour"}, {"id": "2", "value": "1 – 24 Hours"}, {"id": "3", "value": "> 24 Hours"}], "value": ""}, "trauma": {"field_id": "14987", "label": "<PERSON>rauma", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "modified_rankin_scale_mrs": {"field_id": "14988", "label": "Modified Rankin Scale (mRS)", "input_type": "select", "options": [{"id": "1", "value": "0: No symptoms at all"}, {"id": "2", "value": "1: No significant disability despite symptoms"}, {"id": "3", "value": "2: Slight disability"}, {"id": "4", "value": "3: Moderate disability"}, {"id": "5", "value": "4: Moderately severe disability"}, {"id": "6", "value": "5: Severe disability"}, {"id": "7", "value": "6: Death"}, {"id": "8", "value": "Not Administered"}], "value": ""}, "procedure_related_neurologic_event": {"field_id": "14990", "label": "Procedure Related Neurologic Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}, "device_related_neurologic_event": {"field_id": "15015", "label": "Device Related Neurologic Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}}, "bleeding": {"follow_up_adjudication_status": {"field_id": "14971", "label": "Follow-up Adjudication Status", "input_type": "radio", "options": ["Alive", "Deceased"], "value": ""}, "adjudication_date_of_death": {"field_id": "14972", "label": "Adjudication Date of Death", "input_type": "string", "value": ""}, "invasive_intervention_required": {"field_id": "14991", "label": "Invasive Intervention Required", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "rbc_transfusion": {"field_id": "14992", "label": "RBC Transfusion", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "number_of_rbc_units_transfused": {"field_id": "14993", "label": "Number of RBC Units Transfused", "input_type": "string", "value": ""}, "hemoglobin_pre_transfusion": {"field_id": "14994", "label": "Hemoglobin Pre-Transfusion", "input_type": "string", "value": ""}, "diagnostic_imaging_performed": {"field_id": "14995", "label": "Diagnostic Imaging Performed", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "end_organ_damage": {"field_id": "14996", "label": "End Organ Damage", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "bleeding_event_readmission": {"field_id": "14975", "label": "Bleeding Event Readmission", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "major_surgery_within_past_30_days": {"field_id": "14997", "label": "Major Surgery within Past 30 days", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "percutaneous_coronary_intervention_within_past_30_days": {"field_id": "14998", "label": "Percutaneous Coronary Intervention within Past 30 days", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "procedure_related_bleeding_event": {"field_id": "14999", "label": "Procedure Related Bleeding Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}, "device_related_bleeding_event": {"field_id": "15000", "label": "<PERSON>ce Related Bleeding Event", "input_type": "select", "options": [{"id": "1", "value": "Certain"}, {"id": "2", "value": "Probable"}, {"id": "3", "value": "Possible"}, {"id": "4", "value": "Unlikely"}, {"id": "5", "value": "Unclassifiable"}], "value": ""}}, "systemic_thromboembolism": {"follow_up_adjudication_status": {"field_id": "14973", "label": "Follow-up Adjudication Status", "input_type": "radio", "options": ["Alive", "Deceased"], "value": ""}, "date_of_death": {"field_id": "14974", "label": "Date of Death", "input_type": "string", "format": "mm/dd/yyyy", "value": ""}, "death_cause": {"field_id": "15016", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "focal_end_organ_hypoperfusion_present": {"field_id": "15001", "label": "Focal End-Organ Hypoperfusion Present", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "systemic_thromboembolization_imaging_evidence": {"field_id": "15002", "label": "Systemic Thromboembolization Imaging Evidence", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "imaging_method": {"field_id": "15003", "label": "Imaging Method", "input_type": "select", "options": [{"id": "1", "value": "Angiography"}, {"id": "2", "value": "Computed Tomography"}, {"id": "3", "value": "Magnetic Resonance Imaging"}, {"id": "4", "value": "Ultrasound"}, {"id": "5", "value": "Other Imaging"}], "value": ""}, "therapeutic_intervention_performed": {"field_id": "15004", "label": "Therapeutic Intervention Performed", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "intervention_type": {"field_id": "15005", "label": "Intervention Type", "input_type": "select", "options": [{"id": "1", "value": "Catheter"}, {"id": "2", "value": "Pharmacological"}, {"id": "3", "value": "Surgical"}, {"id": "4", "value": "Other"}], "value": ""}}, "current_medications_at_time_of_event": {"field_id": "15007", "label": "Current Medications at Time of Event", "medications": {"fondaparinux": {"field_id": "15006", "label": "Fondaparinux", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "heparin_derivative": {"label": "Heparin Derivative", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "low_molecular_weight_heparin": {"label": "Low Molecular Weight Heparin", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "unfractionated_heparin": {"label": "Unfractionated Heparin", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "warfarin": {"label": "Warfarin", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "aspirin_81_100_mg": {"label": "Aspirin (81-100 mg)", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "aspirin_101_324_mg": {"label": "Aspirin (101-324 mg)", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "aspirin_325_mg": {"label": "Aspirin 325 mg", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "aspirin_dipyridamole": {"label": "Aspirin/Dipyridamole", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "vorapaxar": {"label": "Vorapaxar", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "apixaban": {"label": "Apixaban", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "dabigatran": {"label": "Dabigatran", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "edoxaban": {"label": "Edoxaban", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "rivaroxaban": {"label": "Rivaroxaban", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "cangrelor": {"label": "<PERSON><PERSON><PERSON><PERSON>", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "clopidogrel": {"label": "Clopidogrel", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "other_p2y12": {"label": "Other P2Y12", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "prasugrel": {"label": "Prasug<PERSON>", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "ticagrelor": {"label": "Ticagrelor", "input_type": "radio", "options": ["Yes", "No"], "value": ""}, "ticlopidine": {"label": "Ticlopidine", "input_type": "radio", "options": ["Yes", "No"], "value": ""}}}}, "follow_up_anticoagulation_therapy": {"warfarin_discontinued": {"field_id": "14951", "label": "Warfarin Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": ""}, "if_yes_date": {"field_id": "14966", "label": "If Yes, Date", "format": "mm / dd / yyyy", "input_type": "string", "value": ""}, "warfarin_resumed": {"field_id": "14953", "label": "Warfarin Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": ""}, "doac_discontinued": {"field_id": "14955", "label": "DOAC Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": ""}, "doac_resumed": {"field_id": "14957", "label": "DOAC Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": ""}, "aspirin_discontinued": {"field_id": "14959", "label": "<PERSON><PERSON><PERSON> Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": ""}, "aspirin_resumed": {"field_id": "14961", "label": "Aspirin Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": ""}, "p2y12_discontinued": {"field_id": "14963", "label": "P2Y12 Discontinued", "options": ["No - Not Discontinued", "Yes - Discontinued"], "input_type": "radio", "value": ""}, "p2y12_resumed": {"field_id": "14965", "label": "P2Y12 Resumed", "options": ["No", "Yes (Thrombotic Event)", "Yes (Other)"], "input_type": "radio", "value": ""}}, "follow_up_events": {"follow_up_event": {"field_id": "14948", "label": "Follow-up Event", "input_type": "string", "value": ""}, "event_occurred": {"field_id": "14276", "label": "Event Occurred", "input_type": "string", "value": ""}, "event_dates": {"field_id": "14277", "label": "Event Date(s)", "input_type": "string", "value": ""}, "cardiovascular": {"endocarditis": {"label": "Endocarditis", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "iatrogenic_asd": {"label": "Iatrogenic ASD (requiring intervention)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "laa_occlusion_reintervention": {"label": "LAA Occlusion Reintervention", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "myocardial_infarction": {"label": "Myocardial Infarction", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "pci": {"label": "PCI", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "pericarditis": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "unplanned_cardiac_surgery": {"label": "Unplanned Cardiac Surgery", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "unplanned_intervention": {"label": "Unplanned Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "systemic": {"deep_vein_thrombosis": {"label": "Deep Vein Thrombosis", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "new_requirement_for_dialysis": {"label": "New Requirement for Dialysis", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "non_device_related_readmission": {"label": "Non-Device Related Readmission", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "systemic_thromboembolism": {"label": "Systemic Thromboembolism (other than stroke) (COMPLETE ADJUDICATION)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "device": {"device_explant": {"label": "Device Explant", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "device_fracture": {"label": "Device Fracture", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "device_migration": {"label": "<PERSON><PERSON>", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "device_related_readmission": {"label": "Device Related Readmission", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "device_systemic_embolism": {"label": "Device Systemic Embolism", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "device_thrombus": {"label": "<PERSON><PERSON>", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "neurologic": {"hemorrhagic_stroke": {"label": "Hemorrhagic Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "intracranial_hemorrhage": {"label": "Intracranial Hemorrhage (other than hemorrhagic stroke)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "ischemic_stroke": {"label": "Ischemic Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "tia": {"label": "TIA", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "undetermined_stroke": {"label": "Undetermined Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "bleeding_complete_adjudication": {"access_site_bleeding": {"field_id": "14948", "label": "Access Site Bleeding", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "gi_bleeding": {"field_id": "unknown", "label": "GI Bleeding", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "hematoma": {"field_id": "unknown", "label": "Hematoma", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "hemothorax_not_requiring_drainage": {"field_id": "unknown", "label": "Hemothorax (not requiring drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "hemothorax_requiring_drainage": {"field_id": "unknown", "label": "Hemothorax (requiring drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "other_hemorrhage_non_intracranial": {"field_id": "unknown", "label": "Other Hemorrhage (non-intracranial)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "pericardial_effusion_requiring_open_cardiac_surgery": {"field_id": "unknown", "label": "Pericardial Effusion (requiring open cardiac surgery)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "pericardial_effusion_with_tamponade_requiring_percutaneous_drainage": {"field_id": "unknown", "label": "Pericardial Effusion with tamponade (requiring percutaneous drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "pericardial_effusion_without_tamponade_requiring_percutaneous_drainage": {"field_id": "unknown", "label": "Pericardial Effusion without tamponade (requiring percutaneous drainage)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "retroperitoneal_bleeding": {"field_id": "unknown", "label": "Retroperitoneal Bleeding", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "vascular_complications": {"field_id": "unknown", "label": "Vascular Complications", "input_type": "radio", "options": ["No", "Yes"], "value": ""}}, "peripheral_vascular": {"av_fistula_requiring_surgical_repair": {"field_id": "unknown", "label": "AV Fistula (requiring surgical repair)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "pseudoaneurysm_requiring_endovascular_repair": {"field_id": "unknown", "label": "Pseudoaneurysm (requiring endovascular repair)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "pseudoaneurysm_requiring_surgical_repair": {"field_id": "unknown", "label": "Pseudoaneurysm (requiring surgical repair)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}, "pseudoaneurysm_requiring_thrombin_injection_only": {"field_id": "unknown", "label": "Pseudoaneurysm (requiring thrombin injection only)", "input_type": "radio", "options": ["No", "Yes"], "value": ""}}, "pulmonary": {"pulmonary_embolism": {"field_id": "unknown", "label": "Pulmonary Embolism", "input_type": "radio", "options": ["No", "Yes"], "value": ""}}}, "follow_up_medications": {"medication": {"field_id": "1990", "label": "Medication", "options": [{"id": "1", "value": "Fondaparinux"}, {"id": "2", "value": "Heparin Derivative"}, {"id": "3", "value": "Low Molecular Weight Heparin"}, {"id": "4", "value": "Unfractionated Heparin"}, {"id": "5", "value": "Warfarin"}, {"id": "6", "value": "<PERSON><PERSON><PERSON>"}, {"id": "7", "value": "Aspirin/Dipyridamole"}, {"id": "8", "value": "Vorapaxar"}, {"id": "9", "value": "Apixaban"}, {"id": "10", "value": "Dabigatran"}, {"id": "11", "value": "Edoxaban"}, {"id": "12", "value": "Rivaroxaban"}, {"id": "13", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "14", "value": "Clopidogrel"}, {"id": "15", "value": "Other P2Y12"}, {"id": "16", "value": "Prasug<PERSON>"}, {"id": "17", "value": "Ticagrelor"}, {"id": "18", "value": "Ticlopidine"}], "input_type": "select", "value": "id"}, "current_medications_at_time_of_follow_up": {"field_id": "14949", "label": "Current Medications at Time of Follow-up", "options": ["Yes", "No - No Reason", "No - Medical Reason", "No - Pt. Reason"], "input_type": "radio", "value": ""}, "if_yes_dose": {"field_id": "14950", "label": "If Yes, <PERSON><PERSON>", "options": [{"id": "1", "value": "81-100 MG"}, {"id": "2", "value": "101-324 MG"}, {"id": "3", "value": "325 MG"}], "input_type": "select", "value": "id"}}, "history_and_risk_factors": {"cha2ds2_vasc_risk_scores": {"cha2ds2_vasc_congestive_heart_failure": {"field_id": "4005", "label": "CHA2DS2-VASc Congestive Heart Failure", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "nyha_functional_classification": {"field_id": "4010", "label": "NYHA Functional Classification", "options": [{"id": "1", "value": "Class I"}, {"id": "2", "value": "Class II"}, {"id": "3", "value": "Class III"}, {"id": "4", "value": "Class IV"}], "input_type": "select", "value": "id"}, "cha2ds2_vasc_lv_dysfunction": {"field_id": "4015", "label": "CHA2DS2-VASc LV Dysfunction", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cha2ds2_vasc_thromboembolic_event": {"field_id": "4040", "label": "CHA2DS2-VASc Thromboembolic Event", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cha2ds2_vasc_hypertension": {"field_id": "4020", "label": "CHA2DS2-VASc Hypertension", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cha2ds2_vasc_vascular_disease": {"field_id": "4045", "label": "CHA2DS2-VASc Vascular Disease", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cha2ds2_vasc_diabetes_mellitus": {"field_id": "4025", "label": "CHA2DS2-VASc Diabetes Mellitus", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "vascular_disease_type": {"field_id": "4050", "label": "Vascular Disease Type", "options": [{"id": "1", "value": "Prior MI"}, {"id": "2", "value": "PAD"}, {"id": "3", "value": "Known Aortic Plaque"}, {"id": "4", "value": "CAD"}, {"id": "5", "value": "PCI"}, {"id": "6", "value": "CABG"}, {"id": "7", "value": "Carotid Artery Disease"}], "input_type": "select", "value": "id"}, "cha2ds2_vasc_stroke": {"field_id": "4030", "label": "CHA2DS2-VASc Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cha2ds2_vasc_tia": {"field_id": "4035", "label": "CHA2DS2-VASc TIA", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}, "has_bled_risk_scores": {"has_bled_hypertension_uncontrolled": {"field_id": "4055", "label": "HAS-BLED Hypertension (Uncontrolled)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_bleeding": {"field_id": "4095", "label": "HAS-BLED Bleeding", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_abnormal_renal_function": {"field_id": "4060", "label": "HAS-BLED Abnormal Renal Function", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_labile_inr": {"field_id": "4100", "label": "HAS-BLED Labile INR", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_abnormal_liver_function": {"field_id": "4065", "label": "HAS-BLED Abnormal Liver Function", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_alcohol": {"field_id": "4105", "label": "HAS-BLED Alcohol", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_stroke": {"field_id": "4070", "label": "HAS-BLED Stroke", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_stroke_type": {"field_id": "14792", "label": "HAS-BLED Stroke Type", "options": [{"id": "1", "value": "Hemorrhagic"}, {"id": "2", "value": "Ischemic"}, {"id": "3", "value": "Undetermined"}], "input_type": "select", "value": "id"}, "has_bled_drugs_antiplatelet": {"field_id": "4110", "label": "HAS-BLED Drugs - Antiplatelet", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "has_bled_drugs_nsaids": {"field_id": "4115", "label": "HAS-BLED Drugs - NSAIDS", "options": ["No", "Yes"], "input_type": "radio", "value": ""}}}, "history_interventions": {"cardiac_structural_intervention": {"field_id": "14802", "label": "Cardiac Structural Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "cardiac_structural_intervention_type": {"field_id": "14803", "label": "Cardiac Structural Intervention Type", "options": [{"id": "1", "value": "Aortic Balloon Valvuloplasty"}, {"id": "2", "value": "Transcatheter Aortic Valve Replacement (TAVR)"}, {"id": "3", "value": "AV Replacement – Surgical"}, {"id": "4", "value": "AV Repair – Surgical"}, {"id": "5", "value": "Mitral Balloon Valvuloplasty"}, {"id": "6", "value": "Transcatheter Mitral Valve Repair (TMVR)"}, {"id": "7", "value": "MV Replacement – Surgical"}, {"id": "8", "value": "MV Repair – Surgical"}, {"id": "9", "value": "Mitral Annuloplasty Ring – Surgical"}, {"id": "10", "value": "Mitral Transcatheter – Valve-in-valve"}, {"id": "11", "value": "ASD Closure"}, {"id": "12", "value": "PFO Closure"}, {"id": "13", "value": "Pulmonic Replacement"}, {"id": "14", "value": "Pulmonic Repair"}, {"id": "15", "value": "Tricuspid Replacement"}, {"id": "16", "value": "Tricuspid Repair"}], "input_type": "select", "value": "id"}, "left_atrial_appendage_occlusion_intervention": {"field_id": "14804", "label": "Left Atrial Appendage Occlusion Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "left_atrial_appendage_intervention_type": {"field_id": "14806", "label": "Left Atrial Appendage Intervention Type", "options": [{"id": "1", "value": "Epicardial Ligation"}, {"id": "2", "value": "Surgical Amputation"}, {"id": "3", "value": "Surgical Ligation"}, {"id": "4", "value": "Percutaneous Occlusion"}, {"id": "5", "value": "Surgical Closure Device"}, {"id": "6", "value": "Surgical Stapling"}], "input_type": "select", "value": "id"}}, "history_rhythm_history": {"atrial_fibrillation": {"field_id": "13709", "label": "Atrial Fibrillation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "atrial_fibrillation_classification": {"field_id": "4400", "label": "Atrial Fibrillation Classification", "options": [{"id": "1", "value": "Paroxysmal"}, {"id": "2", "value": "Persistent"}, {"id": "3", "value": "Long standing persistent"}, {"id": "4", "value": "Permanent"}], "input_type": "select", "value": "id"}, "valvular_atrial_fibrillation": {"field_id": "4380", "label": "Valvular Atrial Fibrillation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "hx_of_rheumatic_valve_disease": {"field_id": "14799", "label": "Hx of Rheumatic Valve Disease", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "hx_of_mitral_valve_replacement": {"field_id": "4385", "label": "Hx of Mitral Valve Replacement", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "mechanical_valve_in_mitral_position": {"field_id": "4390", "label": "Mechanical Valve in Mitral Position", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "hx_of_mitral_valve_repair": {"field_id": "4395", "label": "Hx of Mitral Valve Repair", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "attempt_at_atrial_fibrillation_termination": {"field_id": "4410", "label": "Attempt at Atrial Fibrillation Termination", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "pharmacologic_cardioversion": {"field_id": "4415", "label": "Pharmacologic Cardioversion", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "dc_cardioversion": {"field_id": "4420", "label": "DC Cardioversion", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "catheter_ablation": {"field_id": "4425", "label": "Catheter Ablation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_catheter_ablation_date": {"field_id": "4430", "label": "Most Recent Catheter Ablation Date", "options": [], "input_type": "string", "value": ""}, "prior_ablation_strategy": {"field_id": "4435", "label": "Prior Ablation Strategy(s)", "options": [], "input_type": "string", "value": ""}, "surgical_ablation": {"field_id": "4440", "label": "Surgical Ablation", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_surgical_ablation_date": {"field_id": "4445", "label": "Most Recent Surgical Ablation Date", "options": [], "input_type": "string", "value": ""}, "atrial_flutter": {"field_id": "4450", "label": "Atrial Flutter", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "atrial_flutter_classification": {"field_id": "4455", "label": "Atrial Flutter Classification", "options": [{"id": "1", "value": "Typical/Cavotricuspid Isthmus (CTI) Dependent"}, {"id": "2", "value": "Atypical"}], "input_type": "select", "value": "id"}, "attempt_at_atrial_flutter_termination": {"field_id": "4460", "label": "Attempt at Atrial Flutter Termination", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "pharmacologic_cardioversion_flutter": {"field_id": "4465", "label": "Pharmacologic Cardioversion (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "dc_cardioversion_flutter": {"field_id": "4470", "label": "DC Cardioversion (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "catheter_ablation_flutter": {"field_id": "4475", "label": "Catheter Ablation (Flutter)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_catheter_ablation_date_flutter": {"field_id": "4480", "label": "Most Recent Catheter Ablation Date (Flutter)", "options": [], "input_type": "string", "value": ""}}, "in_hospital_adjudication": {"demographics": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": ""}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": ""}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": ""}, "procedure_start_date": {"field_id": "7001", "label": "Procedure Start Date", "input_type": "string", "value": ""}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": ""}, "study_patient_id": {"field_id": "3030", "label": "Study Patient ID", "input_type": "string", "value": ""}, "adjudication_event": {"field_id": "14312", "label": "Adjudication Event", "input_type": "string", "value": ""}, "adjudication_event_date": {"field_id": "14313", "label": "Adjudication Event Date", "input_type": "string", "value": ""}}, "neurologic": {"adjudication_status": {"field_id": "14902", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": ""}, "adjudication_date_of_death": {"field_id": "14903", "label": "Adjudication Date of Death", "input_type": "string", "value": ""}, "symptom_onset_date": {"field_id": "14904", "label": "Symptom Onset Date", "input_type": "string", "value": ""}, "neurologic_deficit_with_rapid_onset": {"field_id": "14905", "label": "Neurologic Deficit with Rapid Onset", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "neurologic_deficit_clinical_presentation": {"field_id": "14906", "label": "Neurologic Deficit Clinical Presentation", "options": ["Stroke-related", "Non-Stroke-related"], "input_type": "radio", "value": ""}, "diagnosis_confirmation_by_neurology": {"field_id": "14907", "label": "Diagnosis Confirmation by Neurology", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "brain_imaging_performed": {"field_id": "14908", "label": "Brain Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "brain_imaging_type": {"field_id": "14909", "label": "Brain Imaging Type", "options": [{"id": "", "value": "Cerebral Angiography"}, {"id": "", "value": "Computed Tomography"}, {"id": "", "value": "Magnetic Resonance Imaging"}, {"id": "", "value": "Other"}], "input_type": "select", "value": "id"}, "deficit_type": {"field_id": "14910", "label": "Deficit Type", "options": [{"id": "", "value": "No deficit"}, {"id": "", "value": "Infarction"}, {"id": "", "value": "Hemorrhage"}, {"id": "", "value": "Both"}], "input_type": "select", "value": "id"}, "hemorrhagic_stroke_type": {"field_id": "14911", "label": "Hemorrhagic Stroke Type", "options": [{"id": "", "value": "Intracerebral"}, {"id": "", "value": "Subarachnoid"}, {"id": "", "value": "Subdural"}], "input_type": "select", "value": "id"}, "subsequent_iv_rtpa_administered": {"field_id": "14912", "label": "Subsequent IV rtPA Administered", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "subsequent_endovascular_therapeutic_intervention": {"field_id": "14913", "label": "Subsequent Endovascular Therapeutic Intervention", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "symptoms_duration": {"field_id": "14914", "label": "Symptoms Duration", "options": [{"id": "", "value": "< 1 Hour"}, {"id": "", "value": "1 – 24 Hours"}, {"id": "", "value": "> 24 Hours"}], "input_type": "select", "value": "id"}, "trauma": {"field_id": "14915", "label": "<PERSON>rauma", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "modified_rankin_scale_mrs": {"field_id": "14916", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "", "value": "0: No symptoms at all"}, {"id": "", "value": "1: No significant disability despite symptoms"}, {"id": "", "value": "2: Slight disability"}, {"id": "", "value": "3: Moderate disability"}, {"id": "", "value": "4: Moderately severe disability"}, {"id": "", "value": "5: Severe disability"}, {"id": "", "value": "6: Death"}], "input_type": "select", "value": "id"}, "not_administered": {"field_id": "14917", "label": "Not Administered", "input_type": "string", "value": ""}, "procedure_related_neurologic_event": {"field_id": "14918", "label": "Procedure Related Neurologic Event", "options": [{"id": "", "value": "Certain"}, {"id": "", "value": "Probable"}, {"id": "", "value": "Possible"}, {"id": "", "value": "Unlikely"}, {"id": "", "value": "Unclassifiable"}], "input_type": "select", "value": "id"}, "device_related_neurologic_event": {"field_id": "14931", "label": "Device Related Neurologic Event", "options": [{"id": "", "value": "Certain"}, {"id": "", "value": "Probable"}, {"id": "", "value": "Possible"}, {"id": "", "value": "Unlikely"}, {"id": "", "value": "Unclassifiable"}], "input_type": "select", "value": "id"}}, "bleeding": {"adjudication_status": {"field_id": "14924", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": ""}, "adjudication_date_of_death": {"field_id": "14930", "label": "Adjudication Date of Death", "input_type": "string", "value": ""}, "invasive_intervention_required": {"field_id": "14929", "label": "Invasive Intervention Required", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "rbc_transfusion": {"field_id": "14919", "label": "RBC Transfusion", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "number_of_rbc_units_transfused": {"field_id": "14920", "label": "Number of RBC Units Transfused", "input_type": "string", "value": ""}, "hemoglobin_pre_transfusion": {"field_id": "14921", "label": "Hemoglobin Pre-Transfusion", "input_type": "string", "value": ""}, "diagnostic_imaging_performed": {"field_id": "14922", "label": "Diagnostic Imaging Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "end_organ_damage": {"field_id": "14923", "label": "End Organ Damage", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "major_surgery_within_past_30_days": {"field_id": "14927", "label": "Major Surgery within Past 30 days", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "percutaneous_coronary_intervention_within_past_30_days": {"field_id": "14928", "label": "Percutaneous Coronary Intervention within Past 30 days", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "procedure_related_bleeding_event": {"field_id": "14925", "label": "Procedure Related Bleeding Event", "options": [{"id": "", "value": "Certain"}, {"id": "", "value": "Probable"}, {"id": "", "value": "Possible"}, {"id": "", "value": "Unlikely"}, {"id": "", "value": "Unclassifiable"}], "input_type": "select", "value": "id"}, "device_related_bleeding_event": {"field_id": "14926", "label": "<PERSON>ce Related Bleeding Event", "options": [{"id": "", "value": "Certain"}, {"id": "", "value": "Probable"}, {"id": "", "value": "Possible"}, {"id": "", "value": "Unlikely"}, {"id": "", "value": "Unclassifiable"}], "input_type": "select", "value": "id"}}}, "intra_or_post_procedure_events": {"general_events": {"event": {"field_id": "12153", "label": "Event", "input_type": "string", "value": ""}, "event_occurred": {"field_id": "9002", "label": "Event Occurred", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "event_dates": {"field_id": "14275", "label": "Event Date(s)", "format": "mm/dd/yyyy", "input_type": "string", "value": ""}}, "peripheral_vascular": {"av_fistula_no_intervention_required": {"label": "AV Fistula (no intervention required)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "av_fistula_requiring_surgical_repair": {"label": "AV Fistula (requiring surgical repair)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pseudoaneurysm_no_intervention_required": {"label": "Pseudoaneurysm (no intervention required)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pseudoaneurysm_requiring_endovascular_repair": {"label": "Pseudoaneurysm (requiring endovascular repair)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pseudoaneurysm_requiring_surgical_repair": {"label": "Pseudoaneurysm (requiring surgical repair)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pseudoaneurysm_requiring_thrombin_injection": {"label": "Pseudoaneurysm (requiring thrombin injection only)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}}, "neurologic": {"hemorrhagic_stroke": {"label": "Hemorrhagic Stroke", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "intracranial_hemorrhage": {"label": "Intracranial Hemorrhage (other than hemorrhagic stroke)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "ischemic_stroke": {"label": "Ischemic Stroke", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "tia": {"label": "TIA", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "undetermined_stroke": {"label": "Undetermined Stroke", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}}, "bleeding": {"access_site_bleeding": {"label": "Access Site Bleeding", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "gi_bleeding": {"label": "GI Bleeding", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "hematoma": {"label": "Hematoma", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "hemothorax_no_drainage": {"label": "Hemothorax (not requiring drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "hemothorax_requiring_drainage": {"label": "Hemothorax (requiring drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "other_hemorrhage_non_intracranial": {"label": "Other Hemorrhage (non-intracranial)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pericardial_effusion_open_cardiac_surgery": {"label": "Pericardial Effusion (requiring open cardiac surgery)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pericardial_effusion_tamponade_percutaneous": {"label": "Pericardial Effusion with tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pericardial_effusion_without_tamponade": {"label": "Pericardial Effusion without tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "retroperitoneal_bleeding": {"label": "Retroperitoneal Bleeding", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "vascular_complications": {"label": "Vascular Complications", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}}, "pulmonary": {"pleural_effusion": {"label": "Pleural Effusion", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pneumonia": {"label": "Pneumonia", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pneumothorax_no_intervention": {"label": "Pneumothorax (no intervention required)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pneumothorax_requiring_intervention": {"label": "Pneumothorax (requiring intervention)", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "pulmonary_embolism": {"label": "Pulmonary Embolism", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}, "respiratory_failure": {"label": "Respiratory Failure", "options": ["No", "Yes"], "date_format": "mm/dd/yyyy", "input_type": "radio", "value": ""}}}, "demographics": {"last_name": {"field_id": "2000", "label": "Last Name", "input_type": "string", "value": ""}, "first_name": {"field_id": "2010", "label": "First Name", "input_type": "string", "value": ""}, "middle_name": {"field_id": "2020", "label": "Middle Name", "input_type": "string", "value": ""}, "ssn": {"field_id": "2030", "label": "SSN", "input_type": "string", "value": ""}, "ssn_na": {"field_id": "2031", "label": "SSN", "options": ["True", "False"], "input_type": "radio", "value": ""}, "patient_id": {"field_id": "2040", "label": "Patient ID", "input_type": "string", "value": ""}, "other_id": {"field_id": "2045", "label": "Other ID", "input_type": "string", "value": ""}, "birth_date": {"field_id": "2050", "label": "Birth Date", "input_type": "string", "value": ""}, "sex": {"field_id": "2060", "label": "Sex", "options": ["Male", "Female"], "input_type": "radio", "value": ""}, "patient_zip_code": {"field_id": "2065", "label": "Patient Zip Code", "input_type": "string", "value": ""}, "patient_zip_code_na": {"field_id": "2066", "label": "Patient Zip Code NA", "options": ["True", "False"], "input_type": "string", "value": ""}, "hispanic_or_latino_ethnicity": {"field_id": "2076", "label": "Hispanic or Latino Ethnicity", "options": ["True", "False"], "input_type": "radio", "value": ""}, "race": {"field_id": "unknown", "label": "Race", "options": [{"id": "", "value": "White"}, {"id": "", "value": "Black/African American"}, {"id": "", "value": "Asian"}, {"id": "", "value": "American Indian/Alaskan Native"}, {"id": "", "value": "Native Hawaiian/Pacific Islander"}], "input_type": "select", "value": "id"}}, "physical_exam_and_labs": {"height": {"field_id": "6000", "label": "Height", "metric": "cm", "input_type": "string", "value": ""}, "weight": {"field_id": "6005", "label": "Weight", "metric": "kg", "input_type": "string", "value": ""}, "pulse": {"field_id": "6010", "label": "Pulse", "metric": "bpm", "input_type": "string", "value": ""}, "blood_pressure": {"field_id": "6015/6020", "label": "Blood Pressure", "metric": "mmHg", "input_type": "string", "value": ""}, "hemoglobin": {"field_id": "6030", "label": "Hemoglobin", "options": [{"id": "g_dl", "value": "g/dL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "prothrombin_time": {"field_id": "6040", "label": "Prothrombin Time (PT)", "options": [{"id": "sec", "value": "sec"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "inr": {"field_id": "6045", "label": "INR", "options": [{"id": "not_drawn", "value": "Not Drawn"}], "input_type": "string", "value": ""}, "creatinine": {"field_id": "6050", "label": "Creatinine", "options": [{"id": "mg_dl", "value": "mg/dL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "albumin": {"field_id": "14210", "label": "Albumin", "options": [{"id": "g_dl", "value": "g/dL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "platelet_count": {"field_id": "13213", "label": "Platelet Count", "options": [{"id": "ul", "value": "µL"}, {"id": "not_drawn", "value": "Not Drawn"}], "input_type": "select", "value": ""}, "modified_rankin_scale": {"field_id": "14805", "label": "Modified Rankin Scale (mRS)", "options": [{"id": "0", "value": "0: No symptoms at all"}, {"id": "1", "value": "1: No significant disability despite symptoms"}, {"id": "2", "value": "2: Slight disability"}, {"id": "3", "value": "3: Moderate disability"}, {"id": "4", "value": "4: Moderately severe disability"}, {"id": "5", "value": "5: Severe disability"}, {"id": "not_administered", "value": "Not Administered"}], "input_type": "select", "value": ""}}, "post_procedure_labs": {"peak_creatinine": {"field_id": "14868", "label": "Peak Creatinine", "metric": "mg/dL", "not_drawn_field_id": "14870", "input_type": "string", "value": ""}, "hemoglobin_lowest": {"field_id": "14871", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lowest)", "metric": "g/dL", "not_drawn_field_id": "14872", "input_type": "string", "value": ""}, "creatinine_at_discharge": {"field_id": "14869", "label": "Creatinine (at Discharge)", "metric": "mg/dL", "not_drawn_field_id": "14867", "input_type": "string", "value": ""}}, "pre_procedure_diagnostics": {"transesophageal_echocardiogram_tee_performed": {"field_id": "14828", "label": "Transesophageal Echocardiogram (TEE) Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "most_recent_tee_date": {"field_id": "14829", "label": "Most Recent TEE Date", "input_type": "string", "value": ""}, "atrial_thrombus_detected": {"field_id": "14838", "label": "Atrial Thrombus Detected", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "laa_orifice_maximal_width": {"field_id": "14830", "label": "LAA Orifice Maximal Width", "input_type": "string", "value": ""}}, "pre_procedure_medications": {"fondaparinux": {"field_id": "6985", "label": "Fondaparinux", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "heparin_derivative": {"field_id": "6985", "label": "Heparin Derivative", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "low_molecular_weight_heparin": {"field_id": "6985", "label": "Low Molecular Weight Heparin", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "unfractionated_heparin": {"field_id": "6985", "label": "Unfractionated Heparin", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "warfarin": {"field_id": "6985", "label": "Warfarin", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "aspirin_81_100_mg": {"field_id": "6985", "label": "Aspirin (81-100 mg)", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "aspirin_101_324_mg": {"field_id": "6985", "label": "Aspirin (101-324 mg)", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "aspirin_325_mg": {"field_id": "6985", "label": "Aspirin 325 mg", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "aspirin_dipyridamole": {"field_id": "6985", "label": "Aspirin/Dipyridamole", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "vorapaxar": {"field_id": "6985", "label": "Vorapaxar", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "apixaban": {"field_id": "6985", "label": "Apixaban", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "dabigatran": {"field_id": "6985", "label": "Dabigatran", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "edoxaban": {"field_id": "6985", "label": "Edoxaban", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "rivaroxaban": {"field_id": "6985", "label": "Rivaroxaban", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "cangrelor": {"field_id": "6985", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "clopidogrel": {"field_id": "6985", "label": "Clopidogrel", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "other_p2y12": {"field_id": "6985", "label": "Other P2Y12", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "prasugrel": {"field_id": "6985", "label": "Prasug<PERSON>", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select"}, "ticagrelor": {"field_id": "6985", "label": "Ticagrelor", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}, "ticlopidine": {"field_id": "6985", "label": "Ticlopidine", "options": [{"id": "1", "value": "Past"}, {"id": "2", "value": "Current"}, {"id": "3", "value": "Held"}, {"id": "4", "value": "Never"}], "input_type": "select", "value": "id"}}, "procedureInfo": {"procedure": {"procedure_start_date_and_time": {"field_id": "7000", "label": "Procedure Start Date and Time", "input_type": "text"}, "procedure_stop_date_and_time": {"field_id": "7005", "label": "Procedure Stop Date and Time", "input_type": "text"}, "operator_name_npi": {"field_id": "14861, 14860, 14862/14863", "label": "Operator Name/NPI", "input_type": "text"}, "fellow_name_npi_fellowship_program_id": {"field_id": "15433, 15434, 15435/15436/15431", "label": "Fellow Name/NPI/Fellowship Program ID", "input_type": "text"}, "shared_decision_making": {"field_id": "14732", "label": "Shared Decision Making", "options": ["No", "Yes"], "input_type": "radio"}, "sdm_tool_used": {"field_id": "14733", "label": "SDM Tool Used", "options": ["No", "Yes"], "input_type": "radio"}, "sdm_tool_name": {"field_id": "14734", "label": "SDM Tool Name", "input_type": "text"}, "procedure_location": {"field_id": "12871", "label": "Procedure Location", "options": [{"id": "0", "value": "OR"}, {"id": "1", "value": "Hybrid OR"}, {"id": "2", "value": "Cath Lab"}, {"id": "3", "value": "Hybrid Cath Lab"}, {"id": "4", "value": "EP Lab"}], "input_type": "select"}, "sedation": {"field_id": "7130", "label": "Sedation", "options": [{"id": "0", "value": "Minimal Sedation/Anxiolysis"}, {"id": "1", "value": "Deep Sedation/Analgesia"}, {"id": "2", "value": "Moderate Sedation/Analgesia (Conscious Sedation)"}, {"id": "3", "value": "General Anesthesia"}], "input_type": "select"}, "laa_occlusion_indication": {"field_id": "14837", "label": "LAA Occlusion Indication", "options": [{"id": "0", "value": "High fall risk"}, {"id": "1", "value": "History of major bleed"}, {"id": "2", "value": "Clinically significant bleeding risk (Other than those listed here)"}, {"id": "3", "value": "Increased thromboembolic stroke risk"}, {"id": "4", "value": "Labile INR"}, {"id": "5", "value": "Non-compliance with anticoagulation therapy"}, {"id": "6", "value": "Patient preference"}], "input_type": "select"}, "procedure_canceled": {"field_id": "14834", "label": "Procedure Canceled", "options": ["No", "Yes"], "input_type": "radio"}, "procedure_canceled_reason": {"field_id": "14833", "label": "Procedure Canceled Reason", "options": [{"id": "0", "value": "Anatomy not conducive for implant"}, {"id": "1", "value": "Appendage too large (for device implant)"}, {"id": "2", "value": "Appendage too small (for device implant)"}, {"id": "3", "value": "Catheterization challenge"}, {"id": "4", "value": "Decompensation in patient condition"}, {"id": "5", "value": "Epicardial access issue"}, {"id": "6", "value": "<PERSON><PERSON><PERSON><PERSON> detected"}, {"id": "7", "value": "Unanticipated patient condition"}, {"id": "8", "value": "Patient/Family choice"}], "input_type": "select"}}, "device_information": {"access_system": {"field_id": "14839", "label": "Access System", "input_type": "text"}, "device": {"field_id": "14841", "label": "<PERSON><PERSON>", "input_type": "text"}, "udi": {"field_id": "14843", "label": "UDI", "input_type": "text"}, "laa_isolation_approach": {"field_id": "14844", "label": "LAA Isolation Approach", "options": ["Epicardial", "Percutaneous"], "input_type": "radio"}, "device_successfully_deployed": {"field_id": "14968", "label": "<PERSON>ce Successfully Deployed", "options": ["No", "Yes"], "input_type": "radio"}, "reason_device_not_deployed_successfully": {"field_id": "14845", "label": "Reason Device Not Deployed Successfully", "options": [{"id": "0", "value": "Deployed, not released"}, {"id": "1", "value": "Not deployed"}, {"id": "2", "value": "<PERSON><PERSON> retrieved"}], "input_type": "select"}}, "procedure_aborted": {"procedure_aborted": {"field_id": "14831", "label": "Procedure Aborted", "options": ["No", "Yes"], "input_type": "radio"}, "procedure_aborted_reason": {"field_id": "14832", "label": "Procedure Aborted Reason", "options": [{"id": "0", "value": "Anatomy not conducive for implant"}, {"id": "1", "value": "Appendage too large (for device implant)"}, {"id": "2", "value": "Appendage too small (for device implant)"}, {"id": "3", "value": "Catheterization challenge"}, {"id": "4", "value": "Decompensation in patient condition"}, {"id": "5", "value": "Device related"}, {"id": "6", "value": "Transcatheter device retrieval"}, {"id": "7", "value": "Device release criteria not met"}, {"id": "8", "value": "Epicardial access issue"}, {"id": "9", "value": "Surgical device retrieval"}, {"id": "10", "value": "Device associated thrombus developed during procedure"}, {"id": "11", "value": "Unanticipated patient condition"}, {"id": "12", "value": "Patient/Family choice"}], "input_type": "select"}}, "device_margin_residual_leak": {"device_margin_residual_leak": {"field_id": "14848", "label": "<PERSON><PERSON> Residual Leak", "input_type": "text"}, "not_assessed": {"field_id": "14849", "label": "Not Assessed", "input_type": "radio"}}, "guidance_method": {"guidance_method": {"field_id": "7200", "label": "Guidance Method(s)", "options": [{"id": "0", "value": "Intracardiac 3D Echo"}, {"id": "1", "value": "Electro Anatomic Mapping"}, {"id": "2", "value": "Fluoroscopy"}, {"id": "3", "value": "Transesophageal Echo (TEE)"}], "input_type": "select"}}, "conversion_to_open_heart_surgery": {"conversion_to_open_heart_surgery": {"field_id": "14846", "label": "Conversion to Open Heart Surgery", "options": ["No", "Yes"], "input_type": "radio"}, "reason": {"field_id": "14847", "label": "Reason", "options": [{"id": "0", "value": "Complication"}, {"id": "1", "value": "Device Retrieval"}, {"id": "2", "value": "Unfavorable Anatomy"}, {"id": "3", "value": "Medical decision for open ligation of appendage"}], "input_type": "select"}}, "concomitant_procedure": {"concomitant_procedure_performed": {"field_id": "14855", "label": "Concomitant Procedure(s) Performed", "options": ["No", "Yes"], "input_type": "radio"}, "concomitant_procedure_type": {"field_id": "14857", "label": "Concomitant Procedure Type", "options": [{"id": "0", "value": "AFib Ablation"}, {"id": "1", "value": "ICD"}, {"id": "2", "value": "PCI"}, {"id": "3", "value": "TAVR"}, {"id": "4", "value": "TMVR"}, {"id": "5", "value": "ASD Closure Congenital"}, {"id": "6", "value": "ASD Closure Iatrogenic"}, {"id": "7", "value": "PFO Closure Congenital"}], "input_type": "select"}}, "device_systemic_embolization": {"device_systemic_embolization_surgical_retrieval": {"field_id": "14856", "label": "Device Systemic Embolization (surgical retrieval)", "options": ["No", "Yes"], "input_type": "radio"}}}, "systemic_thromboembolism": {"adjudication_status": {"field_id": "14932", "label": "Adjudication Status", "options": ["Alive", "Deceased"], "input_type": "radio", "value": ""}, "adjudication_date_of_death": {"field_id": "14933", "label": "Adjudication Date of Death", "format": "mm/dd/yyyy", "input_type": "string", "value": ""}, "death_cause": {"field_id": "14934", "label": "Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention)", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "focal_end_organ_hypoperfusion_present": {"field_id": "14935", "label": "Focal End-Organ Hypoperfusion Present", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "systemic_thromboembolization_imaging_evidence": {"field_id": "14939", "label": "Systemic Thromboembolization Imaging Evidence", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "imaging_method": {"field_id": "14936", "label": "Imaging Method", "options": [{"id": "", "value": "Angiography"}, {"id": "", "value": "Computed Tomography"}, {"id": "", "value": "Magnetic Resonance Imaging"}, {"id": "", "value": "Ultrasound"}, {"id": "", "value": "Other Imaging"}], "input_type": "select", "value": "id"}, "therapeutic_intervention_performed": {"field_id": "14937", "label": "Therapeutic Intervention Performed", "options": ["No", "Yes"], "input_type": "radio", "value": ""}, "intervention_type": {"field_id": "14938", "label": "Intervention Type", "options": [{"id": "", "value": "Catheter"}, {"id": "", "value": "Pharmacological"}, {"id": "", "value": "Surgical"}, {"id": "", "value": "Other"}], "input_type": "select", "value": "id"}, "current_medications_at_time_of_event": {"field_id": "14941", "label": "Current Medications at Time of Event", "medications": {"fondaparinux": {"field_id": "14940", "options": ["Yes", "No"], "input_type": "radio", "value": ""}, "heparin_derivative": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "low_molecular_weight_heparin": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "unfractionated_heparin": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "warfarin": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_81_100_mg": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_101_324_mg": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_325_mg": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "aspirin_dipyridamole": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "vorapaxar": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "apixaban": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "dabigatran": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "edoxaban": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "rivaroxaban": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "cangrelor": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "clopidogrel": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "other_p2y12": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "prasugrel": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "ticagrelor": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}, "ticlopidine": {"options": ["Yes", "No"], "input_type": "radio", "value": ""}}}}}