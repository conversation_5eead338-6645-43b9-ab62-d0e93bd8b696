<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <title>Chat Interface</title>
    <link rel="stylesheet" href="index.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="../config.js"></script>
    <script defer src="index.js"></script>
  </head>
  <body class="h-screen flex flex-col bg-[#f8f6ff]">
    <!-- Clear History Button (fixed in top-right corner) -->
    <i
      id="clear-history-btn"
      class="fixed top-2 right-2 z-50 p-2 bg-red-600 text-white rounded fa fa-trash-can"
    ></i>

    <!-- Image Modal -->
    <div
      id="image-modal"
      class="fixed inset-0 bg-black bg-opacity-75 z-50 flex justify-center items-center hidden"
    >
      <div class="relative">
        <button
          id="close-modal"
          class="z-50 absolute top-2 right-2 text-white text-2xl font-bold"
        >
          &times;
        </button>
        <img
          id="modal-image"
          src=""
          alt="Zoomed View"
          class="max-w-full max-h-screen rounded-lg zoomable"
        />
      </div>
    </div>

    <!-- Clear History Confirmation Modal -->
    <div
      id="clear-history-modal"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center hidden"
    >
      <div class="bg-white p-6 rounded-lg text-center">
        <p class="mb-4">Are you sure you want to clear chat history?</p>
        <button
          id="confirm-clear-btn"
          class="px-4 py-2 bg-red-600 text-white rounded mr-2"
        >
          Yes
        </button>
        <button
          id="cancel-clear-btn"
          class="px-4 py-2 bg-gray-300 text-black rounded"
        >
          No
        </button>
      </div>
    </div>

    <div
      id="chat-container"
      class="mt-10 flex-1 p-4 overflow-y-auto max-h-screen bg-[#f8f6ff]"
    >
      <div class="flex items-start my-2"></div>
    </div>

    <div class="flex items-center gap-2 p-4 border-t bg-[#f8f6ff]">
      <div class="relative w-full">
        <!-- Input Wrapper -->
        <textarea
          id="chat-input"
          rows="1"
          maxlength="2000"
          class="w-full px-4 pt-2 pb-4 text-black bg-white border border-gray-300 rounded-lg shadow-md focus:outline-none min-h-12 overflow-y-auto"
          placeholder="Type your message here..."
        ></textarea>

        <!-- Character Limit -->
        <div
          id="char-limit"
          class="absolute bottom-2 right-2 text-xs text-gray-500"
        >
          0/2000
        </div>
      </div>

      <button
        id="send-button"
        class="bg-[#8143d9] text-white p-2 rounded-lg hover:bg-purple-600 transition"
      >
        Send
      </button>
    </div>
  </body>
</html>
