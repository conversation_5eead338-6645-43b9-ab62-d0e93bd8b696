let accessToken = localStorage.getItem("access_token");
if (!accessToken) {
  window.location.href = "/";
}
const container = document.getElementById("jsonEditor");
const options = {
  mode: "tree",
  modes: ["tree", "view", "code"],
  theme: "ace/theme/monokai",
};
const editor = new JSONEditor(container, options);
getSiteConfig();

document
  .getElementById("fileInput")
  .addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        try {
          const parsedJson = JSON.parse(e.target.result);
          editor.set(parsedJson);
          editor.expandAll();
          editor.focus();
        } catch (error) {
          alert("Invalid JSON file");
        }
      };
      reader.readAsText(file);
    }
  });
document.getElementById("submitJson").addEventListener("click", (e) => {
  if (!accessToken) {
    window.location.href = "/";
    return;
  }
  if (!accessToken) {
    alert("Not Authorised");
    window.location.href = "/";
    return;
  }
  let data = editor.get();
  if (!data || Object.keys(data).length === 0) {
    alert("No data found.");
    return;
  }
  showLoader(true);

  fetch(`${config.apiUrl}/sites/etl/config`, {
    method: "PUT",
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  })
    .then(async (res) => {
      if (!res.ok) {
        throw new Error(`${res.status} - ${res.statusText}`);
      }
      return await res.json();
    })
    .then((data) => {
      alert("JSON config updated successfully..");
      showLoader(false);
    })
    .catch((error) => {
      alert(error.message);
      showLoader(false);
    });
});

function getSiteConfig() {
  if (!accessToken) {
    alert("Not Authorised");
    window.location.href = "/";
    return;
  }
  showLoader(true);
  fetch(`${config.apiUrl}/sites/etl/config`, {
    method: "GET",
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  })
    .then(async (res) => {
      if (!res.ok) {
        throw new Error(`${res.status} - ${res.statusText}`);
      }
      return await res.json();
    })
    .then((data) => {
      editor.set(data?.result?.config);
      editor.expandAll();
      showLoader(false);
    })
    .catch((error) => {
      alert(error.message);
      showLoader(false);
    });
}

function showLoader(show = true) {
  if (show) {
    document.getElementById("overlay").classList.remove("hidden");
  } else {
    document.getElementById("overlay").classList.add("hidden");
  }
}
