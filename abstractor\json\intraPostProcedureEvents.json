{"intra_or_post_procedure_events": {"verified": "", "events": {"field_id": "12153", "label": "Events", "elements": {"cardiovascular": {"air_embolism": {"field_id": "9002", "label": "Air Embolism", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "cardiac_arrest": {"field_id": "9002", "label": "Cardiac Arrest", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "heart_failure": {"field_id": "9002", "label": "Heart Failure", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "heart_valve_damage": {"field_id": "9002", "label": "Heart Valve Damage", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "left_atrial_thrombus": {"field_id": "9002", "label": "Left Atrial Thrombus", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "myocardial_infarction": {"field_id": "9002", "label": "Myocardial Infarction", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pericardial_effusion_no_intervention": {"field_id": "9002", "label": "Pericardial Effusion (no intervention required)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pericarditis": {"field_id": "9002", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "systemic": {"anaphylaxis": {"field_id": "9002", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "arterial_thrombosis": {"field_id": "9002", "label": "Arterial Thrombosis", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "deep_vein_thrombosis": {"field_id": "9002", "label": "Deep Vein Thrombosis", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "systemic_thromboembolism_other_than_stroke": {"field_id": "9002", "label": "Systemic Thromboembolism (other than stroke) (COMPLETE ADJUDICATION)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "gastrointestinal_genitourinary": {"esophageal_injury": {"field_id": "9002", "label": "Esophageal Injury (resulting from TEE probe)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "hepatic_injury": {"field_id": "9002", "label": "Hepatic Injury", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "new_requirement_for_dialysis": {"field_id": "9002", "label": "New Requirement for Dialysis", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "device": {"device_explant": {"field_id": "9002", "label": "Device Explant", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "device_infection": {"field_id": "9002", "label": "Device Infection", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "device_migration": {"field_id": "9002", "label": "<PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "device_thrombus": {"field_id": "9002", "label": "<PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "device_systemic_embolization_catheter_retrieval": {"field_id": "9002", "label": "Device Systemic Embolization (catheter retrieval)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "device_systemic_embolization_surgical_retrieval": {"field_id": "9002", "label": "Device Systemic Embolization (surgical retrieval)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "peripheral_vascular": {"av_fistula_no_intervention_required": {"field_id": "9002", "label": "AV Fistula (no intervention required)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "av_fistula_requiring_surgical_repair": {"field_id": "9002", "label": "AV Fistula (requiring surgical repair)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pseudoaneurysm_no_intervention_required": {"field_id": "9002", "label": "Pseudoaneurysm (no intervention required)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pseudoaneurysm_requiring_endovascular_repair": {"field_id": "9002", "label": "Pseudoaneurysm (requiring endovascular repair)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pseudoaneurysm_requiring_surgical_repair": {"field_id": "9002", "label": "Pseudoaneurysm (requiring surgical repair)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pseudoaneurysm_requiring_thrombin_injection": {"field_id": "9002", "label": "Pseudoaneurysm (requiring thrombin injection only)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "neurologic": {"hemorrhagic_stroke": {"field_id": "9002", "label": "Hemorrhagic Stroke", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "intracranial_hemorrhage": {"field_id": "9002", "label": "Intracranial Hemorrhage (other than hemorrhagic stroke)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "ischemic_stroke": {"field_id": "9002", "label": "Ischemic Stroke", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "tia": {"field_id": "9002", "label": "TIA", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "undetermined_stroke": {"field_id": "9002", "label": "Undetermined Stroke", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "bleeding": {"access_site_bleeding": {"field_id": "9002", "label": "Access Site Bleeding", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "gi_bleeding": {"field_id": "9002", "label": "GI Bleeding", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "hematoma": {"field_id": "9002", "label": "Hematoma", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "hemothorax_no_drainage": {"field_id": "9002", "label": "Hemothorax (not requiring drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "hemothorax_requiring_drainage": {"field_id": "9002", "label": "Hemothorax (requiring drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "other_hemorrhage_non_intracranial": {"field_id": "9002", "label": "Other Hemorrhage (non-intracranial)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pericardial_effusion_open_cardiac_surgery": {"field_id": "9002", "label": "Pericardial Effusion (requiring open cardiac surgery)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pericardial_effusion_tamponade_percutaneous": {"field_id": "9002", "label": "Pericardial Effusion with tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pericardial_effusion_without_tamponade": {"field_id": "9002", "label": "Pericardial Effusion without tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "retroperitoneal_bleeding": {"field_id": "9002", "label": "Retroperitoneal Bleeding", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "vascular_complications": {"field_id": "9002", "label": "Vascular Complications", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}, "pulmonary": {"pleural_effusion": {"field_id": "9002", "label": "Pleural Effusion", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pneumonia": {"field_id": "9002", "label": "Pneumonia", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pneumothorax_no_intervention": {"field_id": "9002", "label": "Pneumothorax (no intervention required)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pneumothorax_requiring_intervention": {"field_id": "9002", "label": "Pneumothorax (requiring intervention)", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "pulmonary_embolism": {"field_id": "9002", "label": "Pulmonary Embolism", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "respiratory_failure": {"field_id": "9002", "label": "Respiratory Failure", "options": ["No", "Yes"], "if_yes": {"field_id": "14275", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What are the dates of any intra or post-procedure events that occurred between the start of the procedure and either the next procedure or discharge?", "description": "\n**Coding Instruction:**\nIndicate all dates of intra or post procedure events that occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}, "input_type": "multi_input_field", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What intra or post-procedure events occurred during the TAVR procedure?", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any of the following intra or post-procedure events occur between the start of the TAVR procedure and discharge or the next procedure: Air Embolism, Cardiac Arrest, Heart Failure, Heart Valve Damage, Left Atrial Thrombus, Myocardial Infarction, Pericardial Effusion (no intervention required), Perica<PERSON>tis, Anaphylaxis, Arterial Thrombosis, Deep Vein Thrombosis, Systemic Thromboembolism (other than stroke), Esophageal Injury (resulting from TEE probe), Hepatic Injury, New Requirement for Dialysis, Device Explant, Device Infection, Device Migration, <PERSON>ce Thrombus, Device Systemic Embolization (catheter retrieval), Device Systemic Embolization (surgical retrieval), AV Fistula (no intervention required), AV Fistula (requiring surgical repair), Pseudoaneurysm (no intervention required), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only), Hemorrhagic Stroke, Intracranial Hemorrhage (other than hemorrhagic stroke), Ischemic Stroke, TIA, Undetermined Stroke, Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage (non-intracranial), Pericardial Effusion (requiring open heart surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications, Pleural Effusion, Pneumonia, Pneumothorax (no intervention required), Pneumothorax (requiring intervention), Pulmonary Embolism, or Respiratory Failure?\"", "description": "\n**Coding Instruction:**\nIndicate if the specific intra or post procedure event(s) occurred.\n**Target Value:**\nAny occurrence between start of procedure and until next procedure or discharge"}}}