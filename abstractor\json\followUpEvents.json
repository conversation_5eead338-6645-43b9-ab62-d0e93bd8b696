{"follow_up_events": {"verified": "", "follow_up_event": {"field_id": "14948", "label": "Follow-up Event", "event_occurred": {"cardiovascular": {"endocarditis": {"field_id": "14276", "label": "Endocarditis", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "iatrogenic_asd": {"field_id": "14276", "label": "Iatrogenic ASD (requiring intervention)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "laa_occlusion_reintervention": {"field_id": "14276", "label": "LAA Occlusion Reintervention", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "myocardial_infarction": {"field_id": "14276", "label": "Myocardial Infarction", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pci": {"field_id": "14276", "label": "PCI", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pericarditis": {"field_id": "14276", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "unplanned_cardiac_surgery": {"field_id": "14276", "label": "Unplanned Cardiac Surgery", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "unplanned_intervention": {"field_id": "14276", "label": "Unplanned Intervention", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}, "systemic": {"deep_vein_thrombosis": {"field_id": "14276", "label": "Deep Vein Thrombosis", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "new_requirement_for_dialysis": {"field_id": "14276", "label": "New Requirement for Dialysis", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "non_device_related_readmission": {"field_id": "14276", "label": "Non-Device Related Readmission", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "systemic_thromboembolism": {"field_id": "14276", "label": "Systemic Thromboembolism (other than stroke) (COMPLETE ADJUDICATION)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}, "device": {"device_explant": {"field_id": "14276", "label": "Device Explant", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "device_fracture": {"field_id": "14276", "label": "Device Fracture", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "device_migration": {"field_id": "14276", "label": "<PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "device_related_readmission": {"field_id": "14276", "label": "Device Related Readmission", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "device_systemic_embolism": {"field_id": "14276", "label": "Device Systemic Embolism", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "device_thrombus": {"field_id": "14276", "label": "<PERSON><PERSON>", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}, "neurologic": {"hemorrhagic_stroke": {"field_id": "14276", "label": "Hemorrhagic Stroke", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "intracranial_hemorrhage": {"field_id": "14276", "label": "Intracranial Hemorrhage (other than hemorrhagic stroke)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "ischemic_stroke": {"field_id": "14276", "label": "Ischemic Stroke", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "tia": {"field_id": "14276", "label": "TIA", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "undetermined_stroke": {"field_id": "14276", "label": "Undetermined Stroke", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}, "bleeding_complete_adjudication": {"access_site_bleeding": {"field_id": "14276", "label": "Access Site Bleeding", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "gi_bleeding": {"field_id": "14276", "label": "GI Bleeding", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "hematoma": {"field_id": "14276", "label": "Hematoma", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "hemothorax_not_requiring_drainage": {"field_id": "14276", "label": "Hemothorax (not requiring drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "hemothorax_requiring_drainage": {"field_id": "14276", "label": "Hemothorax (requiring drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "other_hemorrhage_non_intracranial": {"field_id": "14276", "label": "Other Hemorrhage (non-intracranial)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pericardial_effusion_requiring_open_cardiac_surgery": {"field_id": "14276", "label": "Pericardial Effusion (requiring open cardiac surgery)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pericardial_effusion_with_tamponade_requiring_percutaneous_drainage": {"field_id": "14276", "label": "Pericardial Effusion with tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pericardial_effusion_without_tamponade_requiring_percutaneous_drainage": {"field_id": "14276", "label": "Pericardial Effusion without tamponade (requiring percutaneous drainage)", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "retroperitoneal_bleeding": {"field_id": "14276", "label": "Retroperitoneal Bleeding", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "vascular_complications": {"field_id": "14276", "label": "Vascular Complications", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}, "peripheral_vascular": {"limb_ischemia": {"field_id": "14276", "label": "Limb Ischemia", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "vascular_intervention_or_surgery": {"field_id": "14276", "label": "Vascular Intervention or Surgery", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}, "pulmonary": {"mechanical_ventilation_greater_than_48h": {"field_id": "14276", "label": "Mechanical Ventilation > 48 hrs", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pulmonary_embolism": {"field_id": "14276", "label": "Pulmonary Embolism", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "pneumonia": {"field_id": "14276", "label": "Pneumonia", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "respiratory_failure": {"field_id": "14276", "label": "Respiratory Failure", "options": ["No", "Yes"], "if_yes": {"field_id": "14277", "label": "Event Date", "input_type": "date", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What is the date the follow-up event occurred?", "description": "\n**Coding Instruction:**\nIndicate the date the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}, "input_type": "radio", "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "Did any follow-up events occur?", "description": "\n**Coding Instruction:**\nIndicate if the event occurred.\n**Target Value:**\nAny occurrence on follow-up"}}}, "value": "", "modified_by": "", "value_source": "Embeddings", "q_prompt": "What follow-up events from the provided list have occurred since the last follow-up or discharge? Please specify the event name, whether it occurred, and the date it occurred. The events to consider include:\n\n- Endocarditis\n- Iatrogenic ASD (requiring intervention)\n- LAA Occlusion Reintervention\n- Myocardial Infarction\n- PCI\n- Pericarditis\n- Unplanned Cardiac Surgery\n- Unplanned Intervention\n- Deep Vein Thrombosis\n- New Requirement for Dialysis\n- Non-Device Related Readmission\n- Systemic Thromboembolism (other than stroke) (Complete Adjudication)\n- Device Explant\n- Device Fracture\n- Device Migration\n- Device Related Readmission\n- Device Systemic Embolism\n- Device Thrombus\n- Hemorrhagic Stroke (Complete Adjudication)\n- Intracranial Hemorrhage (other than hemorrhagic stroke) (Complete Adjudication)\n- Ischemic Stroke (Complete Adjudication)\n- TIA (Complete Adjudication)\n- Undetermined Stroke (Complete Adjudication)\n- Access Site Bleeding (Complete Adjudication)\n- GI Bleeding (Complete Adjudication)\n- Hematoma (Complete Adjudication)\n- Hemothorax (not requiring drainage) (Complete Adjudication)\n- Hemothorax (requiring drainage) (Complete Adjudication)\n- Other Hemorrhage (non-intracranial) (Complete Adjudication)\n- Pericardial Effusion (requiring open cardiac surgery) (Complete Adjudication)\n- Pericardial Effusion with tamponade (requiring percutaneous drainage) (Complete Adjudication)\n- Pericardial Effusion without tamponade (requiring percutaneous drainage) (Complete Adjudication)\n- Retroperitoneal Bleeding (Complete Adjudication)\n- Vascular Complications\n- AV Fistula (requiring surgical repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring endovascular repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring surgical repair) (Complete Adjudication)\n- Pseudoaneurysm (requiring thrombin injection only) (Complete Adjudication)\n- Pulmonary Embolism", "description": "\n**Coding Instruction:**\nIndicate if any event from the NCDR-provided list had occurred between the time of the last follow-up (or discharge if this is the 45-day follow-up) and the current follow-up.\n**Target Value:**\nThe value on Follow-up"}}}