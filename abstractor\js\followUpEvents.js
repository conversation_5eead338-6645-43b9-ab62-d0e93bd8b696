import {
  formatHeading,
  updateTileStyle,
  validateStringInput,
  formatDisplayDate,
} from "../utils.js";

function updateContainerHighlight(container, data) {
  // Check if the container is verified
  if (data.verified && data.verified.value === "True") {
    container.style.border = "2px solid green";
    container.style.borderRadius = "8px";
  } else {
    container.style.border = "2px solid red";
    container.style.borderRadius = "8px";
  }
}

// Function to render an event field with its conditional content
function renderEventField(
  eventName,
  eventData,
  eventCategory,
  categoryContainer,
  patientData
) {
  // Create a tile wrapper for the event
  const tileWrapper = document.createElement("div");
  tileWrapper.classList.add("tile-wrapper");
  tileWrapper.style.position = "relative";
  tileWrapper.style.marginBottom = "24px";

  // Create the field container
  const fieldContainer = document.createElement("div");
  fieldContainer.classList.add("field-container");
  fieldContainer.style.padding = "16px";
  fieldContainer.style.border = "1px solid #dee2e6";
  fieldContainer.style.borderRadius = "4px";
  fieldContainer.style.backgroundColor = "#fff";

  // Create and append the label element
  const label = document.createElement("label");
  label.classList.add("label", "cursor-pointer");
  label.textContent = `${eventData.label} (${eventData.field_id})`;
  if (eventData.description) {
    label.setAttribute("title", eventData.description);
  }
  fieldContainer.appendChild(label);

  // Create the modified_by display element
  const modifiedByDisplay = document.createElement("span");
  modifiedByDisplay.style.display = "block";
  modifiedByDisplay.style.textAlign = "right";
  modifiedByDisplay.style.marginTop = "-10px";
  modifiedByDisplay.style.color = "#8143d9";
  modifiedByDisplay.style.fontSize = "12px";
  if (!eventData.modified_by) {
    eventData.modified_by = "";
  }
  modifiedByDisplay.textContent = eventData.modified_by;

  // Handle different input types
  if (eventData.input_type === "radio") {
    // Use the renderSingleField function to handle radio inputs
    renderSingleField(
      eventData,
      fieldContainer,
      eventCategory,
      eventName,
      patientData
    );
  } else if (eventData.input_type === "date") {
    // Use the renderSingleField function to handle date inputs
    renderSingleField(
      eventData,
      fieldContainer,
      eventCategory,
      eventName,
      patientData
    );
  } else if (
    eventData.input_type === "string" ||
    eventData.input_type === "text"
  ) {
    // Use the renderSingleField function to handle string/text inputs
    renderSingleField(
      eventData,
      fieldContainer,
      eventCategory,
      eventName,
      patientData
    );
  } else if (eventData.input_type === "select") {
    // Use the renderSingleField function to handle select inputs
    renderSingleField(
      eventData,
      fieldContainer,
      eventCategory,
      eventName,
      patientData
    );
  } else if (eventData.input_type === "multi_select") {
    // Use the renderSingleField function to handle multi_select inputs
    renderSingleField(
      eventData,
      fieldContainer,
      eventCategory,
      eventName,
      patientData
    );
  } else if (eventData.input_type === "multi_input_field") {
    // Use the renderSingleField function to handle multi_input_field inputs
    renderSingleField(
      eventData,
      fieldContainer,
      eventCategory,
      eventName,
      patientData
    );
  }

  // Apply initial border styling
  updateTileStyle(fieldContainer, eventData.value || "");

  tileWrapper.appendChild(fieldContainer);
  tileWrapper.appendChild(modifiedByDisplay);
  categoryContainer.appendChild(tileWrapper);
}

// Function to render conditional content (if_yes, if_no, if_alive, if_deceased fields)
function renderConditionalContent(
  selectedOption,
  parentField,
  container,
  eventCategory,
  eventName,
  patientData,
  parentPath = []
) {
  // Clear previous content
  container.innerHTML = "";
  container.style.display = "none";

  // Determine which conditional key to use based on selected option
  let conditionalKey = null;
  if (selectedOption === "Yes" && parentField.if_yes) {
    conditionalKey = "if_yes";
  } else if (selectedOption === "No" && parentField.if_no) {
    conditionalKey = "if_no";
  } else if (selectedOption === "Alive" && parentField.if_alive) {
    conditionalKey = "if_alive";
  } else if (selectedOption === "Deceased" && parentField.if_deceased) {
    conditionalKey = "if_deceased";
  }

  // If we don't have a conditional key or data, return early
  if (!conditionalKey || !parentField[conditionalKey]) {
    return;
  }

  // Show the container since we have conditional content
  container.style.display = "block";

  // Get the conditional data
  const conditionalData = parentField[conditionalKey];

  // If the conditional data is a simple field (not an object with subfields)
  if (conditionalData.input_type) {
 
    // For date fields, we need special handling to ensure the label is displayed
  

    renderSingleField(
      conditionalData,
      container,
      eventCategory,
      eventName,
      patientData,
      conditionalKey,
      parentPath
    );
  } else {
    // Handle object with multiple subfields
    Object.entries(conditionalData).forEach(([subKey, subField]) => {
      // Skip non-field properties
      if (typeof subField !== "object" || !subField.label) {
        return;
      }

      // Create a container for this sub-field
      const subFieldContainer = document.createElement("div");
      subFieldContainer.classList.add("sub-field-container");
      subFieldContainer.style.marginBottom = "15px";

      // Create and append the label
      const subLabel = document.createElement("label");
      subLabel.classList.add("label", "cursor-pointer");
      subLabel.textContent = `${subField.label} ${
        subField.field_id ? `(${subField.field_id})` : ""
      }`;
      if (subField.description) {
        subLabel.setAttribute("title", subField.description);
      }
      subFieldContainer.appendChild(subLabel);

      // Create the appropriate input based on sub-field type
      renderSingleField(
        subField,
        subFieldContainer,
        eventCategory,
        eventName,
        patientData,
        conditionalKey,
        [...parentPath, { key: subKey }]
      );

      container.appendChild(subFieldContainer);
    });
  }
}

// Helper function to render a single field based on its input type
function renderSingleField(
  fieldData,
  container,
  eventCategory,
  eventName,
  patientData,
  conditionalKey = null,
  parentPath = []
) {
  // Initialize the field value if not set
  if (fieldData.value === undefined) {
    fieldData.value = "";
  }

  // Function to update the field value in the patientData object
  const updateFieldValue = (newValue, modifiedBy = "ABSTRACTOR") => {
    // Start with the base path
    let dataRef =
      patientData.follow_up_events.follow_up_event.event_occurred[
        eventCategory
      ][eventName];

    // If we have a conditional key, add it to the path
    if (conditionalKey) {
      dataRef = dataRef[conditionalKey];
    }

    // Navigate through any additional parent paths
    for (const pathSegment of parentPath) {
      if (pathSegment.key) {
        dataRef = dataRef[pathSegment.key];
      }
    }

    // Update the value and modified_by
    dataRef.value = newValue;
    dataRef.modified_by = modifiedBy;

    // Update UI
    updateContainerHighlight(
      document.getElementById("followUpEvents"),
      patientData.follow_up_events
    );
  };

  // Handle different input types
  if (fieldData.input_type === "date") {
    const dateWrapper = document.createElement("div");
    dateWrapper.style.position = "relative";

    // Check if we're in conditional content (conditionalKey is not null)
    // In conditional content, we need to add a label since the parent function doesn't add one
    if (conditionalKey) {
   
      // Create and append the label for the date field
      const dateLabel = document.createElement("label");
      dateLabel.classList.add("label", "cursor-pointer");
      dateLabel.textContent = `${fieldData.label} ${
        fieldData.field_id ? `(${fieldData.field_id})` : ""
      }`;
      if (fieldData.description) {
        dateLabel.setAttribute("title", fieldData.description);
      }
      dateLabel.style.display = "block";
      dateLabel.style.marginBottom = "5px";
      dateLabel.style.fontWeight = "normal";
      dateWrapper.appendChild(dateLabel);
    }

    // Create display input
    const displayInput = document.createElement("input");
    displayInput.type = "text";
    displayInput.name = `${eventCategory}-${eventName}-date-display`;
    displayInput.readOnly = true;
    displayInput.value = formatDisplayDate(fieldData.value);
    displayInput.placeholder = "MM/DD/YYYY";
    displayInput.style.cursor = "pointer";
    displayInput.style.marginTop = "5px";
    displayInput.style.width = "100%";
    displayInput.style.padding = "8px 12px";
    displayInput.style.border = "1px solid #ccc";
    displayInput.style.borderRadius = "4px";

    // Hidden date input
    const dateInput = document.createElement("input");
    dateInput.type = "date";
    dateInput.name = `${eventCategory}-${eventName}-date`;
    dateInput.value = fieldData.value || "";
    dateInput.style.position = "absolute";
    dateInput.style.opacity = "0";
    dateInput.style.cursor = "pointer";

    // Set max date to today
    const today = new Date().toISOString().split("T")[0];
    dateInput.max = today;

    dateInput.addEventListener("change", (e) => {
      const selectedDate = e.target.value;
      displayInput.value = formatDisplayDate(selectedDate);
      updateFieldValue(selectedDate);
    });

    // Trigger date picker when clicking display input
    displayInput.addEventListener("click", () => {
      dateInput.showPicker();
    });

    dateWrapper.appendChild(displayInput);
    dateWrapper.appendChild(dateInput);
    container.appendChild(dateWrapper);
  } else if (
    fieldData.input_type === "string" ||
    fieldData.input_type === "text"
  ) {
    // Create text input for string/text fields
    const input = document.createElement("input");
    input.type = "text";
    input.name = `${eventCategory}-${eventName}-text`;
    input.placeholder = `Enter ${fieldData.label}`;
    input.value = fieldData.value || "";
    input.style.marginTop = "5px";
    input.style.width = "100%";

    let previousValue = input.value;

    input.addEventListener("input", (e) => {
      const currentValue = e.target.value;

      // Use validation utilities with temporary callback
      let isValid = false;
      validateStringInput(
        currentValue,
        fieldData.field_id,
        (validatedValue) => {
          isValid = true;
          previousValue = validatedValue;

          // Update model and UI only if value is valid
          updateFieldValue(validatedValue);

          // Update input value if validation modified it
          if (validatedValue !== currentValue) {
            e.target.value = validatedValue;
          }
        }
      );

      // Revert to previous value if validation failed
      if (!isValid) {
        e.target.value = previousValue;
      }
    });

    container.appendChild(input);
  } else if (fieldData.input_type === "radio") {
    const radioContainer = document.createElement("div");
    radioContainer.classList.add("radio-container");
    radioContainer.style.display = "flex";
    radioContainer.style.flexDirection = "row";
    radioContainer.style.flexWrap = "nowrap";
    radioContainer.style.gap = "15px";
    radioContainer.style.marginTop = "5px";

    // Create a container for nested conditional content
    const nestedConditionalContainer = document.createElement("div");
    nestedConditionalContainer.classList.add("nested-conditional-container");
    nestedConditionalContainer.style.marginTop = "10px";
    nestedConditionalContainer.style.padding = "10px";
    nestedConditionalContainer.style.border = "1px solid #eee";
    nestedConditionalContainer.style.borderRadius = "4px";
    nestedConditionalContainer.style.display = "none";
    nestedConditionalContainer.style.width = "100%";

    fieldData.options.forEach((option) => {
      const radioWrapper = document.createElement("div");
      radioWrapper.style.display = "inline-flex";
      radioWrapper.style.alignItems = "center";
      radioWrapper.style.marginRight = "20px";
      radioWrapper.style.whiteSpace = "nowrap";

      const input = document.createElement("input");
      input.type = "radio";
      input.name = `${eventCategory}-${eventName}-radio`;
      input.value = option;
      input.id = `${eventCategory}-${eventName}-${option}`;
      input.checked = fieldData.value === option;

      input.addEventListener("change", (e) => {
        if (e.target.checked) {
          // Update the field value
          updateFieldValue(e.target.value);

          // Handle conditional content if this is a multi_input_field
          if (
            fieldData.if_yes ||
            fieldData.if_no ||
            fieldData.if_alive ||
            fieldData.if_deceased
          ) {
            // Clear and render conditional content
            renderConditionalContent(
              e.target.value,
              fieldData,
              nestedConditionalContainer,
              eventCategory,
              eventName,
              patientData,
              parentPath
            );
          }
        }
      });

      const optionLabel = document.createElement("label");
      optionLabel.setAttribute(
        "for",
        `${eventCategory}-${eventName}-${option}`
      );
      optionLabel.innerText = option;
      if (fieldData.description) {
        optionLabel.setAttribute("title", fieldData.description);
      }

      radioWrapper.appendChild(input);
      radioWrapper.appendChild(optionLabel);
      radioContainer.appendChild(radioWrapper);
    });

    // If a radio option is already selected and has conditional content, render it
    if (fieldData.value) {
      if (
        (fieldData.value === "Yes" && fieldData.if_yes) ||
        (fieldData.value === "No" && fieldData.if_no) ||
        (fieldData.value === "Alive" && fieldData.if_alive) ||
        (fieldData.value === "Deceased" && fieldData.if_deceased)
      ) {
        renderConditionalContent(
          fieldData.value,
          fieldData,
          nestedConditionalContainer,
          eventCategory,
          eventName,
          patientData,
          parentPath
        );
      }
    }

    container.appendChild(radioContainer);
    container.appendChild(nestedConditionalContainer);
  } else if (fieldData.input_type === "select") {
    const select = document.createElement("select");
    select.name = `${eventCategory}-${eventName}-select`;
    select.style.marginTop = "5px";
    select.style.width = "100%";

    // Add a default option if no value is set
    if (!fieldData.value) {
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.innerText = "Select an option";
      defaultOption.disabled = true;
      defaultOption.selected = true;
      select.appendChild(defaultOption);
    }

    fieldData.options.forEach((option) => {
      const optionElement = document.createElement("option");
      optionElement.value = option.id;
      optionElement.innerText = option.value;
      if (fieldData.value && fieldData.value === option.value) {
        optionElement.selected = true;
      }
      select.appendChild(optionElement);
    });

    select.addEventListener("change", (e) => {
      const selectedOption = fieldData.options.find(
        (option) => option.id === e.target.value
      );
      const newValue = selectedOption ? selectedOption.value : "";
      updateFieldValue(newValue);
    });

    container.appendChild(select);
  } else if (fieldData.input_type === "multi_select") {
    // Initialize the value as an array if it's not already
    if (!Array.isArray(fieldData.value)) {
      fieldData.value = fieldData.value ? [fieldData.value] : [];
    }

    // Create dropdown container with relative positioning
    const dropdownContainer = document.createElement("div");
    dropdownContainer.classList.add("dropdown-container");
    dropdownContainer.style.position = "relative";
    dropdownContainer.style.width = "100%";
    dropdownContainer.style.marginTop = "5px";

    // Make sure all parent containers allow overflow
    container.style.overflow = "visible";

    // Create dropdown header/button
    const dropdownHeader = document.createElement("div");
    dropdownHeader.classList.add("dropdown-header");
    dropdownHeader.style.padding = "8px 12px";
    dropdownHeader.style.border = "1px solid #ccc";
    dropdownHeader.style.borderRadius = "4px";
    dropdownHeader.style.cursor = "pointer";
    dropdownHeader.style.display = "flex";
    dropdownHeader.style.justifyContent = "space-between";
    dropdownHeader.style.alignItems = "center";
    dropdownHeader.style.backgroundColor = "#fff";

    // Display selected values or placeholder
    const selectedText = document.createElement("span");
    selectedText.classList.add("selected-text");

    // Function to update the selected text display
    const updateSelectedText = () => {
      if (fieldData.value.length === 0) {
        selectedText.textContent = "Select options...";
      } else if (fieldData.value.length === 1) {
        const selectedOption = fieldData.options.find(
          (opt) => opt.value === fieldData.value[0]
        );
        selectedText.textContent = selectedOption
          ? selectedOption.value
          : fieldData.value[0];
      } else {
        selectedText.textContent = `${fieldData.value.length} options selected`;
      }
    };

    updateSelectedText();

    // Add dropdown arrow
    const dropdownArrow = document.createElement("span");
    dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

    dropdownHeader.appendChild(selectedText);
    dropdownHeader.appendChild(dropdownArrow);

    // Create dropdown content (initially hidden)
    const dropdownContent = document.createElement("div");
    dropdownContent.classList.add("dropdown-content");
    dropdownContent.style.display = "none";
    dropdownContent.style.position = "absolute";
    dropdownContent.style.width = "100%";
    dropdownContent.style.maxHeight = "200px";
    dropdownContent.style.overflowY = "auto";
    dropdownContent.style.backgroundColor = "#fff";
    dropdownContent.style.border = "1px solid #ccc";
    dropdownContent.style.borderRadius = "4px";
    dropdownContent.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
    dropdownContent.style.zIndex = "1000";
    dropdownContent.style.top = "100%";
    dropdownContent.style.left = "0";

    // Create a checkbox for each option
    fieldData.options.forEach((option) => {
      const checkboxWrapper = document.createElement("div");
      checkboxWrapper.classList.add("checkbox-wrapper");
      checkboxWrapper.style.display = "flex";
      checkboxWrapper.style.alignItems = "center";
      checkboxWrapper.style.padding = "8px 12px";
      checkboxWrapper.style.borderBottom = "1px solid #eee";
      checkboxWrapper.style.cursor = "pointer";

      const input = document.createElement("input");
      input.type = "checkbox";
      input.name = `${eventCategory}-${eventName}-${option.id}`;
      input.value = option.id;
      input.id = `${eventCategory}-${eventName}-${option.id}`;
      input.style.marginRight = "8px";

      // Check if this option is in the selected values array
      input.checked = fieldData.value.includes(option.value);

      input.addEventListener("change", (e) => {
        // Get the current values array
        let currentValues = Array.isArray(fieldData.value)
          ? [...fieldData.value]
          : [];

        if (e.target.checked) {
          // Add the value if it's not already in the array
          if (!currentValues.includes(option.value)) {
            currentValues.push(option.value);
          }
        } else {
          // Remove the value if it's in the array
          currentValues = currentValues.filter((val) => val !== option.value);
        }

        // Update the model
        updateFieldValue(currentValues);

        // Update selected text display
        updateSelectedText();
      });

      const optionLabel = document.createElement("label");
      optionLabel.setAttribute(
        "for",
        `${eventCategory}-${eventName}-${option.id}`
      );
      optionLabel.innerText = option.value;
      if (option.field_id) {
        optionLabel.innerText += ` (${option.field_id})`;
      }

      checkboxWrapper.appendChild(input);
      checkboxWrapper.appendChild(optionLabel);
      dropdownContent.appendChild(checkboxWrapper);
    });

    // Toggle dropdown on click
    dropdownHeader.addEventListener("click", () => {
      const isOpen = dropdownContent.style.display === "block";
      dropdownContent.style.display = isOpen ? "none" : "block";
      dropdownArrow.innerHTML = isOpen ? "&#9662;" : "&#9652;";
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", (e) => {
      if (!dropdownContainer.contains(e.target)) {
        dropdownContent.style.display = "none";
        dropdownArrow.innerHTML = "&#9662;";
      }
    });

    dropdownContainer.appendChild(dropdownHeader);
    dropdownContainer.appendChild(dropdownContent);
    container.appendChild(dropdownContainer);
  } else if (fieldData.input_type === "multi_input_field") {
    // Create container for multi-input field
    const multiInputContainer = document.createElement("div");
    multiInputContainer.classList.add("multi-input-container");
    multiInputContainer.style.marginTop = "5px";

    // Create radio buttons for options
    const radioContainer = document.createElement("div");
    radioContainer.classList.add("radio-container");
    radioContainer.style.display = "flex";
    radioContainer.style.flexDirection = "row";
    radioContainer.style.flexWrap = "nowrap";
    radioContainer.style.gap = "15px";
    radioContainer.style.width = "100%";

    // Create a container for conditional content
    const conditionalContentContainer = document.createElement("div");
    conditionalContentContainer.classList.add("conditional-content-container");
    conditionalContentContainer.style.marginTop = "15px";
    conditionalContentContainer.style.padding = "15px";
    conditionalContentContainer.style.border = "1px solid #eee";
    conditionalContentContainer.style.borderRadius = "4px";
    conditionalContentContainer.style.display = "none";
    conditionalContentContainer.style.width = "100%";
    conditionalContentContainer.style.boxShadow = "0 1px 3px rgba(0,0,0,0.05)";

    // Create radio buttons for each option
    if (fieldData.options && Array.isArray(fieldData.options)) {
      fieldData.options.forEach((option) => {
        const radioWrapper = document.createElement("div");
        radioWrapper.style.display = "inline-flex";
        radioWrapper.style.alignItems = "center";
        radioWrapper.style.marginRight = "20px";
        radioWrapper.style.whiteSpace = "nowrap";

        const input = document.createElement("input");
        input.type = "radio";
        input.name = `${eventCategory}-${eventName}-multi-input`;
        input.value = option;
        input.id = `${eventCategory}-${eventName}-${option}`;
        input.checked = fieldData.value === option;

        input.addEventListener("change", (e) => {
          if (e.target.checked) {
            // Update the field value
            updateFieldValue(e.target.value);

            // Clear and render conditional content
            conditionalContentContainer.innerHTML = "";
            conditionalContentContainer.style.display = "none";

            // Check if this option has conditional content to display
            let hasConditionalContent = false;
            if (e.target.value === "Yes" && fieldData.if_yes) {
              hasConditionalContent = true;
            } else if (e.target.value === "No" && fieldData.if_no) {
              hasConditionalContent = true;
            } else if (e.target.value === "Alive" && fieldData.if_alive) {
              hasConditionalContent = true;
            } else if (e.target.value === "Deceased" && fieldData.if_deceased) {
              hasConditionalContent = true;
            }

            // Only render if there's conditional content to show
            if (hasConditionalContent) {
              renderConditionalContent(
                e.target.value,
                fieldData,
                conditionalContentContainer,
                eventCategory,
                eventName,
                patientData,
                parentPath
              );
            }
          }
        });

        const optionLabel = document.createElement("label");
        optionLabel.setAttribute(
          "for",
          `${eventCategory}-${eventName}-${option}`
        );
        optionLabel.innerText = option;
        if (fieldData.description) {
          optionLabel.setAttribute("title", fieldData.description);
        }

        radioWrapper.appendChild(input);
        radioWrapper.appendChild(optionLabel);
        radioContainer.appendChild(radioWrapper);
      });
    }

    // If a value is already selected, render its conditional content
    if (fieldData.value) {
      // Check if this option has conditional content to display
      let hasConditionalContent = false;
      if (fieldData.value === "Yes" && fieldData.if_yes) {
        hasConditionalContent = true;
      } else if (fieldData.value === "No" && fieldData.if_no) {
        hasConditionalContent = true;
      } else if (fieldData.value === "Alive" && fieldData.if_alive) {
        hasConditionalContent = true;
      } else if (fieldData.value === "Deceased" && fieldData.if_deceased) {
        hasConditionalContent = true;
      }

      // Only render if there's conditional content to show
      if (hasConditionalContent) {
        renderConditionalContent(
          fieldData.value,
          fieldData,
          conditionalContentContainer,
          eventCategory,
          eventName,
          patientData,
          parentPath
        );
      }
    }

    multiInputContainer.appendChild(radioContainer);
    multiInputContainer.appendChild(conditionalContentContainer);
    container.appendChild(multiInputContainer);
  }
}

export function renderFollowUpEvents(patientData) {
  const container = document.getElementById("followUpEvents");
  container.innerHTML = "";
  container.style.position = "relative";
  // Don't set display to grid here - let the accordion control it
  // container.style.display = "grid";
  container.style.gridTemplateColumns = "repeat(auto-fill, minmax(300px, 1fr))";
  container.style.gap = "16px";
  container.style.padding = "16px";

  // Ensure the verified field exists as an object
  if (
    typeof patientData.follow_up_events.verified !== "object" ||
    patientData.follow_up_events.verified === null
  ) {
    patientData.follow_up_events.verified = {
      value: patientData.follow_up_events.verified || "False",
      modified_by: "",
    };
  }

  // Process each top-level category in follow_up_events
  Object.entries(patientData.follow_up_events).forEach(([category, fields]) => {
    if (category === "verified") return; // Skip verified field, it's handled separately

    // Create a container for this category
    const categoryContainer = document.createElement("div");
    categoryContainer.classList.add("category-container");
    categoryContainer.style.gridColumn = "1 / -1"; // Span all columns
    categoryContainer.style.display = "grid";
    categoryContainer.style.gridTemplateColumns =
      "repeat(auto-fill, minmax(300px, 1fr))";
    categoryContainer.style.gap = "16px";

    // Create a heading for the category
    const categoryLabel = document.createElement("h2");
    categoryLabel.style.fontWeight = "bold";
    categoryLabel.style.gridColumn = "1 / -1"; // Span all columns
    categoryLabel.style.marginBottom = "16px";

    // For follow_up_event, include the field ID
    if (category === "follow_up_event" && fields.field_id) {
      categoryLabel.innerText = `${formatHeading(category)} (${
        fields.field_id
      })`;
    
    } else {
      categoryLabel.innerText = formatHeading(category);
    }

    categoryContainer.appendChild(categoryLabel);

    // Handle the nested structure for follow_up_event
    if (category === "follow_up_event" && fields.event_occurred) {
      // Process each event category (cardiovascular, systemic, etc.)
      Object.entries(fields.event_occurred).forEach(
        ([eventCategory, eventFields]) => {
          // Create a subcategory container with its own grid
          const subcategoryContainer = document.createElement("div");
          subcategoryContainer.classList.add("subcategory-container");
          subcategoryContainer.style.gridColumn = "1 / -1"; // Span all columns
          subcategoryContainer.style.display = "grid";
          subcategoryContainer.style.gridTemplateColumns =
            "repeat(auto-fill, minmax(300px, 1fr))";
          subcategoryContainer.style.gap = "16px";
          subcategoryContainer.style.marginBottom = "24px";

          // Create a heading for the subcategory
          const subcategoryLabel = document.createElement("h3");
          subcategoryLabel.style.fontWeight = "bold";
          subcategoryLabel.style.gridColumn = "1 / -1"; // Span all columns
          subcategoryLabel.style.marginTop = "16px";
          subcategoryLabel.style.marginBottom = "16px";
          subcategoryLabel.innerText = formatHeading(eventCategory);
          subcategoryContainer.appendChild(subcategoryLabel);

          // Render each event in this category (endocarditis, iatrogenic_asd, etc.)
          Object.entries(eventFields).forEach(([eventName, eventData]) => {
            renderEventField(
              eventName,
              eventData,
              eventCategory,
              subcategoryContainer,
              patientData
            );
          });

          // Add the subcategory container to the category container
          categoryContainer.appendChild(subcategoryContainer);
        }
      );
    }

    // Handle other direct fields (not follow_up_event)
    if (fields && typeof fields.label !== "undefined") {
      // Create a tile wrapper for this field
      const tileWrapper = document.createElement("div");
      tileWrapper.classList.add("tile-wrapper");
      tileWrapper.style.position = "relative";
      tileWrapper.style.marginBottom = "24px";

      // Create the field container
      const fieldContainer = document.createElement("div");
      fieldContainer.classList.add("field-container");
      fieldContainer.style.padding = "16px";
      fieldContainer.style.border = "1px solid #dee2e6";
      fieldContainer.style.borderRadius = "4px";
      fieldContainer.style.backgroundColor = "#fff";

      // Create and append the label element
      const label = document.createElement("label");
      label.classList.add("label", "cursor-pointer");
      label.textContent = `${fields.label} (${fields.field_id})`;
      if (fields.description) {
        label.setAttribute("title", fields.description);
      }
      fieldContainer.appendChild(label);

      // Create the modified_by display element
      const modifiedByDisplay = document.createElement("span");
      modifiedByDisplay.style.display = "block";
      modifiedByDisplay.style.textAlign = "right";
      modifiedByDisplay.style.marginTop = "-10px";
      modifiedByDisplay.style.color = "#8143d9";
      modifiedByDisplay.style.fontSize = "12px";
      if (!fields.modified_by) {
        fields.modified_by = "";
      }
      modifiedByDisplay.textContent = fields.modified_by;

      // Use renderSingleField to handle all input types
      renderSingleField(
        fields,
        fieldContainer,
        category,
        "", // No event name for direct fields
        patientData
      );

      // Apply initial border styling
      updateTileStyle(fieldContainer, fields.value || "");

      tileWrapper.appendChild(fieldContainer);
      tileWrapper.appendChild(modifiedByDisplay);
      categoryContainer.appendChild(tileWrapper);
    }

    // Add the category container to the main container
    container.appendChild(categoryContainer);
  });

  // Create the verified checkbox container
  const verifiedContainer = document.createElement("div");
  verifiedContainer.style.position = "absolute";
  verifiedContainer.style.bottom = "16px";
  verifiedContainer.style.right = "16px";
  verifiedContainer.style.display = "flex";
  verifiedContainer.style.alignItems = "center";
  verifiedContainer.style.backgroundColor = "white";
  verifiedContainer.style.padding = "8px 16px";
  verifiedContainer.style.borderRadius = "4px";
  verifiedContainer.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";

  // Create the verified checkbox
  const containerVerifiedCheckbox = document.createElement("input");
  containerVerifiedCheckbox.type = "checkbox";
  containerVerifiedCheckbox.id = "follow-up-events-verified-checkbox";
  containerVerifiedCheckbox.checked =
    patientData.follow_up_events.verified.value === "True";
  containerVerifiedCheckbox.style.width = "24px";
  containerVerifiedCheckbox.style.height = "24px";
  containerVerifiedCheckbox.style.marginRight = "8px";

  // Add event listener to the verified checkbox
  containerVerifiedCheckbox.addEventListener("change", (e) => {
    patientData.follow_up_events.verified.value = e.target.checked
      ? "True"
      : "False";
    patientData.follow_up_events.verified.modified_by = "ABSTRACTOR";
    updateContainerHighlight(container, patientData.follow_up_events);
  });

  // Create the verified label
  const containerVerifiedLabel = document.createElement("label");
  containerVerifiedLabel.setAttribute(
    "for",
    "follow-up-events-verified-checkbox"
  );
  containerVerifiedLabel.innerText = "Verified";
  containerVerifiedLabel.style.fontSize = "18px";
  containerVerifiedLabel.style.fontWeight = "bold";
  containerVerifiedLabel.style.margin = "0";

  // Add the checkbox and label to the verified container
  verifiedContainer.appendChild(containerVerifiedCheckbox);
  verifiedContainer.appendChild(containerVerifiedLabel);

  // Add the verified container to the main container
  container.appendChild(verifiedContainer);

  // Apply initial container highlight
  updateContainerHighlight(container, patientData.follow_up_events);
}
