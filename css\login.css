.shake {
  animation: shake 0.3s;
  animation-iteration-count: 2;
}

/* Define the shake animation */
@keyframes shake {
  0% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  50% {
    transform: translateX(5px);
  }

  75% {
    transform: translateX(-5px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loader {
  border: 4px solid rgba(143, 88, 221, 1);
  border-top: 4px solid #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

.animate-spin-custom {
  animation: spin 2s linear infinite;
}

.main-techs-container {
  position: absolute;
  left: -256px;
  width: 256px; /* Reduced from 300px to match the left offset */
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: left 0.1s ease;
  overflow: hidden; /* Prevent content from overflowing */
}

.main-techs-container.open {
  left: 0;
}

/* Hide the original pseudo-element since we're using a real element now */
.main-techs-container::before {
  display: none;
}

/* Make toggle buttons more clickable in mobile view */
[class*="toggle-button"] {
  position: relative;
  z-index: 20; /* Higher z-index to ensure clickability */
  cursor: pointer;
}

/* Ensure the toggle button's image is also clickable */
[class*="toggle-button"] img {
  pointer-events: none; /* Make clicks pass through to the parent button */
}

/* Touch gesture indicator */
.sidebar-touch-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  z-index: 999;
  background-color: transparent;
}

/* Add visual feedback when dragging */
.main-techs-container.dragging {
  transition: none; /* Disable transition during drag for immediate feedback */
  box-shadow: 0 0 15px rgba(143, 88, 221, 0.5); /* Add shadow for visual feedback */
}

@media (min-width: 768px) {
  .main-techs-container {
    position: static;
    width: 600px; /* Drastically increased to ensure Implanting Physician text is fully visible */
    height: auto;
    left: auto;
    transition: none;
  }

  .main-techs-container::before {
    display: none;
  }
}
